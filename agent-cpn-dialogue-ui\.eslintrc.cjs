module.exports = {
    root: true,
    env: {
        browser: true,
        es2021: true,
        node: true,
    },
    extends: [
        'eslint:recommended',
        'plugin:vue/essential',
        'plugin:prettier/recommended',
        './src/types/.eslintrc-auto-import.json',
    ],
    parser: 'vue-eslint-parser',
    parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
    },
    plugins: ['vue', 'prettier'],
    rules: {
        'prettier/prettier': 'error',
        'vue/multi-word-component-names': 'off',
        'vue/no-v-html': 'off',
        'vue/html-closing-bracket-newline': 'off',
        'vue/max-attributes-per-line': 'off',
        'no-console': 'off',
        'no-debugger': 'warn',
        'no-undef': 'error',
        'no-empty': ['warn', { allowEmptyCatch: true }],
        indent: 'off',
        'key-spacing': ['error', { beforeColon: false, afterColon: true }],
        'object-curly-spacing': ['error', 'always'],
        'no-unused-vars': 'warn',
    },
};

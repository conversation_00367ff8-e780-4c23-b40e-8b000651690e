{"name": "saber", "version": "3.2.0", "type": "module", "scripts": {"dev": "vite --host", "dev:test": "vite --host --mode test", "build": "vite build", "build:dev": " vite build  --mode development", "build:prod": "vite build --mode production", "serve": "vite preview --host", "lint": "eslint --ext .js,.jsx,.vue,.ts,.tsx ./src", "lint:fix": "eslint --ext .js,.jsx,.vue,.ts,.tsx --fix ./", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,vue,less,scss,json}\"", "_comment": "//preview 本地预览生产环境构建后的项目", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.0.9", "@microsoft/fetch-event-source": "^2.0.1", "@saber/nf-design-base-elp": "^1.2.0", "@smallwei/avue": "^3.2.19", "@stomp/stompjs": "^7.1.1", "@vueuse/core": "^13.4.0", "animate.css": "^4.1.1", "avue-plugin-ueditor": "^1.0.3", "axios": "^0.21.1", "crypto-js": "^4.1.1", "dayjs": "^1.10.6", "echarts": "^5.6.0", "element-plus": "^2.3.1", "js-base64": "^3.7.4", "js-cookie": "^3.0.0", "js-md5": "^0.7.3", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.4.1", "shikiji": "^0.10.2", "sm-crypto": "^0.3.13", "sockjs-client": "^1.6.1", "stompjs": "^2.3.3", "update": "^0.7.4", "vue": "^3.5.13", "vue-element-plus-x": "1.3.2", "vue-i18n": "^9.1.9", "vue-router": "^4.2.4", "vuex": "^4.0.2"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/compiler-sfc": "^3.0.5", "babel-eslint": "^10.1.0", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-vue": "^10.0.1", "less": "^4.2.0", "less-loader": "^11.1.3", "mockjs": "^1.1.0", "postcss-pxtorem": "^6.1.0", "prettier": "^3.5.3", "prettier-eslint": "^16.4.1", "sass": "^1.69.5", "typescript": "^5.8.3", "unocss": "^66.1.4", "unplugin-auto-import": "^0.11.2", "unplugin-vue-components": "^28.5.0", "vite": "^4.5.14", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-mock": "^2.9.4", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^10.1.3"}}
export default {
  plugins: {
    'postcss-pxtorem': {
      rootValue: 16, // 根元素字体大小，通常为 16px
      unitPrecision: 5, // rem 的小数点位数
      propList: ['*'], // 需要转换的属性，* 表示所有
      selectorBlackList: [
        '.no-rem', // 不转换的类名
        /^\.el-/, // Element Plus 组件不转换
        /^\.avue-/, // Avue 组件不转换
        /^\.uno-/, // UnoCSS 生成的类不转换
        /^\.n-/, // Naive UI 组件不转换（如果有的话）
        /^\.ant-/, // Ant Design 组件不转换（如果有的话）
      ],
      replace: true, // 替换而不是添加备用属性
      mediaQuery: false, // 允许在媒体查询中转换px
      minPixelValue: 1, // 最小转换像素值
      exclude: /node_modules/i, // 排除 node_modules
    },
  },
}

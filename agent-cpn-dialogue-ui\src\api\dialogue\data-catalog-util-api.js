import request from '@/axios';
const catalogService = '/data-component-catalog';
//数据库列表查询
export const selectClassification = params =>
    request({
        url: catalogService + '/classification/tree',
        method: 'get',
        params: params,
    });
export const selectClassificationDetail = params =>
    request({
        url: catalogService + '/classification/detail',
        method: 'get',
        params: params,
    });

export const selectAppSystem = params =>
    request({
        url: catalogService + '/appSystem/select',
        method: 'get',
        params: params,
    });
export const selectAppSystemDetail = params =>
    request({
        url: catalogService + '/appSystem/detail',
        method: 'get',
        params: params,
    });

export const selectDatabaseApi = params =>
    request({
        url: catalogService + '/database/select',
        method: 'get',
        params: params,
    });

export const selectDatabaseDetail = async params =>
    await request({
        url: catalogService + '/database/detail',
        method: 'get',
        params: params,
    });

export const listTableBySource = async params =>
    await request({
        url: catalogService + '/database/listTableBySource',
        method: 'get',
        params: params,
    });

export const selectFileSystemList = async params =>
    await request({
        url: catalogService + '/fileServer/select',
        method: 'get',
        params: params,
    });
export const selectFileSystemDetail = async params =>
    await request({
        url: catalogService + '/fileServer/detail',
        method: 'get',
        params: params,
    });

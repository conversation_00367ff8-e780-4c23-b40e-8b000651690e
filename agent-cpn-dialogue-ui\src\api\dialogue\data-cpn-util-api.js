import request from '@/axios';

const utilService = '/data-component-util';
//数据库列表查询
export const selectDictByDB = params =>
    request({
        url: utilService + '/database/list',
        method: 'get',
        params: params,
    });

/* 获取元数据的表 */
export const getTables = unid => {
    return request({
        url: utilService + '/database/getTables',
        method: 'get',
        params: {
            unid,
        },
    });
};
/* 获取数据表的字段 */
export const getColumns = (unid, tableName) => {
    return request({
        url: utilService + '/database/getColumns',
        method: 'get',
        params: {
            unid,
            tableName,
        },
    });
};

import request from '@/axios';
export const reply = async res => {
    const { data, code } = await request({
        url: '/data-component-dialogue/noauth/dialogue/reply',
        method: 'post',
        data: res,
    });
    return data;
};

export const replySync = res => {
    return request({
        url: '/data-component-dialogue/noauth/dialogue/reply',
        method: 'post',
        data: res,
    });
};

export const generateDialogueMsg = res => {
    return request({
        url: '/data-component-dialogue/noauth/dialogue/generateDialogueMsg',
        method: 'post',
        data: res,
    });
};
export const uploadFile = async res => {
    const { data, code } = await request({
        url: '/data-component-dialogue/noauth/data/file/upload',
        method: 'post',
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        meta: {
            isSerialize: false,
        },
        data: res,
    });
    return data;
};
// 会话详情
export const getSessionList = async res => {
    const {
        data: { data },
        code,
    } = await request({
        url: '/data-component-dialogue/history/dialogue',
        method: 'get',
        params: res,
    });
    return data;
};
// 历史记录列表
export const getHistoryList = async res => {
    const {
        data: { data },
        code,
    } = await request({
        url: '/data-component-dialogue/history/list',
        method: 'get',
        params: res,
    });
    return { data, code };
};

// 修改历史记录
export const updateHistory = async res => {
    const {
        data: { data },
        code,
    } = await request({
        url: '/data-component-dialogue/history/update',
        method: 'post',
        data: res,
    });
    return { data, code };
};

// 删除历史记录
export const deleteHistory = async res => {
    const {
        data: { data },
        code,
    } = await request({
        url: '/data-component-dialogue/history/delete',
        method: 'get',
        params: res,
    });
    return { data, code };
};

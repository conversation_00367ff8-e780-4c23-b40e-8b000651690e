import { useUserStore } from '@/components/dialogue/store/modules/user';
import { storeToRefs } from 'pinia';
import { useIntervalFn } from '@vueuse/core';
import { getStore, setStore } from '@/utils/store';
import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import { getToken, setToken } from '@/utils/auth';

/**
 * @description token自动无感刷新
 */
export function useTokenAutoRefresh() {
    const userStore = useUserStore();
    const { token } = storeToRefs(userStore);

    // 检查token是否快过期
    const checkAndRefresh = async () => {
        try {
            // 获取当前token
            const currentToken = getToken();
            if (!currentToken || !userStore.token) {
                return;
            }

            // 如果正在刷新，跳过本次检查
            if (userStore.isRefreshing) {
                console.log('Token正在刷新中，跳过本次检查');
                return;
            }

            // 获取token存储的时间戳
            const tokenData = getStore({ name: 'token' }) || {};
            if (!tokenData || !tokenData.datetime) {
                // 如果没有时间戳，设置当前时间
                setStore({
                    name: 'token',
                    content: {
                        token: currentToken,
                        datetime: dayjs().format(),
                    },
                });
                return;
            }

            const lastTime = dayjs(tokenData.datetime);
            const now = dayjs();
            const diff = now.diff(lastTime, 'seconds');

            // 根据实际token有效期调整阈值
            const TOKEN_EXPIRE_SECONDS = 60 * 60 * 2; // 2小时（根据实际token有效期调整）
            const REFRESH_THRESHOLD = 60 * 10; // 剩10分钟自动刷新

            if (diff >= TOKEN_EXPIRE_SECONDS - REFRESH_THRESHOLD) {
                console.log('Token即将过期，开始自动刷新...');

                if (typeof userStore.refreshToken === 'function') {
                    await userStore.refreshToken();
                    console.log('Token自动刷新成功');
                }
            }
        } catch (error) {
            console.error('Token自动刷新失败:', error);

            // 根据错误类型决定处理方式
            if (error.message?.includes('无refresh_token') || error.response?.status === 401) {
                // 无效的refresh_token，清除用户信息并跳转登录页
                userStore.clearToken();
                userStore.clearUserInfo();
                window.location.href = '/login';
            } else {
                // 网络错误等，可以重试，不立即跳转登录页
                console.log('Token刷新遇到网络错误，将在下次检查时重试');
            }
        }
    };

    // 每30秒检测一次（更频繁的检测）
    useIntervalFn(checkAndRefresh, 30 * 1000, { immediate: true });
}

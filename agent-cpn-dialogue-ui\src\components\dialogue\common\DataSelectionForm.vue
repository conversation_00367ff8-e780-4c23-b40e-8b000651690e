<template>
    <div class="data-selection-form">
        <div class="selection-area">
            <div class="selection-text">
                <template v-for="(part, index) in parsedTemplate" :key="index">
                    <span v-if="part.type === 'text'">{{ part.content }}</span>
                    <span v-else-if="part.type === 'select'" class="select-wrapper">
                        <el-select v-model="formValues[part.field]" class="blue-select">
                            <template #prefix>
                                <span class="select-text" :class="{ selected: formValues[part.field] }">
                                    【
                                    {{
                                        formValues[part.field]
                                            ? getLabelByField(part.field, formValues[part.field])
                                            : part.placeholder
                                    }}】
                                </span>
                            </template>
                            <el-option
                                v-for="item in getOptionsByField(part.field)"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </span>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup name="DataSelectionForm">
import { ref, computed, onMounted } from 'vue';

const props = defineProps({
    /**
     * 模板字符串，使用 {field:placeholder} 语法表示下拉选择框
     */
    template: {
        type: String,
        default:
            '帮我将{source:源名}数据源，{db:库名}数据库，{table:表名}数据表的数据以{format:全量}的方式汇聚到{targetSource:源名}数据源，{targetDb:库名}数据库，{targetTable:表名}数据表。',
    },
});

// 表单值
const formValues = ref({
    source: '',
    db: '',
    table: '',
    format: '',
    targetSource: '',
    targetDb: '',
    targetTable: '',
});

// 解析模板
const parsedTemplate = computed(() => {
    const result = [];
    let lastIndex = 0;
    const regex = /{([^:]+):([^}]+)}/g;
    let match;

    while ((match = regex.exec(props.template)) !== null) {
        // 添加前面的文本
        if (match.index > lastIndex) {
            result.push({
                type: 'text',
                content: props.template.substring(lastIndex, match.index),
            });
        }

        // 添加选择框
        result.push({
            type: 'select',
            field: match[1],
            placeholder: match[2],
        });

        lastIndex = match.index + match[0].length;
    }

    // 添加剩余的文本
    if (lastIndex < props.template.length) {
        result.push({
            type: 'text',
            content: props.template.substring(lastIndex),
        });
    }

    return result;
});

/**
 * 数据源选项
 * @type {import('vue').Ref<Array<{value: string, label: string}>>}
 */
const sourceOptions = ref([
    { value: 'mysql1', label: 'MySQL数据源1' },
    { value: 'mysql2', label: 'MySQL数据源2' },
    { value: 'oracle1', label: 'Oracle数据源' },
]);

/**
 * 数据库选项
 * @type {import('vue').Ref<Array<{value: string, label: string}>>}
 */
const dbOptions = ref([
    { value: 'db1', label: '业务数据库' },
    { value: 'db2', label: '用户数据库' },
    { value: 'db3', label: '订单数据库' },
]);

/**
 * 数据表选项
 * @type {import('vue').Ref<Array<{value: string, label: string}>>}
 */
const tableOptions = ref([
    { value: 'table1', label: '用户表' },
    { value: 'table2', label: '订单表' },
    { value: 'table3', label: '商品表' },
]);

/**
 * 数据采集方式选项
 * @type {import('vue').Ref<Array<{value: string, label: string}>>}
 */
const formatOptions = ref([
    { value: 'full', label: '全量' },
    { value: 'increment', label: '增量' },
    { value: 'snapshot', label: '快照' },
]);

/**
 * 目标数据源选项
 * @type {import('vue').Ref<Array<{value: string, label: string}>>}
 */
const targetSourceOptions = ref([
    { value: 'es1', label: 'Elasticsearch' },
    { value: 'mongo1', label: 'MongoDB' },
    { value: 'mysql3', label: 'MySQL目标库' },
]);

/**
 * 目标数据库选项
 * @type {import('vue').Ref<Array<{value: string, label: string}>>}
 */
const targetDbOptions = ref([
    { value: 'target_db1', label: '目标业务库' },
    { value: 'target_db2', label: '目标分析库' },
    { value: 'target_db3', label: '目标备份库' },
]);

/**
 * 目标数据表选项
 * @type {import('vue').Ref<Array<{value: string, label: string}>>}
 */
const targetTableOptions = ref([
    { value: 'target_table1', label: '目标用户表' },
    { value: 'target_table2', label: '目标订单表' },
    { value: 'target_table3', label: '目标日志表' },
]);

/**
 * 根据字段获取选项列表
 * @param {string} field - 字段名称
 * @returns {Array<{value: string, label: string}>} - 选项列表
 */
const getOptionsByField = field => {
    const optionsMap = {
        source: sourceOptions.value,
        db: dbOptions.value,
        table: tableOptions.value,
        format: formatOptions.value,
        targetSource: targetSourceOptions.value,
        targetDb: targetDbOptions.value,
        targetTable: targetTableOptions.value,
    };
    return optionsMap[field] || [];
};

/**
 * 根据字段和值获取标签
 * @param {string} field - 字段名称
 * @param {string} value - 字段值
 * @returns {string} - 标签文本
 */
const getLabelByField = (field, value) => {
    const options = getOptionsByField(field);
    const option = options.find(item => item.value === value);
    return option ? option.label : value;
};

/**
 * 表单验证计算属性
 * @type {import('vue').ComputedRef<boolean>}
 */
const isFormValid = computed(() => {
    // 检查所有选择框是否都有值
    return parsedTemplate.value.filter(part => part.type === 'select').every(part => !!formValues.value[part.field]);
});

/**
 * 获取表单数据
 * @returns {Object} - 表单数据
 */
const getFormData = () => {
    return { ...formValues.value };
};

// 暴露给父组件的属性和方法
defineExpose({
    isFormValid,
    getFormData,
});
</script>

<style scoped lang="scss">
.data-selection-form {
    .selection-area {
        line-height: 2;
        font-size: 14px;
        padding: 15px;
        border-radius: 4px;
        background: #f4f9fe;

        .selection-text {
            color: #333;
        }

        .select-wrapper {
            position: relative;
            display: inline-block;
            margin: 0 4px;

            .select-text {
                color: #1570ef;
                line-height: 18px;
            }

            .blue-select {
                :deep(.el-input__wrapper) {
                    box-shadow: none !important;
                    background-color: transparent;
                    padding: 0;
                    cursor: pointer;
                    width: auto;
                }

                :deep(.el-input__inner) {
                    display: none;
                }

                :deep(.el-select__caret) {
                    display: none;
                }
            }
        }
    }
}
</style>

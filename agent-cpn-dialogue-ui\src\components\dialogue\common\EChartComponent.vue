<template>
    <div
        ref="chartRef"
        :style="{
            width: width,
            height: height,
        }"
        class="echart-component"
    ></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import * as echarts from 'echarts';

defineOptions({
    name: 'EChartComponent',
});

const props = defineProps({
    // 图表配置项
    option: {
        type: Object,
        required: true,
    },
    // 图表宽度
    width: {
        type: String,
        default: '100%',
    },
    // 图表高度
    height: {
        type: String,
        default: '300px',
    },
    // 是否自动调整大小
    autoResize: {
        type: Boolean,
        default: true,
    },
    // 主题
    theme: {
        type: String,
        default: '',
    },
});

const emit = defineEmits(['chartReady', 'chartClick']);

const chartRef = ref(null);
let chartInstance = null;

/**
 * @description 初始化图表
 */
const initChart = () => {
    if (!chartRef.value) return;

    // 销毁之前的实例
    if (chartInstance) {
        chartInstance.dispose();
    }

    // 创建图表实例
    chartInstance = echarts.init(chartRef.value, props.theme);

    // 设置配置项
    if (props.option) {
        chartInstance.setOption(props.option);
    }

    // 注册点击事件
    chartInstance.on('click', params => {
        emit('chartClick', params);
    });

    // 通知图表已准备好
    emit('chartReady', chartInstance);
};

/**
 * @description 更新图表大小
 */
const resizeChart = () => {
    if (chartInstance) {
        chartInstance.resize();
    }
};

/**
 * @description 更新图表配置
 */
const updateChart = () => {
    if (chartInstance && props.option) {
        chartInstance.setOption(props.option, true);
    }
};

// 监听配置变化
watch(
    () => props.option,
    () => {
        nextTick(() => {
            updateChart();
        });
    },
    { deep: true }
);

// 监听主题变化
watch(
    () => props.theme,
    () => {
        nextTick(() => {
            initChart();
        });
    }
);

// 组件挂载时初始化图表
onMounted(() => {
    nextTick(() => {
        initChart();

        // 添加窗口大小变化监听
        if (props.autoResize) {
            window.addEventListener('resize', resizeChart);
        }
    });
});

// 组件卸载前销毁图表实例和事件监听
onBeforeUnmount(() => {
    if (chartInstance) {
        chartInstance.dispose();
        chartInstance = null;
    }

    if (props.autoResize) {
        window.removeEventListener('resize', resizeChart);
    }
});

// 暴露方法
defineExpose({
    getChartInstance: () => chartInstance,
    resize: resizeChart,
    update: updateChart,
});
</script>

<style scoped lang="scss">
.echart-component {
    box-sizing: border-box;
}
</style>

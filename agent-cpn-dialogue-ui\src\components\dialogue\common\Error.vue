<template>
    <div class="thinking-content-step">
        <div class="top-container">
            <div class="icon-wrapper">
                <el-icon class="step-icon"><Check /></el-icon>
            </div>
            <div class="content-title" @click="toggleCollapse">
                <span class="content-title-text">{{ finalStepTitle }}</span>
                <el-icon v-if="isShowExpand" class="collapse-icon" :class="{ 'is-collapsed': isCollapsed }">
                    <ArrowDown />
                </el-icon>
            </div>
        </div>

        <div class="step-content">
            <div></div>
            <div class="extra-text">{{ data.message || data.result.message }}</div>
            <div class="action-buttons">
                <a @click="retry">
                    <img :src="publicPath + `/static/dialogue/sx.png`" />
                    重新输入
                </a>
            </div>
        </div>
    </div>
</template>

<script setup>
defineOptions({
    name: 'ThinkingContentStep',
});
import { publicPath } from '@/components/dialogue/store/main.js';
import { Check, ArrowDown } from '@element-plus/icons-vue';
/**
 * @description 思维链步骤组件
 */
import { ref, computed } from 'vue';
import { useSessionStore } from '@/components/dialogue/store/modules/session';
import { useDialogueHandler } from '@/components/dialogue/hooks/useDialogueHandler';

const sessionStore = useSessionStore();
import { senderValue } from '@/components/dialogue/store/input.js';
const props = defineProps({
    /**
     * @description 是否显示连接线
     */
    lastStep: {
        type: Boolean,
        default: false,
    },
    stepTitle: {
        type: String,
        default: '提示',
    },
    data: {
        type: Object,
        default: () => ({ code: 400, success: false, msg: '解析失败' }),
    },
    isError: {
        type: Boolean,
        default: true,
    },
    isShowExpand: {
        type: Boolean,
        default: false,
    },
    extraText: {
        type: String,
        default: '',
    },
    /**
     * @description 是否默认折叠
     */
    defaultCollapsed: {
        type: Boolean,
        default: false,
    },
});

const isCollapsed = ref(props.defaultCollapsed);
const finalExtraText = computed(() => props.data?.extraText ?? props.extraText);
const finalStepTitle = computed(() => props.data?.stepTitle ?? props.stepTitle);
const showLine = computed(() => ((props.data?.lastStep ?? props.lastStep) ? true : false));

/**
 * @description 切换折叠状态
 */
const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value;
};

/**
 * @description 重新输入功能，获取最后一条用户输入的内容
 */
const retry = () => {
    // 获取messageList中最后一条用户输入的内容
    const userMessages = sessionStore.messageList.filter(item => item.isUser);

    if (userMessages.length > 0) {
        // 获取最后一条用户消息
        const lastUserMessage = userMessages[userMessages.length - 1];
        const lastUserInput = lastUserMessage.question;

        // 最后一条用户输入
        if (lastUserInput) {
            senderValue.value = lastUserInput;
        }
    }
};
</script>

<style scoped lang="scss">
.thinking-content-step {
    display: flex;
    align-items: flex-start;
    position: relative;
    flex-direction: column;
    margin-left: 6px;
    .top-container {
        display: flex;
        align-items: center;
        margin-right: 12px;
        position: relative;
    }

    .icon-wrapper {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #ef6820;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1;
        margin-top: 2px;
        .step-icon {
            color: #fff;
            font-size: 14px;
        }
    }
    .action-buttons {
        display: flex;
        align-items: center;
        gap: 10px;
        background: #f4f9fe;
        padding: 9px;
        border-radius: 8px;
        a {
            color: #409eff;
            cursor: pointer;
            display: flex;
            align-items: center;
            white-space: nowrap;

            img,
            i {
                margin-right: 5px;
            }
        }
    }
    .step-content {
        padding: 0 16px;
        color: #475467;
        width: 100%;
        display: flex;
        font-size: 14px;
        align-items: center;
        justify-content: space-between;
        margin: 0;
        font-weight: normal;
        &::after {
            content: '';
            position: absolute;
            top: 10px;
            left: 7px;
            height: 100%;
            border: 1px dashed #ef6820;
            z-index: v-bind('showLine ? -1 : 0');
        }
    }

    .content-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-left: 10px;
        cursor: pointer;
        color: #ef6820;
        font-weight: 500;
        background-color: #fef6ee;
        border-radius: 4px;
        padding: 16px;
        height: 30px;
        font-size: 16px;
        .collapse-icon {
            font-size: 14px;
            transition: transform 0.3s ease;
            margin-left: 8px;
            color: #98a2b3;

            &.is-collapsed {
                transform: rotate(-180deg);
            }
        }
    }
    .extra-text {
        padding: 2px 2px;
    }
}
</style>

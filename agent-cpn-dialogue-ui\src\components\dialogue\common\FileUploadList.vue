<template>
    <Attachments
        :file-list="files"
        :http-request="handleHttpRequest"
        :before-upload="handleBeforUpload"
        :hide-upload="false"
        drag
        overflow="wrap"
        :limit="limit"
        :items="files"
        @upload-drop="handleUploadDrop"
        @delete-card="handleDeleteCard"
    >
        <template #empty-upload v-if="!autoUpload">
            <div class="h-20 flex items-center justify-center">
                <el-empty description="请选择模板文件" :image-size="40" />
            </div>
        </template>
    </Attachments>
</template>

<script setup>
/**
 * @description 文件上传列表公共组件，支持文件拖拽上传、删除等功能
 */
defineOptions({
    name: 'FileUploadList',
});

import { files, fileListUrl } from '@/components/dialogue/store/input.js';
import { ElMessage } from 'element-plus';
import { isFontTest } from '@/components/dialogue/store/main';
import { uploadFile } from '@/api/dialogue/index.js';

const props = defineProps({
    /**
     * 文件列表
     */
    fileList: {
        type: Array,
        required: true,
    },
    autoUpload: {
        type: Boolean,
        default: true,
    },
    /**
     * 上传请求方法
     */
    httpRequest: {
        type: Function,
        required: true,
    },
    /**
     * 上传前校验方法
     */
    beforeUpload: {
        type: Function,
        default: undefined,
    },
    /**
     * 是否隐藏上传按钮
     */
    hideUpload: {
        type: Boolean,
        default: false,
    },
    /**
     * 上传文件数量限制
     */
    limit: {
        type: Number,
        default: 1,
    },
    /**
     * 标题
     */
    title: {
        type: String,
        default: '上传文件列表',
    },
    /**
     * 删除文件方法（可选，父组件传入时优先使用）
     */
    deleteCard: {
        type: Function,
        default: undefined,
    },
});
const emit = defineEmits(['delete-card']);
const fileList = computed(() => props.fileList || files.value);

async function handleHttpRequest(options) {
    // console.log('🚀 ~ SenderHeader.vue:122 ~ handleHttpRequest ~ options:', options);
    const formData = new FormData();
    formData.append('file', options.file);
    ElMessage.info('上传中...');
    // console.log('调试: 开始处理文件上传请求', options.file);

    // 获取文件类型
    const fileType = getFileTypeFromName(options.file.name);

    try {
        // 调用API上传文件
        let res = 1;
        if (!isFontTest) {
            const { data } = await uploadFile(formData);
            res = data;
        }
        // 设置文件URL，供发送消息时使用
        fileListUrl.value = res;
        if (res) {
            const fileUrl = res || '';
            // console.log('调试: 文件上传URL', fileUrl);

            files.value.push({
                id: files.value.length,
                uid: options.file.uid,
                name: options.file.name,
                fileSize: options.file.size,
                fileType: fileType,
                imgFile: options.file,
                showDelIcon: true,
                imgVariant: 'square',
                // 保存服务器返回的文件信息
                serverFileInfo: res,
                fileUrl: fileUrl,
                url: res,
            });
            ElMessage.success('上传成功: ');
        } else {
            console.error('调试: 上传失败，返回结果为空');
            ElMessage.error('上传失败');
        }
    } catch (error) {
        console.error('文件上传失败:', error);
        ElMessage.error('文件上传失败: ' + (error.message || '未知错误'));
    }
}

// 文件上传
function handleBeforUpload(file) {
    // console.log('调试: 文件上传检查', file);
    if (file.size > 1024 * 1024 * 2) {
        ElMessage.error('文件大小不能超过 2MB!');
        return false;
    }
}

async function handleUploadDrop(files, props) {
    // console.log('调试: 拖放上传文件', files);
    // console.log('调试: 拖放上传属性', props);

    if (files && files.length > 0) {
        if (files[0].type === '') {
            ElMessage.error('禁止上传文件夹！');
            return false;
        }

        for (let index = 0; index < files.length; index++) {
            const file = files[index];
            await handleHttpRequest({ file });
        }
    }
}

/**
 * 从文件名推断文件类型
 * @param {String} fileName - 文件名
 * @returns {String} 文件类型
 */
function getFileTypeFromName(fileName) {
    if (!fileName) return 'file';

    const extension = fileName.split('.').pop().toLowerCase();

    const typeMap = {
        txt: 'txt',
        doc: 'word',
        docx: 'word',
        xls: 'excel',
        xlsx: 'excel',
        ppt: 'ppt',
        pptx: 'ppt',
        pdf: 'pdf',
        jpg: 'image',
        jpeg: 'image',
        png: 'image',
        gif: 'image',
        mp3: 'audio',
        wav: 'audio',
        mp4: 'video',
        mov: 'video',
        zip: 'zip',
        rar: 'zip',
        '7z': 'zip',
        md: 'mark',
    };

    return typeMap[extension] || 'file';
}
function handleDeleteCard(item) {
    console.log('调试: 开始', files.value);

    files.value = files.value.filter(items => items.uid !== item.uid);
    console.log('调试:赋值', files.value);

    if (props.deleteCard && typeof props.deleteCard === 'function') {
        props.deleteCard(item);
    } else {
        if (files.value.length === 0) {
            // closeHeader();
        }
    }

    ElMessage.success('删除成功');
}
</script>

<style scoped lang="scss">
.file-header {
    padding: 0 10px;
}
</style>

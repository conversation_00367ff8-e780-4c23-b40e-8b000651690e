<template>
    <common-thinkingContentStep :stepTitle="stepTitle">
        <template #content>
            <div class="extra-text mb-2">
                {{ props.data?.result.extraText || '以下是XXX结果文件，请下载查看' }}
            </div>
            <div class="file-card-wrapper" v-for="file in fileList" :key="file.uid">
                <FilesCard
                    :uid="file.uid"
                    :name="file.name"
                    :file-type="file.fileType"
                    :file-url="file.fileUrl"
                    :description="file.fileType"
                    class="file-card"
                    :file-size="file.fileSize"
                    @click="props.isShowPreview ? clickFilePreview(file) : null"
                    :hover-style="{
                        'box-shadow': '0 2px 12px 0 rgba(0, 0, 0, 0.1)',
                        'border-color': 'red',
                        'background-color': 'rgba(255, 0, 0, 0.1)',
                        cursor: 'pointer',
                    }"
                ></FilesCard>
                <div class="download-icon" @click.stop="downloadFile(file)">
                    <el-tooltip content="下载文件" placement="top">
                        <el-icon><Download /></el-icon>
                    </el-tooltip>
                </div>
            </div>
        </template>
    </common-thinkingContentStep>
</template>

<script setup>
import { Download } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

const { setRightPanel } = useRightPanelStore();
import eventBus from '@/utils/bus';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
// 文件列表
const fileList = ref();
const stepTitle = ref('指标数据导入结果');
const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            result: {
                linkResult:
                    'http://*************:9000/data-agent/upload/20250624/8e5b9bfb4cdad9e61d1846e5d41abfa5.xlsx',
                fileName: '模板文件',
                fileType: 'doc',
                // fileType: 'xlsx',
                fileSize: 11680,
                mdResult:
                    '| 责任部门编码 | 责任部门 | 项目状态 | 是否重点项目 | 项目数 |\n|------|------|------|------|------|\n| 龙田街道 | 龙田街道 | 续建 | 1 | 1\n| 国资局 | 国资局 | 续建 | 1 | 7\n| 规自局 | 规自局 | 续建 | 1 | 15\n| 供电局 | 供电局 | 续建 | 1 | 3\n| 公安局 | 公安局 | 续建 | 1 | 1\n| default | default | 新开工 | 1 | 79\n| 教育局 | 教育局 | 新开工 | 1 | 1\n| 工信局 | 工信局 | 新开工 | 1 | 23\n| 水务局 | 水务局 | 新开工 | 1 | 3\n|',
                message: '模板表格解析成功',
                status: '成功',
                databaseInfo: '龙田街道',
                querySql: 'select * from table where name = "龙田街道"',
                stepTitle: '指标数据导入结果',
            },
        }),
    },
    // stepTitle: {
    //     type: String,
    //     default: '指标数据导入结果',
    // },
    // 是否需要点击右侧预览文件
    isShowPreview: {
        type: Boolean,
        default: true,
    },
});
// 定义组件选项
defineOptions({
    name: 'DataAskExportTable',
});
// 右侧展开时候文件预览的额外按钮事件
const handleThreeIconFn = () => {
    let dataList = fileList.value;
    dataList = fileList.value.map(item => {
        return {
            ...item,
            name: item.databaseInfo,
        };
    });
    console.log('dataList', dataList);
    setRightPanel(
        'RightEditTablePreview',
        {
            title: '表格导数结果',
            showThreeIcon: true,
            icons: [
                {
                    name: 'bianji',
                    tooltip: '编辑',
                    handler: handleEdit,
                },
            ],
            data: dataList,
        },
        {
            cancel: clickFilePreview,
        }
    );
};
const handleEdit = val => {
    // 发送点击事件
    eventBus.emit('edit');
};
// 处理文件卡片点击
const clickFilePreview = file => {
    if (stepTitle.value.startsWith('指标')) {
        setRightPanel('TablePreview', {
            markdownContent: fileList.value[0].mdResult,
            threeIconTooltip: '编辑',
            cancel: clickFilePreview,
            fileName: file.name,
            title: file.fileName,
            icons: [
                {
                    name: 'zhankai',
                    tooltip: '编辑',
                    handler: handleThreeIconFn,
                },
            ],
        });
    }
    if (stepTitle.value.startsWith('报告')) {
        console.log('点击文件预览', props.data);
        setRightPanel('RightAskDatabaogao', {
            data: props.data,
            icons: [
                {
                    name: 'tree-shousuo',
                    tooltip: '缩略图',
                },
            ],
            title: '模板文件',
            showChart: true,
        });
    }
};

/**
 * @description 下载文件
 * @param {Object} file - 文件对象
 */
const downloadFile = file => {
    // 使用传入的文件URL或文件对象中的URL
    const fileUrl = props.fileUrl || file.fileUrl;

    if (!fileUrl) {
        ElMessage.error('文件下载链接不存在');
        return;
    }

    try {
        // 创建一个a标签用于下载
        const link = document.createElement('a');
        1;
        link.href = fileUrl;
        link.download = file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        ElMessage.success('文件下载开始');
    } catch (error) {
        console.error('文件下载失败:', error);
        ElMessage.error('文件下载失败: ' + (error.message || '未知错误'));
    }
};

watchEffect(() => {
    const data = props.data?.result || [];
    stepTitle.value = data.stepTitle ? data.stepTitle : '指标数据导入结果';
    fileList.value = [
        {
            uid: data.fileName,
            fileName: '模版文件',
            name: data.fileName + '.' + data.fileType,
            fileSize: data.fileSize,
            fileUrl: data.linkResult,
            mdResult: data.mdResult,
            sql: data.querySql,
            databaseInfo: data.databaseInfo,
        },
    ];
    // console.log('watchEffect.data', data);
});
</script>

<style scoped lang="scss">
.file-card-wrapper {
    position: relative;
    display: inline-block;
    margin-right: 8px;
    margin-bottom: 8px;

    .download-icon {
        position: absolute;
        right: 8px;
        bottom: 8px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.3s;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        z-index: 10;

        &:hover {
            background-color: #409eff;
            color: white;
        }

        .el-icon {
            font-size: 14px;
        }
    }

    &:hover {
        .download-icon {
            opacity: 1;
        }
    }
}
</style>

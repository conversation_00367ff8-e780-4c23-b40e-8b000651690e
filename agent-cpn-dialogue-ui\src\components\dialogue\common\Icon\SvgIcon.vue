<script setup>
import '@/assets/iconfont/iconfont.js';

const props = defineProps({
    className: {
        type: String,
        default: '',
    },
    name: {
        type: String,
        default: 'lianjie',
    },
    size: {
        type: String,
        default: '15px',
    },
    color: {
        type: String,
        default: '#333',
    },
});

const iconName = computed(() => `#common-${props.name}`);
const svgClass = computed(() => {
    if (props.className) {
        return `svg-icon ${props.className}`;
    }
    return 'svg-icon';
});
</script>

<template>
    <svg :class="svgClass" aria-hidden="true" :style="{ fontSize: size }">
        <use :xlink:href="iconName" :fill="color" />
    </svg>
</template>

<style scope lang="scss">
.sub-el-icon,
.nav-icon {
    position: relative;
    display: inline-block;
    margin-right: 12px;
    font-size: 15px;
}
.svg-icon {
    position: relative;
    width: 1em;
    height: 1em;
    vertical-align: -2px;
    fill: currentColor;
}
</style>

<template>
    <svg v-bind="filteredAttrs" aria-hidden="true">
        <use :xlink:href="`#${type}-${name}`" />
    </svg>
</template>

<script setup>
import '@/assets/iconfont/iconfont.js';

defineOptions({
    name: 'Icon',
});
const attrs = useAttrs();

const filteredAttrs = computed(() => {
    const { class: className, style, ...restAttrs } = attrs;
    return {
        ...restAttrs,
        class: ['icon', className],
        style: {
            color: props.color,
            width: `${props.size}px`,
            height: `${props.size}px`,
            ...(style || {}),
        },
    };
});

const props = defineProps({
    type: {
        type: String,
        default: 'common',
    },
    name: {
        type: String,
        default: 'lianjie',
    },
    size: {
        type: Number,
        default: 16,
    },
    color: {
        type: String,
        default: '#475467',
    },
});
</script>

<style lang="scss">
.icon {
    display: inline-block;
    aspect-ratio: 1;
    fill: currentColor;
}
</style>

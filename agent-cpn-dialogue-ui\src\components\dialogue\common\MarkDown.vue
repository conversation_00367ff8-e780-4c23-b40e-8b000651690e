<template>
    <div class="markdown-viewer">
        <div v-if="loading" class="loading">
            <el-skeleton :rows="10" animated />
        </div>
        <div v-else class="markdown-content" v-html="renderedContent"></div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import MarkdownIt from 'markdown-it';

defineOptions({
    name: 'MarkdownViewer',
});

const props = defineProps({
    // Markdown 源代码
    content: {
        type: String,
        default: '',
    },
    markdown: {
        type: String,
        default: '',
    },
});

const loading = ref(true);
const md = ref(null);
const markdownContent = ref('');

// 默认的政府服务指标考核相关内容
const defaultContent = `# 政府服务指标考核

## （一）抽查类指标问题分析

1. **省抽查"一件事"办事指标问题和网上服务情况问题**：1个，佛山市教育局的"教育入学一件事"存在申办网址打开异常。

2. **甲骨易用度问题**：2个，禅城区的"换领居民身份证"存在"点击【手机办理】，扫码后跳转至政务预约，页面空白，无法办理"；

3. **服务可用性问题**：22个，"开具个人所得税纳税记录"、"辖区城涉税事项报验"、"辖区域涉税事项信息反馈"和"运输危险化学品的车辆进入危险化学品运输车辆限制通行区域审批"存在功能显停使用说明；

4. **办事指南准确度问题**：2个，申领佛山市网络预约出租汽车驾驶员证、网络预约出租汽车驾驶员证延期办理速度查询和文书下载流程环节存在问题。

5. **市抽查发现并整改一件事办事指南和网上服务问题76个**。

![image-20250627115433102](https://cdn.jsdelivr.net/gh/tydumplings/Picgo/img20250627115440251.png)

## （二）各区服务问题统计

| 名称 | 佛山市 | 禅城区 | 南海区 | 顺德区 | 三水区 | 高明区 |
|------|--------|--------|--------|--------|--------|--------|
| 问题个数 | 10 | 13 | 14 | 14 | 9 | 16 |
| 占比 | 13.16% | 17.11% | 18.42% | 18.42% | 11.84% | 21.05% |
`;

// 初始化 Markdown 解析器
onMounted(() => {
    // 配置 markdown-it 实例
    md.value = new MarkdownIt({
        html: true,
        linkify: true,
        typographer: true,
    });

    // 设置表格样式
    md.value.renderer.rules.table_open = () => {
        return '<table class="md-table">';
    };

    // 设置内容
    markdownContent.value = props.content || props.markdown || defaultContent;

    loading.value = false;
});

// 监听content属性变化
watch(
    () => props.content,
    newContent => {
        if (newContent !== undefined) {
            markdownContent.value = newContent || defaultContent;
        }
    }
);

// 渲染 Markdown 内容
const renderedContent = computed(() => {
    if (!md.value) {
        return '加载中...';
    }

    if (!markdownContent.value) {
        return '没有内容';
    }

    try {
        const rendered = md.value.render(markdownContent.value);
        return rendered;
    } catch (error) {
        return `<div class="error">渲染错误: ${error.message}</div>`;
    }
});
</script>

<style scoped lang="scss">
.markdown-viewer {
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
}

.loading {
    padding: 20px;
}

.markdown-content {
    line-height: 1.6;
    color: #333;

    :deep(h1) {
        font-size: 28px;
        font-weight: 600;
        margin-top: 24px;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #eaecef;
    }

    :deep(h2) {
        font-size: 22px;
        font-weight: 600;
        margin-top: 24px;
        margin-bottom: 16px;
        padding-bottom: 6px;
        border-bottom: 1px solid #eaecef;
    }

    :deep(h3) {
        font-size: 18px;
        font-weight: 600;
        margin-top: 20px;
        margin-bottom: 12px;
    }

    :deep(p) {
        margin: 16px 0;
    }

    :deep(ul),
    :deep(ol) {
        padding-left: 24px;
        margin: 16px 0;
    }

    :deep(li) {
        margin: 8px 0;
    }

    :deep(code) {
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        background-color: rgba(27, 31, 35, 0.05);
        padding: 0.2em 0.4em;
        border-radius: 3px;
        font-size: 85%;
    }

    :deep(pre) {
        padding: 16px;
        overflow: auto;
        font-size: 85%;
        line-height: 1.45;
        background-color: #f6f8fa;
        border-radius: 3px;
        margin: 16px 0;

        code {
            background-color: transparent;
            padding: 0;
            margin: 0;
            font-size: 100%;
            word-break: normal;
            white-space: pre;
            overflow: visible;
        }
    }

    :deep(table) {
        display: block;
        width: 100%;
        overflow: auto;
        margin: 16px 0;
        border-spacing: 0;
        border-collapse: collapse;

        th,
        td {
            padding: 8px 13px;
            border: 1px solid #dfe2e5;
        }

        th {
            font-weight: 600;
            background-color: #f6f8fa;
        }

        tr:nth-child(2n) {
            background-color: #f8f8f8;
        }
    }

    :deep(blockquote) {
        padding: 0 1em;
        color: #6a737d;
        border-left: 0.25em solid #dfe2e5;
        margin: 16px 0;
    }

    :deep(hr) {
        height: 0.25em;
        padding: 0;
        margin: 24px 0;
        background-color: #e1e4e8;
        border: 0;
    }

    :deep(img) {
        max-width: 100%;
        box-sizing: content-box;
    }
}
</style>

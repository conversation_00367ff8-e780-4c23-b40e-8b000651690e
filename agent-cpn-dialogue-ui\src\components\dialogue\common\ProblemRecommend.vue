<template>
    <div class="problem-recommend-container">
        <div class="problem-title">问题推荐：</div>
        <div class="problem-list">
            <div
                v-for="(item, index) in problemList"
                :key="index"
                class="problem-item"
                @click="handleProblemClick(item)"
            >
                {{ item.title }}
            </div>
        </div>
    </div>
</template>

<script setup>
import { ArrowRight } from '@element-plus/icons-vue';
const { sendMessage } = useDialogueHandler();
import { TestData } from '@/components/dialogue/hooks/test';
defineOptions({
    name: 'ProblemRecommend',
});

// 定义组件的props
const props = defineProps({
    /**
     * 问题列表
     */
    problemList: {
        type: Array,
        default: () => [
            { id: '1', title: '五区预约情况分析' },
            { id: '2', title: '宝安区2024年人工智能企业空间分布数量' },
            { id: '3', title: '全国国高、规上、上市、专精特新企业的数量和占比' },
            { id: '4', title: '生成关联指标看板' },
        ],
    },
});

// 定义事件
const emit = defineEmits(['problem-click']);

/**
 * 处理问题点击事件
 * @param {Object} item - 点击的问题项
 */
const handleProblemClick = item => {
    console.log('🚀 ~ ProblemRecommend.vue ~ item:', item);
    addUserQuestion(item.title);
    // sendMessage(item.title);
    TestData();
    emit('problem-click', item);
};
</script>

<style scoped lang="scss">
.problem-recommend-container {
    border-radius: 12px;
    margin-bottom: 16px;
    width: 100%;
}

.problem-title {
    font-size: 14px;
    font-weight: 500;
    color: #606266;
    margin-bottom: 12px;
}

.problem-list {
    gap: 10px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
}

.problem-item {
    align-items: center;
    font-size: 14px;
    color: $text-secondary;
    padding: 14px 6px;
    background: $bg;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s;
    box-shadow: 0 1px 4px #dee6f0;

    .arrow-icon {
        margin-left: 4px;
        font-size: 12px;
        color: #909399;
    }

    &:hover {
        background-color: #f0f2f5;
    }
}
</style>

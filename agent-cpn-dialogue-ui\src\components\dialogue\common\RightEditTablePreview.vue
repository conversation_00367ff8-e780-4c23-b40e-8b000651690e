<!-- 指标信息 -->
<template>
    <div class="indicator-info-container">
        <!-- 指标列表 -->
        <div class="indicator-list">
            <!-- 动态渲染指标项 -->
            <!-- 这里可以根据实际情况展示不同类型的详情内容 -->
            <div class="flex justify-end mr-2" v-if="showEdit">
                <el-button type="text" @click="handleEdit">编辑</el-button>
            </div>
            <div v-for="item in indicators" :key="item.id" class="indicator-item">
                <div class="item-content">
                    <div class="form-row">
                        <div class="form-item">
                            <div class="form-label required">指标名称：</div>
                            <div class="form-value">
                                <el-input v-model="item.name" :disabled="!item.editing" />
                            </div>
                        </div>
                        <div class="form-item">
                            <div class="form-label required">数据来源：</div>
                            <div class="form-value">
                                <el-input v-model="item.source" :disabled="!item.editing" />
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-item">
                            <div class="form-label required">业务口径：</div>
                            <div class="form-value">
                                <el-input
                                    type="textarea"
                                    v-model="item.description"
                                    :disabled="!item.editing"
                                    :rows="2"
                                />
                            </div>
                        </div>
                        <div class="form-item">
                            <div class="form-label required">计算公式：</div>
                            <div class="form-value">
                                <el-input type="textarea" v-model="item.formula" :disabled="!item.editing" :rows="2" />
                            </div>
                        </div>
                    </div>

                    <div class="form-row-sql" v-if="item.sql">
                        <div class="font-bold mb-2">SQL语句:</div>

                        <div class="form-value sql-area">
                            <div class="sql-content" :class="{ editing: item.editing }">
                                <div
                                    v-if="!item.editing"
                                    v-html="highlightCode(item.sql)"
                                    class="shikiji-container"
                                ></div>
                                <el-input v-else type="textarea" v-model="item.sql" :rows="6" />
                            </div>
                        </div>
                    </div>

                    <div class="form-row" v-if="showUnit">
                        <div class="form-item">
                            <div class="form-label required">数值：</div>
                            <div class="form-value">
                                <el-input v-model="item.value" :disabled="!item.editing" />
                            </div>
                        </div>
                        <div class="form-item">
                            <div class="form-label required">单位：</div>
                            <div class="form-value">
                                <el-input v-model="item.unit" :disabled="!item.editing" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-if="showChart">
            <!-- 图表展示区域 -->
            <div class="font-bold mb-2">结果图表</div>

            <div class="chart-container">
                <EChartComponent
                    :option="chartOption"
                    height="300px"
                    @chart-ready="handleChartReady"
                    @chart-click="handleChartClick"
                />
            </div>
            <common-TablePreview :markdownContent="markdownContent" v-if="showTable" />
        </div>

        <div class="form-actions">
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" @click="handleSave">保存</el-button>
        </div>
    </div>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { watchEffect, ref, onMounted, onUnmounted } from 'vue';
import EChartComponent from './EChartComponent.vue';
import eventBus from '@/utils/bus';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
const { setHighlighter, highlightCode } = useRightPanelStore();

// 定义组件选项
defineOptions({
    name: 'IndicatorInfo',
});

const props = defineProps({
    showUnit: {
        type: Boolean,
        default: false,
    },
    showChart: {
        type: Boolean,
        default: false,
    },
    showTable: {
        type: Boolean,
        default: false,
    },
    showEdit: {
        type: Boolean,
        default: false,
    },
    data: {
        type: Array,
        default: () => [
            {
                id: 3,
                source: '全市业务数据',
                description: '本年度内，佛山市全市政务服务事项对应的业务总量',
                formula: '办件汇聚表-去重复，申报主体类型汇总数',
                sql: `SELECT
            region,
            QUARTER(order_date) AS quarter,
            AVG(CASE WHEN user_count > 0 THEN
            order_amount/user_count
            ELSE NULL END) AS
            avg_customer_value`,
                value: '3.97',
                unit: '万户',
                editing: false,
                sqlExecuted: false,
            },
        ],
    },
});

const markdownContent = ref(`| 序号 | 部门名称 | 自然人业务量 | 法人业务量 | 业务总量 |
| --- | --- | --- | --- | --- |
| 1 | 禅城区 | 171800 | 72533 | 244333 |
| 2 | 顺德区 | 90880 | 22678 | 113558 |
| 3 | 南海区 | 254380 | 49323 | 303703 |
| 4 | 高明区 | 43106 | 6907 | 50013 |
| 5 | 三水区 | 24572 | 13418 | 37990 |
| 6 | 高新区 | 0 | 63 | 66 |`);

// 定义事件
const emit = defineEmits(['close', 'save', 'edit']);

onMounted(() => {
    setHighlighter();
});

/**
 * 指标数据
 */
const indicators = ref();
watchEffect(() => {
    indicators.value = props.data;
});

/**
 * 图表配置
 */
const chartOption = ref({
    title: {
        text: '指标数据分析',
        left: 'center',
    },
    tooltip: {
        trigger: 'axis',
        axisPointer: {
            type: 'shadow',
        },
    },
    legend: {
        data: ['平台数据', '接口来源', '库表报送', '省级数据回流'],
        bottom: '0%',
    },
    grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true,
    },
    xAxis: {
        type: 'category',
        data: ['市级', '禅城区', '顺德区', '高明区', '三水区', '南海区'],
    },
    yAxis: [
        {
            type: 'value',
            name: '数量',
            position: 'left',
            axisLabel: {
                formatter: '{value}',
            },
        },
        {
            type: 'value',
            name: '总量',
            position: 'right',
            axisLabel: {
                formatter: '{value}',
            },
        },
    ],
    series: [
        {
            name: '平台数据',
            type: 'bar',
            data: [45000, 70000, 60000, 80000, 50000, 30000],
        },
        {
            name: '接口来源',
            type: 'bar',
            data: [70000, 120000, 90000, 130000, 90000, 70000],
        },
        {
            name: '库表报送',
            type: 'bar',
            data: [50000, 80000, 70000, 90000, 60000, 40000],
        },
        {
            name: '省级数据回流',
            type: 'bar',
            data: [60000, 110000, 100000, 120000, 80000, 60000],
        },
        {
            name: '汇总',
            type: 'line',
            yAxisIndex: 1,
            data: [100000, 200000, 150000, 220000, 170000, 120000],
            symbolSize: 8,
        },
    ],
});

/**
 * 处理图表准备完成
 */
const handleChartReady = chart => {
    console.log('图表已准备好', chart);
};

/**
 * 处理图表点击事件
 */
const handleChartClick = params => {
    console.log('图表点击事件', params);
};

/**
 * 处理保存按钮点击
 */
const handleSave = () => {
    indicators.value.forEach(item => {
        item.editing = false;
    });
    // 额外的事件处理
    emit('save', indicators.value);
    ElMessage.success('保存成功');
};

/**
 * 处理编辑按钮点击
 */
const handleEdit = val => {
    // 然后执行原有的编辑逻辑
    indicators.value.forEach(item => {
        item.editing = true;
    });
    emit('edit', indicators.value);
};

/**
 * 处理取消按钮点击
 */
const cancel = () => {
    emit('cancel', indicators.value);
    ElMessage.info('操作已取消');
};
onMounted(() => {
    eventBus.on('edit', handleEdit);
});
onUnmounted(() => {
    eventBus.off('edit');
});
</script>

<style scoped lang="scss">
.indicator-info-container {
    position: relative;
    width: 100%;
    border-radius: 4px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
    }

    .close-btn {
        cursor: pointer;
        font-size: 20px;
        color: #909399;

        &:hover {
            color: #409eff;
        }
    }
}
.form-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 40px;
    margin-top: 20px;
}

.chart-container {
    margin-bottom: 20px;
    border-radius: 4px;
    padding: 15px;
}

.indicator-item {
    margin-bottom: 20px;
    border-radius: 4px;

    .item-content {
        padding: 15px 0;
        font-size: 14px;
    }
}
.form-row-sql {
    margin-bottom: 15px;
    .shikiji-container {
        margin: 0;
        border-radius: 4px;
        overflow: auto;
        font-size: 14px;
        line-height: 1.5;
        margin: 15px 0;
        :deep(pre) {
            margin: 0;
            background-color: #f8f9fc !important;
        }
    }

    .tip {
        display: flex;
        justify-content: space-between;
    }
}

.form-row {
    display: flex;
    margin-bottom: 15px;

    .form-item {
        flex: 1;
        display: flex;
        padding: 0 10px;

        &.full-width {
            width: 100%;
        }

        .form-label {
            text-align: right;
            display: flex;
            padding-right: 10px;
            line-height: 32px;
            color: #606266;

            &.required::before {
                content: '*';
                color: #f56c6c;
                margin-right: 4px;
            }
        }

        .form-value {
            flex: 1;
        }
    }
}

.footer-info {
    padding: 15px;
    margin: 20px;
    border-radius: 4px;

    .info-text {
        font-size: 14px;
        color: #606266;
        line-height: 1.8;
    }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;

        .form-item {
            margin-bottom: 10px;
        }
    }
}
</style>

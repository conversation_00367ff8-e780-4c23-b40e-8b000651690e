<template>
    <div class="w-full my-4 rounded-md overflow-hidden">
        <div
            class="flex items-center p-2.5 px-4 bg-[#f5f7fa] rounded-t-md border border-[#ebeef5] border-b-0 justify-end"
        >
            <div class="flex gap-2" v-if="showIcon">
                <el-tooltip content="复制Markdown内容" placement="top">
                    <Icon name="copy" @click="copyMarkdownTable" size="14" />
                </el-tooltip>
                <el-tooltip content="下载表格" placement="top">
                    <el-icon :size="16" @click="downloadExcelTable"><Download /></el-icon>
                </el-tooltip>
            </div>
        </div>
        <!-- 表格预览 -->
        <el-table
            v-if="tableData.length > 0"
            :data="tableData"
            border
            stripe
            style="width: 100%"
            :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        >
            <el-table-column
                v-for="(column, index) in tableHeaders"
                :key="index"
                :prop="column.prop"
                :label="column.label"
                align="center"
            />
        </el-table>
        <div v-else class="flex justify-center items-center h-25 bg-[#f5f7fa] text-[#909399] text-sm rounded-md">
            <p>暂无数据</p>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';

defineOptions({
    name: 'MarkdownTable',
});

const emit = defineEmits(['threeIconFn']);

const props = defineProps({
    markdownContent: {
        type: String,
        default: `
| 序号 | 部门名称 | 自然人业务量 | 法人业务量 | 业务总量 |
| --- | --- | --- | --- | --- |
| 1 | 禅城区 | 171800 | 72533 | 244333 |
| 2 | 顺德区 | 90880 | 22678 | 113558 |
| 3 | 南海区 | 254380 | 49323 | 303703 |
| 4 | 高明区 | 43106 | 6907 | 50013 |
| 5 | 三水区 | 24572 | 13418 | 37990 |
| 6 | 高新区 | 0 | 63 | 66 |
| 7 | 中共佛山市委军民融合发展委员会办公室 | 0 | 0 | 0 |
| 8 | 中共佛山市委办公室 | 0 | 0 | 2 |
| 9 | 中共佛山市委机要和保密局 | 0 | 0 | 0 |
| 10 | 中华人民共和国佛山出入境检验检疫局 | 0 | 0 | 0 |
| 11 | 中华人民共和国佛山海事局 | 0 | 0 | 0 |
| 12 | 中国共产党佛山市委员会宣传部(佛山市新闻出版局) | 0 | 260 | 260 |
| 13 | 中国共产党佛山市委员会统一战线工作部 | 0 | 2 | 3 |
| 14 | 中国国际贸易促进委员会 | 0 | 0 | 0 |
| 15 | 佛山市委员会中国电信股份有限公司 | 0 | 0 | 0 |
| 16 | 佛山神域区分公司中国邮政速递物流股份有限公司 | 0 | 0 | 0 |
| 17 | 佛山市公安局佛山市交通运输局(公路局) | 23107 | 9355 | 32462 |
| 18 | 佛山市人力资源和社会保障局 | 15 | 1 | 16 |
`,
    },
    fileName: {
        type: String,
        default: '表格数据',
    },
    showIcon: {
        type: Boolean,
        default: false,
    },
    threeIcon: {
        type: String,
        default: 'zhankai',
    },
    threeIconTooltip: {
        type: String,
        default: '展开',
    },
    data: {
        type: Object,
        default: () => ({}),
    },
});

const tableHeaders = ref([]);
const tableData = ref([]);
const originalMarkdown = ref('');
const editTable = () => {
    emit('threeIconFn');
};
/**
 * @description 解析Markdown表格数据
 * @param {string} markdown Markdown格式的表格内容
 */
const parseMarkdownTable = markdown => {
    if (!markdown) {
        tableHeaders.value = [];
        tableData.value = [];
        return;
    }

    // 保存原始Markdown内容
    originalMarkdown.value = markdown;

    // 按行分割
    const lines = markdown.trim().split('\n');

    if (lines.length < 3) {
        console.error('Markdown表格格式不正确，至少需要标题行、分隔行和一行数据');
        return;
    }

    // 提取表头行
    const headerLine = lines[0].trim();
    // 移除首尾的 | 并按 | 分割
    const headers = headerLine
        .replace(/^\||\|$/g, '')
        .split('|')
        .map(h => h.trim());

    // 创建表头配置
    tableHeaders.value = headers.map((header, index) => ({
        prop: `col${index}`,
        label: header,
    }));

    // 跳过分隔行（第二行），处理数据行
    const dataRows = lines.slice(2);
    const parsedData = [];

    dataRows.forEach(row => {
        // 移除首尾的 | 并按 | 分割
        const cells = row
            .replace(/^\||\|$/g, '')
            .split('|')
            .map(cell => cell.trim());

        // 创建数据对象
        const rowData = {};
        cells.forEach((cell, index) => {
            rowData[`col${index}`] = cell;
        });

        parsedData.push(rowData);
    });

    tableData.value = parsedData;
};

/**
 * @description 复制Markdown表格到剪贴板
 */
const copyMarkdownTable = () => {
    if (tableData.value.length === 0) {
        ElMessage.warning('表格暂无数据可复制');
        return;
    }

    // 直接复制原始Markdown内容
    navigator.clipboard
        .writeText(originalMarkdown.value)
        .then(() => {
            ElMessage.success('已复制到剪贴板');
        })
        .catch(err => {
            console.error('复制失败:', err);
            ElMessage.error('复制失败，请重试');
        });
};

/**
 * @description 下载Markdown文件
 */
const downloadMarkdownTable = () => {
    if (tableData.value.length === 0) {
        ElMessage.warning('暂无数据可下载');
        return;
    }
    // 创建Markdown文件内容
    const markdownContent = originalMarkdown.value;
    // 创建下载链接
    const blob = new Blob([markdownContent], { type: 'text/markdown;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    // 设置下载属性
    link.setAttribute('href', url);
    link.setAttribute('download', `表格数据_${new Date().toLocaleDateString()}.md`);
    link.style.visibility = 'hidden';
    // 添加到DOM，触发下载，然后移除
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success('Markdown表格下载成功');
};

/**
 * @description 下载Excel表格文件
 */
const downloadExcelTable = () => {
    if (tableData.value.length === 0) {
        ElMessage.warning('暂无数据可下载');
        return;
    }
    // 创建CSV内容
    let csvContent = '';
    // 添加表头
    const headerRow = tableHeaders.value.map(header => `"${header.label}"`).join(',');
    csvContent += headerRow + '\n';
    // 添加数据行
    tableData.value.forEach(row => {
        const dataRow = tableHeaders.value.map(header => `"${row[header.prop] || ''}"`).join(',');
        csvContent += dataRow + '\n';
    });
    // 为Excel添加BOM头，解决中文乱码问题
    const BOM = '\uFEFF';
    const csvContentWithBOM = BOM + csvContent;
    // 创建下载链接
    const blob = new Blob([csvContentWithBOM], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    // 设置下载属性
    link.setAttribute('href', url);
    // 设置文件名
    link.setAttribute('download', `${props.fileName}`);
    link.style.visibility = 'hidden';
    // 添加到DOM，触发下载，然后移除
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    ElMessage.success('Excel表格下载成功');
};

// 监听Markdown内容变化
watch(
    () => props.markdownContent,
    newValue => {
        parseMarkdownTable(newValue);
    },
    { deep: true, immediate: true }
);
</script>

<script setup>
import { FilesCard } from 'vue-element-plus-x';
import { computed, ref } from 'vue';
import { uploadFile } from '@/api/dialogue/index.js';
import { files, fileListUrl } from '@/components/dialogue/store/input.js';
import xlsxTemplate from '@dialogue/templates/表格模板.xlsx';
import wordTemplate from '@dialogue/templates/word模板.docx';
defineOptions({
    name: 'FileList',
});

// 定义props和emits
const props = defineProps({
    searchKeyword: {
        type: String,
        default: '',
    },
});

const emit = defineEmits(['select-file', 'select-additional-content']);

const handleFileCardClick = async file => {
    try {
        // 从实际路径获取文件
        const response = await fetch(file.filePath);
        const blob = await response.blob();
        console.log('文件字节大小:', blob.size); // 打印实际字节大小

        // 创建File对象
        const actualFile = new File([blob], file.name, {
            type: blob.type,
            lastModified: Date.now(),
        });

        // 上传文件
        const formData = new FormData();
        formData.append('file', actualFile);
        const { data: fileUrl } = await uploadFile(formData);
        fileListUrl.value = fileUrl;

        const templateFile = {
            ...file,
            fileUrl: fileUrl,
            showDelIcon: true,
            fileSize: blob.size, // 使用实际的字节大小，而不是预设值
        };

        files.value = [];
        files.value.push(templateFile);
        console.log('files.value', templateFile);

        // 发送完整文件信息
        emit('select-file', templateFile);
    } catch (error) {
        console.error('模板文件处理失败:', error);
        emit('select-file', file);
    }
};

/**
 * @description 处理附加内容点击事件
 */
const handleAdditionalContentClick = () => {
    emit('select-additional-content', additionalContent.value);
};

// 自己定义文件颜色2
const colorMap2 = {
    word: '#0078D4',
    excel: '#4CB050',
    ppt: '#FF9933',
    pdf: '#E81123',
    txt: '#666666',
    mark: '#FFA500',
    image: '#B490F3',
    audio: '#00B2EE',
    video: '#2EC4B6',
    three: '#00C8FF',
    code: '#00589F',
    database: '#F5A623',
    link: '#007BFF',
    zip: '#888888',
    file: '#F0D9B5',
    unknown: '#D8D8D8',
};

// 文件列表配置
const fileList = ref([
    {
        id: 11,
        uid: '11',
        name: 'word模板',
        fileType: 'word',
        fileSize: 38654, // 1.8MB
        filePath: wordTemplate,
    },

    {
        id: 12,
        uid: '12',
        name: '表格模板',
        fileType: 'excel',
        fileSize: 10150, // 1.8MB
        filePath: xlsxTemplate,
    },
    // {
    //     id: 13,
    //     uid: '13',
    //     name: '项目进度汇报PPT',
    //     fileType: 'ppt',
    //     description: '5.2MB',
    //     fileSize: 5.2 * 1024 * 1024, // 5.2MB
    // },
    // {
    //     id: 14,
    //     uid: '14',
    //     name: '用户手册文档',
    //     fileType: 'pdf',
    //     description: '3.7MB',
    //     fileSize: 3.7 * 1024 * 1024, // 3.7MB
    // },
    // {
    //     id: 15,
    //     uid: '15',
    //     name: '会议记录',
    //     fileType: 'txt',
    //     description: '128KB',
    //     fileSize: 128 * 1024, // 128KB
    // },
    // {
    //     id: 16,
    //     uid: '6',
    //     name: '需求说明文档',
    //     fileType: 'mark',
    //     description: '256KB',
    //     fileSize: 256 * 1024, // 256KB
    // },
]);

// 附加内容
const additionalContent = ref(
    `帮我调用最新指标数据，查询下列数据：截至目前统计，2025年1季度的深圳市宝安区GDP当期规模为【】亿元；2024年1季度深圳市宝安区GDP当期规模为【】亿元，同比增长【】%；2023年1季度深圳市宝安区GDP当期规模为【】亿元，同比增长【】%；2022年1季度深圳市宝安区GDP当期规模为【】亿元，同比增长【】%；`
);

/**
 * @description 根据关键字过滤文件列表
 */
const filteredFileList = computed(() => {
    const keyword = props.searchKeyword.trim().toLowerCase();

    if (!keyword) return fileList.value;

    // 包含"报告"关键字，显示word文件
    if (keyword.includes('报告')) {
        return fileList.value.filter(file => file.fileType === 'word');
    }

    // 包含"表格"关键字，显示excel文件
    if (keyword.includes('表格')) {
        return fileList.value.filter(file => file.fileType === 'excel');
    }

    // 包含"PPT"或"演示"关键字，显示ppt文件
    if (keyword.includes('ppt') || keyword.includes('演示')) {
        return fileList.value.filter(file => file.fileType === 'ppt');
    }

    // 包含"文档"关键字，显示pdf文件
    if (keyword.includes('文档')) {
        return fileList.value.filter(file => file.fileType === 'pdf');
    }

    // 包含"记录"关键字，显示txt文件
    if (keyword.includes('记录')) {
        return fileList.value.filter(file => file.fileType === 'txt');
    }

    // 包含"说明"关键字，显示markdown文件
    if (keyword.includes('说明')) {
        return fileList.value.filter(file => file.fileType === 'mark');
    }

    // 默认情况下，关键字不匹配任何文件类型，返回空数组
    return [];
});

/**
 * @description 是否显示附加内容
 */
const showAdditionalContent = computed(() => {
    // 当没有搜索关键字或关键字没有匹配到任何文件时显示附加内容
    return !props.searchKeyword.trim() || filteredFileList.value.length === 0;
});
</script>

<template>
    <div class="flex flex-col gap-2">
        <div class="files-card-container-wrapper">
            <div class="files-card-container">
                <FilesCard
                    v-for="file in filteredFileList"
                    :key="file.uid"
                    :uid="file.uid"
                    :name="file.name"
                    :icon-color="colorMap2[file.fileType]"
                    :file-type="file.fileType"
                    class="file-card"
                    :file-size="file.fileSize"
                    @click="handleFileCardClick(file)"
                ></FilesCard>
                <div
                    v-if="showAdditionalContent && additionalContent"
                    class="additional-content"
                    @click="handleAdditionalContentClick"
                >
                    {{ additionalContent }}
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped lang="less">
.files-card-container-wrapper {
    display: flex;
    gap: 12px;
    flex-direction: column;

    .files-card-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }
}

.file-card {
    margin-bottom: 20px;
}

.file-size {
    font-size: 12px;
    color: #909399;
    margin-left: 8px;
}

.additional-content {
    margin-top: 16px;
    line-height: 1.5;
    color: #606266;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 20px;
    background: #fff;
    margin-bottom: 20px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
        background: #f5f7fa;
        border-color: #c6e2ff;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
}
</style>

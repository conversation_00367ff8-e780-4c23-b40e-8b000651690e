import { computed, reactive, ref, watch } from 'vue';
import { DialogueService } from '@/components/dialogue/hooks/useSystemResponse';
import { generateDialogueMsg, reply } from '@/api/dialogue';
import { useDialogueHandler } from '@/components/dialogue/hooks/useDialogueHandler';
import { listTableBySource, selectDatabaseDetail } from '@/api/dialogue/data-catalog-util-api';

/**
 * @description
 * 目录表单数据和逻辑处理的组合式函数
 * 处理表单字段状态、显示/隐藏逻辑、表单校验、数据转换以及提交功能
 *
 * @param {Object} config - 配置对象，包含字段配置、默认值等
 * @param {Function} emitEvent - 事件发射函数，用于向父组件传递事件
 * @param entityData
 * @returns {Object} 表单相关的状态和方法
 */
export const useCatalogForm = (config, emitEvent, entityData) => {
    const { replySendWs } = useDialogueHandler();
    // 表单占位符配置
    const placeholders = computed(() => config.value.placeholders || {});

    // 日期范围
    const dateRange = ref(['2024-01-01', '2024-12-31']);

    const fileUrl = ref('');
    const schemaName = ref('');
    // 各字段的值
    const fieldValues = reactive({
        category: '结构化数据',
        system: '',
        db: '',
        schema: '',
        table: '',
        range: '',
        level: '',
        frequency: '',
        fileSystem: '',
        fileUrl: '',
        service: '',
    });

    // 格式化日期范围显示
    const formatDateRange = computed(() => {
        if (dateRange.value && dateRange.value.length === 2) {
            return `${dateRange.value[0]}—${dateRange.value[1]}`;
        }
        return '2024-01-01—2024-12-31';
    });
    // 格式化日期范围显示
    const formatUrl = computed(() => {
        if (fileUrl.value) {
            return fileUrl.value;
        }
        return '/';
    });

    const sureFileUrl = () => {
        console.log('asdas', formatUrl.value);
        fieldValues.fileUrl = fileUrl.value;
    };

    watch(fieldValues, newVal => {
        // 数据库相关链路监听
        let db = newVal.db;
        if (db && db != '' && db != '数据库名称') {
            // 模式名称配置 - 现在改为文本输入，不再自动设置
            selectDatabaseDetail({ id: db }).then(res => {
                if (res.data.data) {
                    let defaultSchemaName = '';
                    if (res.data.data.type === 0) {
                        defaultSchemaName = res.data.data.dbName;
                    } else {
                        defaultSchemaName = res.data.data.username;
                    }
                    // 如果用户还没有输入模式名称，则设置默认值
                    if (!schemaName.value) {
                        schemaName.value = defaultSchemaName;
                    }
                    //表名筛选配置
                    let paramTables = {
                        id: db,
                        schemaName: schemaName.value || defaultSchemaName,
                    };
                    listTableBySource(paramTables).then(res => {
                        if (res.data.data) {
                            config.value.tables = res.data.data.map(item => ({
                                label: item.tableComment,
                                value: item.tableName,
                            }));
                        }
                    });
                }
            });
        }

        // 目录类别监听
        let catalogType = newVal.category;
        if (catalogType === 2) {
            config.value.fieldOrder = ['category', 'system', 'fileSystem', 'fileUrl'];
        } else if (catalogType === 3) {
            config.value.fieldOrder = ['category', 'system', 'service'];
        } else {
            config.value.fieldOrder = ['category', 'system', 'db', 'schema', 'table', 'range', 'level', 'dateRange'];
        }
    });

    // 获取字段值
    const getFieldValue = key => {
        if (key === 'dateRange') {
            return formatDateRange.value;
        }

        if (key === 'fileUrl') {
            return formatUrl.value;
        }

        if (key === 'schema') {
            return schemaName.value || '模式名称';
        }

        const optionsMap = {
            category: 'categoryOptions',
            system: 'systemOptions',
            db: 'dbOptions',
            schema: 'schemaOptions',
            table: 'tableOptions',
            range: 'rangeOptions',
            level: 'levelOptions',
            frequency: 'frequencyOptions',
            fileSystem: 'fileSystemOptions',
            service: 'serviceOptions',
        };

        const options = optionsMap[key] ? eval(optionsMap[key]).value : [];
        const selectedValue = fieldValues[key];
        const selectedOption = options.find(item => item.value === selectedValue);

        return selectedOption ? selectedOption.label : '';
    };

    // 各下拉选项的默认数据
    const categoryOptions = computed(
        () =>
            config.value.categories || [
                { label: '非结构化数据', value: '非结构化数据' },
                { label: '结构化数据', value: '结构化数据' },
                { label: '接口数据', value: '接口数据' },
            ]
    );

    const systemOptions = computed(() => config.value.systems || []);

    const dbOptions = computed(() => config.value.dbs || []);

    const schemaOptions = computed(
        () =>
            config.value.schemas || [
                { label: '主数据', value: 'master-data' },
                { label: '分析数据', value: 'analysis-data' },
                { label: '业务数据', value: 'business-data' },
                { label: '历史数据', value: 'history-data' },
                { label: '临时数据', value: 'temp-data' },
                { label: '无', value: '' },
            ]
    );

    const tableOptions = computed(() => config.value.tables || []);

    const rangeOptions = computed(
        () =>
            config.value.ranges || [
                { label: '全部数据', value: '1' },
                { label: '增量数据', value: '2' },
            ]
    );

    const levelOptions = computed(
        () =>
            config.value.levels || [
                { label: '一级(公开)', value: '1' },
                { label: '二级(内部)', value: '2' },
                { label: '三级(保密)', value: '3' },
                { label: '四级(机密)', value: '4' },
                { label: '五级(绝密)', value: '5' },
            ]
    );

    const frequencyOptions = computed(
        () =>
            config.value.frequencies || [
                { label: '实时', value: '0' },
                { label: '每日', value: '1' },
                { label: '每周', value: '2' },
                { label: '每月', value: '3' },
                { label: '每季度', value: '4' },
                { label: '每年', value: '5' },
                { label: '不定期', value: '6' },
            ]
    );

    const fileSystemOptions = computed(() => config.value.fileSystems || []);

    const fileUrlOptions = computed(() => config.value.fileUrls || '');

    const serviceOptions = computed(() => config.value.services || []);

    // 根据字段key获取对应的选项数组
    const getOptionsForField = key => {
        const optionsMap = {
            category: categoryOptions.value,
            system: systemOptions.value,
            db: dbOptions.value,
            schema: schemaOptions.value,
            table: tableOptions.value,
            range: rangeOptions.value,
            level: levelOptions.value,
            frequency: frequencyOptions.value,
            fileSystem: fileSystemOptions.value,
            fileUrl: fileUrlOptions.value,
            service: serviceOptions.value,
        };

        return optionsMap[key] || [];
    };

    const showTips = ref(false);

    // 获取字段是否显示
    const visibleFields = computed(() => {
        // 基础字段定义
        const allFields = [
            { key: 'category', label: '目录分类' },
            { key: 'system', label: '应用系统' },
            { key: 'db', label: '来源数据库' },
            { key: 'schema', label: '模式名称' },
            { key: 'table', label: '数据表是' },
            { key: 'range', label: '数据范围' },
            { key: 'level', label: '数据分级' },
            { key: 'dateRange', label: '时间范围' },
            { key: 'fileSystem', label: '文件系统' },
            { key: 'fileUrl', label: '文件URL' },
            { key: 'service', label: '服务名称' },
        ];

        // 如果指定了自定义顺序，按自定义顺序排序
        if (config.value.fieldOrder && config.value.fieldOrder.length > 0) {
            const customOrderedFields = [];
            // 按自定义顺序添加字段
            for (const key of config.value.fieldOrder) {
                const field = allFields.find(f => f.key === key);
                if (field) {
                    customOrderedFields.push(field);
                }
            }
            return customOrderedFields;
        }

        return allFields;
    });

    // 默认字段配置，控制必填项
    const defaultFieldsConfig = [
        { key: 'category', label: '目录分类', required: true },
        { key: 'system', label: '应用系统', required: true },
        { key: 'db', label: '来源数据库', required: true },
        { key: 'schema', label: '模式名称', required: false },
        { key: 'table', label: '数据表是', required: true },
        { key: 'range', label: '数据范围', required: false },
        { key: 'level', label: '数据分级', required: false },
        { key: 'dateRange', label: '时间范围', required: false },
        { key: 'fileSystem', label: '文件系统', required: false },
        { key: 'fileUrl', label: '文件URL', required: false },
        { key: 'service', label: '服务名称', required: false },
    ];

    // 获取字段配置
    const fieldsConfig = computed(() => {
        // 优先使用传入的字段配置
        if (config.value.fieldsConfig && config.value.fieldsConfig.length > 0) {
            return config.value.fieldsConfig;
        }
        return defaultFieldsConfig;
    });

    /**
     * 判断字段是否为必填项
     * @param {string} fieldKey - 字段名
     * @returns {boolean} - 是否为必填项
     */
    const isFieldRequired = fieldKey => {
        // 首先通过 fieldsConfig 里的配置判断
        const fieldConfig = fieldsConfig.value.find(field => field.key === fieldKey);
        if (fieldConfig) {
            return fieldConfig.required;
        }

        if (config.value.fieldsConfig && config.value.fieldsConfig.length > 0) {
            return config.value.fieldsConfig.some(item => item.field === fieldKey && item.required === true);
        }

        return false;
    };

    // 检查必填项
    const checkRequiredFields = () => {
        const fieldsToCheck = [
            {
                key: 'category',
                value: fieldValues.category,
            },
            {
                key: 'system',
                value: fieldValues.system,
            },
            {
                key: 'db',
                value: fieldValues.db,
            },
            {
                key: 'schema',
                value: schemaName.value,
            },
            {
                key: 'table',
                value: fieldValues.table,
            },
            {
                key: 'range',
                value: fieldValues.range,
            },
            {
                key: 'level',
                value: fieldValues.level,
            },
            {
                key: 'dateRange',
                value: dateRange.value && dateRange.value.length === 2,
            },
        ];

        // 如果频率字段未隐藏，则也检查频率字段
        if (!config.value.hideFrequency) {
            fieldsToCheck.push({
                key: 'frequency',
                value: fieldValues.frequency,
            });
        }

        // 检查所有必填字段是否有值
        return fieldsToCheck.filter(field => isFieldRequired(field.key)).some(field => !field.value);
    };

    // 获取表单数据
    const getFormData = () => {
        const formData = {
            category: fieldValues.category,
            system: fieldValues.system,
            db: fieldValues.db,
            schema: schemaName.value,
            table: fieldValues.table,
            range: fieldValues.range,
            level: fieldValues.level,
            dateRange: dateRange.value,
            fileSystem: fieldValues.fileSystem,
            fileUrl: fileUrl.value,
            service: fieldValues.service,
        };

        if (!config.value.hideFrequency) {
            formData.frequency = fieldValues.frequency;
        }

        return formData;
    };

    // 生成任务
    const generateTask = async contxt => {
        // 获取表单数据
        const formData = getFormData();
        // 用户确认信息

        const userMessage = `生成资源目录`;
        // 从表单中获取对应值
        // const categoryValue = getFieldValue('category'); // 目录分类
        // const systemValue = getFieldValue('system'); // 应用系统
        // const dbValue = getFieldValue('db'); // 来源数据库
        // const tableValue = getFieldValue('table'); // 表名
        // const rangeValue = getFieldValue('range'); // 数据范围
        // const levelValue = getFieldValue('level'); // 数据分级
        // const dateRangeValue = formatDateRange.value; // 时间范围
        // const fileSystemValue = getFieldValue('fileSystem'); // 文件系统
        // const fileUrlValue = getFieldValue('fileUrl'); // 文件URL
        // const serviceValue = getFieldValue('service'); // 服务名称

        // 构建发送数据对象，将表单数据整合到entity中
        let data = {
            actionCode: 'catalogSourceGenAction',
            msg: userMessage,
            dialogueId: contxt.dialogueId,
            userId: contxt.userId,
            entity: entityData.result.entity,
            params: {
                catalogType: formData.category,
                appSystem: formData.system,
                database: formData.db,
                schema: formData.schema,
                tableName: formData.table,
                dataLevel: formData.level,
                timeRange: formData.dateRange,
                dataRange: formData.range,
                fileServer: formData.fileSystem,
                filePath: formData.fileUrl,
                service: formData.service,
                updateFrequency: formData.frequency,
            },
        };

        // 添加用户消息到对话
        DialogueService.addUserQuestion(userMessage);
        console.log('发送数据:', data);
        generateDialogueMsg(data).then(async res => {
            if (res.data.code === 200) {
                data['messageId'] = res.data.data.messageId;
                // 发送WebSocket请求
                replySendWs(data);
                try {
                    // 发送HTTP请求并等待响应
                    const { data: res } = await reply(data);
                } catch (error) {
                    console.error('生成任务失败:', error);
                }

                showTips.value = false;
                // emitEvent('generate-task', formData);
            }
        });
    };

    // 表单值变化时触发
    const emitFormChange = () => {
        const formData = getFormData();
        const isValid = !checkRequiredFields();

        emitEvent('form-change', formData);
        if (isValid) {
            emitEvent('form-valid', formData);
        } else {
            emitEvent('form-invalid');
        }
    };

    // 监听所有表单项的变化
    watch(
        [fieldValues, dateRange, schemaName],
        () => {
            emitFormChange();
        },
        { deep: true }
    );

    // 监听配置变化
    watch(
        () => config.value?.defaultValues,
        newVal => {
            if (newVal) {
                fieldValues.category = newVal.category || '';
                fieldValues.system = newVal.system || '';
                fieldValues.db = newVal.db || '';
                schemaName.value = newVal.schema || '';
                fieldValues.table = newVal.table || '';
                fieldValues.range = newVal.range || '';
                fieldValues.level = newVal.level || '';
                fieldValues.frequency = newVal.frequency || '';
                dateRange.value = newVal.dateRange || ['2025-01-01', '2025-12-31'];
                fieldValues.fileSystem = newVal.fileSystem || '';
                fieldValues.fileUrl = newVal.fileUrl || '';
                fieldValues.service = newVal.service || '';
            }
        },
        { immediate: true, deep: true }
    );

    // 重置表单
    const resetForm = () => {
        const defaultValues = config.value?.defaultValues || {};
        fieldValues.category = defaultValues.category || '';
        fieldValues.system = defaultValues.system || '';
        fieldValues.db = defaultValues.db || '';
        schemaName.value = defaultValues.schema || '';
        fieldValues.table = defaultValues.table || '';
        fieldValues.range = defaultValues.range || '';
        fieldValues.level = defaultValues.level || '';
        fieldValues.frequency = defaultValues.frequency || '';
        dateRange.value = defaultValues.dateRange || ['', ''];
        fieldValues.fileSystem = defaultValues.fileSystem || '';
        fieldValues.fileUrl = defaultValues.fileUrl || '';
        fieldValues.service = defaultValues.service || '';
        showTips.value = false;
    };

    // 手动提交表单
    const submitForm = () => {
        generateTask();
    };

    // 默认文本内容
    const defaultTexts = {
        // 字段标签
        category: '该资源目录是',
        system: '应用系统',
        db: '来源数据库',
        schema: '模式名称',
        table: '数据表是',
        range: '数据范围',
        level: '数据分级',
        dateRange: '时间范围',
        frequency: '更新频率',
        fileSystem: '文件系统',
        fileUrl: '文件路径',
        service: '服务',

        // 字段前缀
        categoryPrefix: '',
        systemPrefix: '，',
        dbPrefix: '，',
        schemaPrefix: '，',
        tablePrefix: '，',
        rangePrefix: '，',
        levelPrefix: '，',
        dateRangePrefix: '，',
        frequencyPrefix: '，',
        fileSystemPrefix: '，',
        fileUrlPrefix: '，',
        servicePrefix: '，',

        // 字段后缀
        categorySuffix: '类别',
        systemSuffix: '',
        dbSuffix: '',
        schemaSuffix: '',
        tableSuffix: '',
        rangeSuffix: '',
        levelSuffix: '',
        dateRangeSuffix: '',
        frequencySuffix: '。',
        fileSystemSuffix: '，',
        fileUrlSuffix: '。',
        serviceSuffix: '。',

        // 占位符
        categoryPlaceholder: '类别名',
        systemPlaceholder: '系统名',
        dbPlaceholder: '库名',
        schemaPlaceholder: '名称',
        tablePlaceholder: '表名',
        rangePlaceholder: '范围',
        levelPlaceholder: '级别',
        dateRangePlaceholder: '2024-01-01—2024-12-31',
        frequencyPlaceholder: '频率',
        fileSystemPlaceholder: '文件系统',
        fileUrlPlaceholder: '路径',
        servicePlaceholder: '服务',

        // 选择提示
        categorySelectPrompt: '请选择类别',
        systemSelectPrompt: '请选择系统',
        dbSelectPrompt: '请选择数据库',
        schemaSelectPrompt: '请选择模式',
        tableSelectPrompt: '请选择数据表',
        rangeSelectPrompt: '请选择范围',
        levelSelectPrompt: '请选择级别',
        frequencySelectPrompt: '请选择频率',
        fileSystemSelectPrompt: '请选择文件系统',
        fileUrlSelectPrompt: '请选择文件',
        serviceSelectPrompt: '请选择服务',

        // 其他
        emptyTip: '必填项为空，请补充相关信息。',
    };

    // 获取文本内容
    const getFieldText = key => {
        return config.value.texts ? config.value.texts[key] : defaultTexts[key] || '';
    };

    // 判断目录内容是否存在
    const hasCatalogContent = computed(() => {
        // 检查是否有任何字段具有文本内容
        return visibleFields.value.some(field => {
            const hasPrefix = getFieldText(`${field.key}Prefix`) !== '';
            const hasLabel = getFieldText(field.key) !== '';
            const hasSuffix = getFieldText(`${field.key}Suffix`) !== '';

            return hasPrefix || hasLabel || hasSuffix;
        });
    });

    return {
        // 状态
        fieldValues,
        dateRange,
        showTips,
        fileUrl,
        schemaName,

        // 计算属性
        placeholders,
        formatDateRange,
        visibleFields,
        fieldsConfig,
        hasCatalogContent,

        // 方法
        getFieldValue,
        getOptionsForField,
        isFieldRequired,
        checkRequiredFields,
        getFormData,
        generateTask,
        resetForm,
        submitForm,
        getFieldText,
        sureFileUrl,
    };
};

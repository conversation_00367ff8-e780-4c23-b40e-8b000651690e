<template>
    <common-thinkingContentStep :stepTitle="'意图识别与分析'">
        <template #content>
            <!-- ----------插入页面内容 ----------------->

            <!-- 查看表单内容 -->
            <workFlow
                :intentionData="data"
                :labels="tableLabels"
                :workflowClass="'workflow-highlight'"
                :customClass="'intention-analysis-table'"
                :showStepContent="true"
            ></workFlow>

            <!-- ----------插入页面内容 end ----------------->
        </template>
    </common-thinkingContentStep>
</template>
<script setup name="createWorkFlow">
/**
 * 意图识别与分析工具组件
 * 用于展示对用户意图的识别结果和相关的任务分析
 */

import workFlow from '@/components/dialogue/common/workFlow.vue';
const props = defineProps({
    data: {
        type: Object,
        default: () => ({
            intent: '数据采集',
            target: '采集数据到表',
            source: '指标来源',
            flow: ['采集信息工具查询', '采集任务确认', '采集结果展示', '后续任务推荐'],
        }),
    },
    tableLabels: {
        type: Object,
        default: () => ({
            intent: '意图定位',
            target: '目标定位',
            flow: '工作流程',
            source: '数据来源',
        }),
    },
});
watch(
    () => props.data,
    v => {}
);
</script>
<style scoped lang="scss">
.intention-analysis-table {
    margin-bottom: 15px;
}

:deep(.workflow-highlight) {
    font-weight: bold;
}

.step-detail {
    background-color: #f5f7fa;
    padding: 10px 15px;
    border-radius: 4px;

    ul {
        margin: 5px 0 0 20px;
        padding: 0;

        li {
            margin-bottom: 5px;
        }
    }
}
</style>

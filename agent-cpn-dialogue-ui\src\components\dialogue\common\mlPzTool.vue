<template>
    <thinkingContentStep :stepTitle="title">
        <template #titleSlot></template>
        <template #content>
            <!-- 资源目录信息区域 -->
            <div class="catalog-info-container">
                <div class="catalog-info-row">
                    <template v-for="(field, fieldIndex) in formattedFields" :key="fieldIndex">
                        <div
                            class="info-item-wrapper"
                            :class="{ 'required-field': field.required }"
                            v-if="props.resourceInfo.catalogType ? true : field.isShow"
                        >
                            <span class="label-col">
                                <span v-if="field.required" class="required-mark">*</span>
                                {{ field.label }}：
                            </span>
                            <span class="value-col">{{ field.value }}</span>
                        </div>
                    </template>
                </div>
            </div>

            <!-- 详细配置按钮 -->
            <div class="detail-config" v-if="props.other === 'source' && props.resourceInfo.catalogType == 1">
                <el-button type="primary" text @click="handleDetailConfig" class="detail-btn">
                    <el-icon><setting /></el-icon>
                    详细配置
                </el-button>
            </div>
            <div class="task_thinking_btn">
                <!-- <a class="btn0">取消</a> -->
                <a class="btn1" @click="handleEdit">修改</a>
                <a class="btn2" @click.once="handleConfirm">确认</a>
            </div>
        </template>
    </thinkingContentStep>
</template>

<script setup name="mlPzTool">
/**
 * 目录基本信息配置工具组件
 * @description 展示资源目录基本信息
 */
import { defineProps, defineEmits, ref, computed, onMounted, watch } from 'vue';
import { Setting } from '@element-plus/icons-vue';
import thinkingContentStep from '@/components/dialogue/common/thinkingContentStep.vue';
import { getDictionary } from '@/api/system/dict';
import { getDept, getDeptTree } from '@/api/system/dept';
import {
    selectAppSystemDetail,
    selectClassification,
    selectClassificationDetail,
    selectDatabaseDetail,
    selectFileSystemDetail,
} from '@/api/dialogue/data-catalog-util-api';
import { selectServiceDetail } from '@/api/dialogue/data-service-util-api';

// 定义props
const props = defineProps({
    // 组件标题
    title: {
        type: String,
        default: '目录基本信息配置工具',
    },
    // 资源字段配置
    defaultFieldsConfig: {
        type: Array,
        default: () => [],
    },
    fieldsConfig: {
        type: Array,
        default: () => [],
    },
    // 资源数据
    resourceInfo: {
        type: Object,
        default: () => ({}),
    },
    data: {
        type: Object,
        default: () => ({}),
    },
    other: {
        type: String,
        default: () => '',
    },
});

// 定义emit
const emit = defineEmits(['detailConfig', 'edit', 'confirm']);

// 默认资源字段配置
// eslint-disable-next-line vue/no-dupe-keys
const defaultFieldsConfig = [
    { key: 'title', label: '资源标题', required: true, isShow: true },
    { key: 'updateDate', label: '更新日期', required: true, isShow: true },
    { key: 'attributeType', label: '属性分类', required: true, isShow: true },
    { key: 'baseType', label: '基础分类', required: false, isShow: true },
    { key: 'themeType', label: '主题分类', required: false, isShow: true },
    { key: 'department', label: '部门', required: true, isShow: true },
    { key: 'shareType', label: '共享类型', required: true, isShow: true },
    { key: 'shareCondition', label: '共享条件', required: false, isShow: true },
    { key: 'shareReason', label: '不共享理由', required: false, isShow: true },
    { key: 'shareMethod', label: '共享方式', required: false, isShow: true },
    { key: 'shareRange', label: '共享范围', required: false, isShow: true },
    { key: 'shareBasis', label: '共享依据', required: false, isShow: true },
    { key: 'openType', label: '开放类型', required: true, isShow: true },
];

// 字段配置列表
const fieldsList = computed(() => {
    // 优先使用父组件传入的defaultFieldsConfig
    if (props.defaultFieldsConfig && props.defaultFieldsConfig.length > 0) {
        return props.defaultFieldsConfig;
    }
    // 其次使用fieldsConfig
    if (props.fieldsConfig && props.fieldsConfig.length > 0) {
        return props.fieldsConfig;
    }
    // 最后使用组件内默认配置
    return defaultFieldsConfig;
});

// 默认资源数据
const defaultResourceData = {
    title: '2025年人口基本信息统计演示数据',
    updateDate: '每日',
    attributeType: '基础资源信息',
    baseType: '社会信用',
    themeType: '综合政务',
    department: '公安局',
    shareType: '有条件共享',
    shareCondition: '内容',
    shareReason: '理由',
    shareMethod: '-',
    shareRange: '-',
    shareBasis: '-',
    openType: '依申请开放',
};

// 资源数据
const resourceData = ref({});

// 格式化字段列表
const formattedFields = computed(() => {
    return fieldsList.value.map(field => ({
        label: field.label,
        value: resourceData.value[field.key] || '',
        required: field.required,
        isShow: field.isShow,
    }));
});

// 详细配置点击事件
const handleDetailConfig = () => {
    emit('detailConfig');
};

const handleEdit = () => {
    // 触发修改事件
    emit('edit', props.data);
};

/**
 * 点击确认按钮事件
 */
const handleConfirm = () => {
    // 触发确认事件
    emit('confirm', props.data.result ? props.data.result.entity : props.data.entity);
};

//监听父组件传参，更新数据
watch(
    () => props.data,
    async newData => {
        if (newData && newData.result.entity) {
            await makeData();
        }
    },
    { deep: true }
);
watch(
    () => props.resourceInfo,
    async newData => {
        if (newData) {
            await makeData();
        }
    },
    { deep: true }
);
watch(
    () => props.resourceInfo.catalogType,
    async newData => {
        if (newData) {
            await makeConfig();
        }
    },
    { deep: true }
);

// 组件挂载时处理数据
onMounted(() => {
    // 处理props.data中的entity数据
    makeData();
});
const makeConfig = async () => {
    if (props.resourceInfo && Object.keys(props.resourceInfo).length > 0) {
    }
};

const makeData = async () => {
    if (props.data && props.data.result) {
        const entity = props.data.result.entity;
        console.log('entity', entity);
        // 创建映射关系，将后端数据字段映射到组件所需字段
        const dataMapping = {
            title: entity.name || '-', // 企业名称映射为资源标题
            updateDate: await getUpdateCycleText(entity.updateCycle), // 更新周期转换为文本
            attributeType: await getResTypeText(entity.resType), // 资源类型转换为文本
            baseType: (await getBaseTypeText(entity.baseType)) || '-', // 基础分类保持不变
            themeType: await getSubjectTypeText(entity.subjectType), // 主题分类转换为文本
            department: (await getDeptText(entity.deptId)) || '-', // 部门ID
            shareType: await getShareTypeText(entity.shareType), // 共享类型转换为文本
            shareCondition: entity.shareCondition || '-', // 共享条件保持不变
            openType: await getOpenTypeText(entity.openType), // 开放类型转换为文本
            shareMethod: (await getShareWayText(entity.shareWay)) || '-', // 共享方式保持不变
            shareRange: (await getShareRangeText(entity.shareScope)) || '-', // 共享范围保持不变
            shareReason: entity.shareReason || '-', // 共享范围保持不变
            shareBasis: entity.shareBasis || '-', // 共享范围保持不变
        };

        // 合并默认数据和映射后的数据
        resourceData.value = {
            // ...defaultResourceData,
            ...dataMapping,
        };
    }
    // 合并默认值和props传入的resourceInfo
    else if (props.resourceInfo && Object.keys(props.resourceInfo).length > 0) {
        const entity = props.resourceInfo;
        const dataMapping1 = {
            category: await getCatalogTypeText(entity.catalogType),
            system: (await getSystemText(entity.sourceSystemId)) || '-',
            db: (await getDatabaseText(entity.sourceDatabaseId)) || '-',
            file: (await getFileSystemText(entity.sourceFileServerId)) || '-',
            service: (await getServiceText(entity.serviceId)) || '-',
            path: entity.sourceFolderPath || '-',
            schema: entity.sourceSchema || '无',
            table: entity.sourceTableName,
            range: await getDataRangeText(entity.dataRange),
            level: await getDataLevelText(entity.dataLevel),
            dateRange: entity.timeRange ? entity.timeRange[0] + '-' + entity.timeRange[1] : '' || '',
        };
        // 合并默认数据和映射后的数据
        resourceData.value = {
            // ...defaultResourceData,
            ...dataMapping1,
        };
    }
};

const getDictValue = (dictList, value) => {
    let dictValue = '-';
    dictList.forEach(item => {
        if (item.dictKey == value) {
            dictValue = item.dictValue;
        }
    });
    return dictValue;
};

// 辅助函数：转换更新周期为文本
const getUpdateCycleText = async updateCycle => {
    const { data: res } = await getDictionary({ code: 'update_cycle' });
    return getDictValue(res.data, updateCycle) || '-';
};
const getCatalogTypeText = async catalogType => {
    const { data: res } = await getDictionary({ code: 'catalog_type' });
    return getDictValue(res.data, catalogType) || '-';
};
const getDataRangeText = async dataRange => {
    const { data: res } = await getDictionary({ code: 'data_range' });
    return getDictValue(res.data, dataRange) || '-';
};
const getDataLevelText = async dataLevel => {
    const { data: res } = await getDictionary({ code: 'data_level' });
    return getDictValue(res.data, dataLevel) || '-';
};

// 辅助函数：转换资源类型为文本
const getResTypeText = async resType => {
    let resTypeMap = {};
    await getDictionary({ code: 'res_type' }).then(res => {
        let data = res.data.data;
        resTypeMap = data;
    });
    return getDictValue(resTypeMap, resType) || '-';
};

// 辅助函数：转换主题分类为文本
const getSubjectTypeText = async subjectType => {
    let subjectTypeMap = {};
    if (subjectType) {
        await selectClassificationDetail({ id: subjectType }).then(res => {
            let data = res.data.data;
            subjectTypeMap = data;
        });
    }
    return subjectTypeMap.name || '-';
};

const getBaseTypeText = async baseType => {
    let baseTypeMap = {};
    if (baseType) {
        await selectClassificationDetail({ id: baseType }).then(res => {
            let data = res.data.data;
            baseTypeMap = data;
        });
    }
    return baseTypeMap.name || '-';
};
const getDeptText = async deptId => {
    let deptMap = {};
    if (deptId) {
        await getDept(deptId).then(res => {
            let data = res.data.data;
            deptMap = data;
        });
    }
    return deptMap.deptName || '-';
};
const getSystemText = async systemId => {
    let systemMap = {};
    if (systemId) {
        await selectAppSystemDetail({ id: systemId }).then(res => {
            let data = res.data.data;
            systemMap = data;
        });
    }
    return systemMap.name || '-';
};
const getDatabaseText = async databaseId => {
    let databaseMap = {};
    if (databaseId) {
        await selectDatabaseDetail({ id: databaseId }).then(res => {
            let data = res.data.data;
            databaseMap = data;
        });
    }
    return databaseMap.name || '-';
};
const getFileSystemText = async fileSystemId => {
    let fileSystemMap = {};
    if (fileSystemId) {
        await selectFileSystemDetail({ id: fileSystemId }).then(res => {
            let data = res.data.data;
            fileSystemMap = data;
        });
    }
    return fileSystemMap.name || '-';
};
const getServiceText = async serviceId => {
    let serviceMap = {};
    if (serviceId) {
        await selectServiceDetail({ id: serviceId }).then(res => {
            let data = res.data.data;
            serviceMap = data;
        });
    }
    return serviceMap.name || '-';
};
// const getTableText =async tableName => {
//     return databaseMap.name || '-';
// };

// 辅助函数：转换共享类型为文本
const getShareTypeText = async shareType => {
    let shareTypeMap = {};
    await getDictionary({ code: 'share_type' }).then(res => {
        let data = res.data.data;
        shareTypeMap = data;
    });
    return getDictValue(shareTypeMap, shareType) || '-';
};

// 辅助函数：转换开放类型为文本
const getOpenTypeText = async openType => {
    let openTypeMap = {};
    await getDictionary({ code: 'open_type' }).then(res => {
        let data = res.data.data;
        openTypeMap = data;
    });
    return getDictValue(openTypeMap, openType) || '-';
};

const getShareWayText = async shareWay => {
    let shareWayMap = {};
    await getDictionary({ code: 'share_way' }).then(res => {
        let data = res.data.data;
        shareWayMap = data;
    });
    return getDictValue(shareWayMap, shareWay) || '-';
};

const getShareRangeText = async shareRange => {
    let shareRangeMap = {};
    await getDictionary({ code: 'share_scope' }).then(res => {
        let data = res.data.data;
        shareRangeMap = data;
    });
    return getDictValue(shareRangeMap, shareRange) || '-';
};
</script>

<style scoped lang="scss">
.catalog-info-container {
    background-color: #f8f9fb;
    border-radius: 4px;
    padding: 18px 20px;
    margin-bottom: 16px;
}

.catalog-info-row {
    display: flex;
    flex-wrap: wrap;
    margin: -8px; // 负边距抵消子元素的margin

    .info-item-wrapper {
        display: flex;
        width: calc(50% - 16px); // 考虑到内边距
        align-items: flex-start;
        margin: 8px;

        .label-col {
            min-width: 110px;
            max-width: 150px;
            color: #606266;
            font-weight: 400;
            text-align: right;
            padding-right: 10px;
            font-size: 14px;
            flex-shrink: 0;

            .required-mark {
                color: #f56c6c;
                margin-right: 2px;
            }
        }

        .value-col {
            flex: 1;
            color: #303133;
            font-size: 14px;
            word-break: break-all;
        }
    }

    .required-field {
        .label-col {
            color: #303133;
            font-weight: 500;
        }
    }
}

.detail-config {
    display: flex;
    padding: 8px 0;

    .detail-btn {
        font-size: 14px;
        color: #409eff;
        background-color: #f4f9fe;
        border: 1px solid #d9ecff;
        border-radius: 6px;
        padding: 9px 12px;
        .el-icon {
            margin-right: 4px;
        }
    }
}
</style>

<template>
    <div class="thinking-content-step">
        <div class="top-container">
            <div class="icon-wrapper">
                <el-icon class="step-icon"><Check /></el-icon>
            </div>
            <div class="content-title" @click="toggleCollapse">
                <span class="content-title-text">{{ finalStepTitle }}</span>
                <el-icon v-if="isShowExpand" class="collapse-icon" :class="{ 'is-collapsed': isCollapsed }">
                    <ArrowDown />
                </el-icon>
            </div>
        </div>
        <div class="step-line" v-if="!showLine"></div>

        <div class="step-content">
            <div class="step-content-body" v-show="!isCollapsed">
                <!-- <div v-if="finalExtraText" class="extra-text">{{ finalExtraText }}</div> -->
                <div v-if="finalExtraText">
                    <XMarkdown :markdown="finalExtraText" class="self-markdown-body" />
                </div>
                <slot name="content"></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
import XMarkdown from './MarkDown.vue';
defineOptions({
    name: 'ThinkingContentStep',
});

/**
 * @description 思维链步骤组件
 */
import { ref, computed } from 'vue';
const props = defineProps({
    /**
     * @description 是否显示连接线
     */
    lastStep: {
        type: Boolean,
        default: false,
    },
    stepTitle: {
        type: String,
        default: '步骤',
    },
    isShowExpand: {
        type: Boolean,
        default: true,
    },
    extraText: {
        type: String,
        default: '',
    },
    data: {
        type: Object,
        default: () => ({}),
    },
    /**
     * @description 是否默认折叠
     */
    defaultCollapsed: {
        type: Boolean,
        default: false,
    },
});

const isCollapsed = ref(props.defaultCollapsed);
const finalExtraText = computed(() => props.data.result?.extraText ?? props.extraText);
const finalStepTitle = computed(() => props.data.result?.stepTitle ?? props.stepTitle);
const showLine = computed(() => (props.data.result?.lastStep || props.lastStep ? true : false));

/**
 * @description 切换折叠状态
 */
const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value;
};
</script>

<style scoped lang="scss">
.self-markdown-body {
    display: flex;
    flex-direction: column;
    gap: 12px;
}
.thinking-content-step {
    display: flex;
    align-items: flex-start;
    position: relative;
    flex-direction: column;
    margin-left: 6px;
    .top-container {
        display: flex;
        align-items: center;
        margin-right: 12px;
        position: relative;
    }

    .icon-wrapper {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #1570ef;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1;
        margin-top: 2px;
        .step-icon {
            color: #fff;
            font-size: 14px;
        }
    }

    .step-content {
        font-size: 14px;
        position: relative;
        padding: 0 16px;
        color: #475467;
        margin-top: 12px;
        margin-left: 10px;
        width: 100%;
        // &::after {
        //     content: '';
        //     position: absolute;
        //     top: 10px;
        //     left: 7px;
        //     height: 100%;
        //     border: 1px dashed #1570ef;
        //     z-index: v-bind('showLine ? -1 : 0');
        // }
    }
    .step-line {
        height: 100%;
        position: absolute;
        border: 1px dashed #1570ef;
        left: 6px;
        top: 10px;
        z-index: 0;
    }
    .content-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-left: 10px;
        cursor: pointer;
        color: #1570ef;
        font-weight: 500;
        background-color: #eff8ff;
        border-radius: 4px;
        padding: 16px;
        height: 30px;
        font-size: 16px;
        .collapse-icon {
            font-size: 14px;
            transition: transform 0.3s ease;
            margin-left: 8px;
            color: #98a2b3;

            &.is-collapsed {
                transform: rotate(-180deg);
            }
        }
    }
    .extra-text {
        padding: 2px 2px;
    }
}
</style>

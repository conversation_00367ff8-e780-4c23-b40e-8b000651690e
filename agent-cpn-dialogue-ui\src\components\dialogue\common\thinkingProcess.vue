<script setup>
import { isDeepThink, hasActiveConnections } from '@/components/dialogue/store/input.js';
import { watch } from 'vue';
const props = defineProps({
    /**
     * 思考内容
     */
    thinkingContent: {
        type: [String, Object],
        default: '',
    },
    /**
     * 思考过程状态：0-正在思考, 1-已完成思考
     */
    processState: {
        type: [Number, Object],
        default: 0,
    },
    isDeepThink: {
        type: Boolean,
        default: true,
    },
});

const statusValue = computed(() => {
    return props.processState === 0 ? 'thinking' : 'end';
});

const DeepThink = computed(() => {
    return props.isDeepThink;
});
</script>

<template>
    <Thinking
        :status="statusValue"
        v-model="DeepThink"
        :content="thinkingContent"
        button-width="200px"
        max-width="100%"
        class="Thinking"
    >
        <template #status-icon="{ status }">
            <Icon name="rock" v-if="status === 'end'" />
            <div v-if="status === 'thinking'" class="thinking-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </template>
        <template #content="{ content }">
            <Bubble :content="content" is-markdown max-width="100%">
                <template #avatar>
                    <el-avatar v-if="false" />
                </template>
            </Bubble>
        </template>
        <template #label="{ status }">
            <div class="text-#667085 title">
                <span v-if="status === 'thinking'">{{ '思考中' }}</span>
                <span v-if="status === 'end'">{{ '已完成思考' }}</span>
            </div>
        </template>

        <template #arrow>
            <el-icon class="text-#667085"><ArrowDown /></el-icon>
        </template>
    </Thinking>
</template>

<style scoped lang="scss">
.thinking-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;

    span {
        display: inline-block;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: #409eff;
        animation: thinking 1.4s infinite ease-in-out both;

        &:nth-child(1) {
            animation-delay: -0.32s;
        }

        &:nth-child(2) {
            animation-delay: -0.16s;
        }
    }
}
:deep() {
    .el-bubble-content {
        background-color: #f0f2f5 !important;
    }
}
:deep(.el-bubble) {
    gap: 0;
}
.title {
    font-size: 14px;
}
@keyframes thinking {
    0%,
    80%,
    100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}
</style>

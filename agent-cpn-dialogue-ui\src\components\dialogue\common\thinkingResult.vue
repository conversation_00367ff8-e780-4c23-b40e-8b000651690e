<template>
    <div class="thinking-result" @click="handleShowSystemInfo">
        <div class="icon-line-container">
            <div class="icon-wrapper">
                <el-icon class="check-icon"><Check /></el-icon>
            </div>
        </div>
        <span class="result-text">{{ data.result.message }}</span>
        <el-icon class="arrow-icon"><ArrowRight /></el-icon>
    </div>
</template>

<script setup name="thinkingResult">
/**
 * @description 任务数据源解析结果显示组件
 */
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
const { setRightPanel, hideRightPanel } = useRightPanelStore();

const props = defineProps({
    /**
     * @description 显示的消息文本
     */
    data: {
        type: Object,
        default: () => ({ message: '调用信息工具成功' }),
    },
    /**
     * @description 是否选中
     */
    selected: {
        type: <PERSON>olean,
        default: false,
    },
});

/**
 * 点击消息时显示系统信息
 */
const handleShowSystemInfo = () => {
    setRightPanel(props.data.stepName, {
        formValue: props.resourceData,
        title: '后台信息',
    });
};
</script>

<style scoped lang="scss">
.thinking-result {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f2f4f7;
    padding: 0 6px;
    margin-bottom: 8px;
    min-height: 40px;
    color: #475467;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    z-index: 2;
    .icon-line-container {
        display: flex;
        align-items: center;
        margin-right: 8px;
    }
    .icon-wrapper {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: #1570ef;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1;
        .check-icon {
            color: #fff;
            font-size: 14px;
            height: 16px;
            width: 16px;
        }
    }
    .result-text {
        color: #475467;
        font-size: 15px;
        font-weight: 500;
        padding: 0 8px;
        background: none;
        border-radius: 0;
        height: 40px;
        display: flex;
        align-items: center;
    }
    .arrow-icon {
        font-size: 18px;
        margin-left: 12px;
        transition: color 0.2s;
    }
}
</style>

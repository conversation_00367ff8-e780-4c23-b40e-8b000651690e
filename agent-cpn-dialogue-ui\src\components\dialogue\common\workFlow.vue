<template>
    <div class="workflow-container" :class="customClass">
        <!-- 意图和目标展示区域 -->
        <div v-if="showIntention || showTarget" class="intention-section">
            <table class="dl_table_style">
                <tbody>
                    <tr>
                        <template v-for="field in displayFields" :key="field.key">
                            <template v-if="intentionData[field.key]">
                                <td class="common_label_title_r td_width0">{{ labels[field.key] }}：</td>
                                <td>
                                    <p class="td_block_box">
                                        <span class="td_block" :class="field.class">
                                            {{ intentionData[field.key] }}
                                        </span>
                                    </p>
                                </td>
                            </template>
                        </template>
                        <td class="common_label_title_r td_width0"></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 工作流程展示区域 -->
        <div v-if="showWorkflow && intentionData.flow?.length" class="workflow-section">
            <table class="dl_table_style">
                <tbody>
                    <tr>
                        <td class="common_label_title_r td_width0">{{ labels.flow }}：</td>
                        <td colspan="5">
                            <p class="td_block_box">
                                <template v-for="(step, index) in intentionData.flow" :key="index">
                                    <span
                                        class="td_block"
                                        :class="[
                                            workflowClass,
                                            { td_color: useDefaultColor },
                                            { 'active-step': index === activeStepIndex },
                                        ]"
                                        @click="onStepClick(index)"
                                    >
                                        {{ step }}
                                    </span>
                                    <img v-if="index < intentionData.flow.length - 1" :src="arrowImgSrc" />
                                </template>
                            </p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 额外信息区域 -->
        <div v-if="hasAdditionalInfo" class="additional-info-section">
            <table class="dl_table_style">
                <tbody>
                    <tr v-if="intentionData.additionalInfo">
                        <td class="common_label_title_r td_width0">{{ labels.additionalInfo || '附加信息' }}：</td>
                        <td colspan="5">
                            <p class="td_block_box">
                                <span class="td_block">{{ intentionData.additionalInfo }}</span>
                            </p>
                        </td>
                    </tr>
                    <slot name="extraRows"></slot>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script setup>
defineOptions({
    name: 'workFlow',
});

/**
 * @description 用于展示对话意图、目标定位和工作流程等信息，支持工作流点击交互
 */
import { computed, ref } from 'vue';
import { publicPath } from '@/components/dialogue/store/main.js';
const props = defineProps({
    intentionData: {
        type: Object,
        required: true,
        default: () => ({
            intent: '数据采集',
            target: '采集数据到数据表',
            source: '数据来源',
            flow: ['数据采集', '采集任务生成工具', '采集结果展示', '后续任务推荐'],
            additionalInfo: '',
            stepContents: {},
        }),
    },
    labels: {
        type: Object,
        default: () => ({
            intent: '意图定位',
            target: '目标定位',
            source: '数据来源',
            flow: '工作流程',
            additionalInfo: '附加信息',
        }),
    },
    showIntention: { type: Boolean, default: true },
    showTarget: { type: Boolean, default: true },
    showWorkflow: { type: Boolean, default: true },
    intentionClass: { type: [String, Array, Object], default: '' },
    targetClass: { type: [String, Array, Object], default: '' },
    workflowClass: { type: [String, Array, Object], default: '' },
    useDefaultColor: { type: Boolean, default: true },
    arrowImgSrc: { type: String, default: publicPath + '/static/dialogue/tdjt.png' },
    customClass: { type: [String, Array, Object], default: '' },
    defaultActiveStep: { type: Number, default: null },
});

const emit = defineEmits(['step-click']);

// 用于显示的字段配置
const displayFields = computed(() => [
    { key: 'intent', class: props.intentionClass },
    { key: 'target', class: props.targetClass },
    { key: 'source', class: props.targetClass },
]);

// 计算当前是否有附加信息要显示
const hasAdditionalInfo = computed(() => !!props.intentionData.additionalInfo);

// 当前激活的步骤索引
const activeStepIndex = ref(props.defaultActiveStep);

// 当前活动步骤的内容
const activeStepContent = computed(() => {
    if (activeStepIndex.value === null) return '';
    return props.intentionData.stepContents?.[activeStepIndex.value] || '';
});

// 步骤点击事件
const onStepClick = index => {
    activeStepIndex.value = index;
    emit('step-click', {
        index,
        name: props.intentionData.flow[index],
        content: activeStepContent.value,
    });
};
</script>

<style scoped lang="scss">
.workflow-container {
    width: 100%;
    margin-bottom: 15px;

    .intention-section,
    .workflow-section,
    .additional-info-section {
        margin-bottom: 10px;
    }

    .td_block {
        cursor: default;
        &.active-step {
            font-weight: bold;
        }
    }

    .workflow-section .td_block {
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
            background-color: #ecf5ff;
            transform: scale(1.05);
        }
    }
}
</style>

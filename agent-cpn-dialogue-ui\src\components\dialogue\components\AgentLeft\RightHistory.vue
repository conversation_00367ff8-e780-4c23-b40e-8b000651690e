<template>
    <div class="history-filter-container">
        <!-- 筛选选项卡 -->
        <div class="filter-tabs">
            <div class="tab-item" :class="{ active: activeTab === 'all' }" @click="setActiveTab('all')">全部</div>
            <div class="tab-item" :class="{ active: activeTab === 'task' }" @click="setActiveTab('task')">
                任务生成对话
            </div>
            <div class="tab-item" :class="{ active: activeTab === 'ask' }" @click="setActiveTab('ask')">
                问政查询对话
            </div>
        </div>

        <!-- 时间筛选 -->
        <div class="time-filter">
            <div
                v-for="(item, index) in timeFilters"
                :key="index"
                class="time-item"
                :class="{ active: activeTimeFilter === item.value }"
                @click="setTimeFilter(item.value)"
            >
                {{ item.label }}
            </div>
        </div>

        <!-- 对话列表 -->
        <div class="dialogue-list">
            <div v-for="(item, index) in filteredDialogueList" :key="index" class="dialogue-item">
                <el-checkbox v-model="item.checked" @change="handleCheckChange"></el-checkbox>
                <div class="dialogue-icon">
                    <Icon :name="item.type === 'task' ? 'cj' : 'wzb'" size="18" />
                </div>
                <div class="dialogue-content">
                    <div class="dialogue-title">{{ item.title }}</div>
                    <div class="dialogue-tags" v-if="item.tags && item.tags.length">
                        <span v-for="(tag, tIndex) in item.tags" :key="tIndex" class="tag">{{ tag }}</span>
                    </div>
                </div>
                <div class="dialogue-info">
                    <div class="dialogue-type">智能问答</div>
                    <div class="dialogue-actions">
                        <el-dropdown trigger="click" @command="command => handleCommand(command, index)">
                            <span class="el-dropdown-link">
                                <i class="el-icon-more"></i>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu>
                                    <el-dropdown-item command="view">查看</el-dropdown-item>
                                    <el-dropdown-item command="delete">删除</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>
                </div>
            </div>

            <div v-if="filteredDialogueList.length === 0" class="empty-list">暂无符合条件的对话记录</div>
        </div>

        <!-- 底部操作按钮 -->
        <div class="action-buttons">
            <div class="selected-count" v-if="selectedCount > 0">已选择 {{ selectedCount }} 条</div>
            <div class="buttons">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确定</el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';

defineOptions({
    name: 'HistoryFilter',
});

// 定义事件
const emit = defineEmits(['cancel', 'confirm']);

// 选项卡状态
const activeTab = ref('all');
// 时间筛选状态
const activeTimeFilter = ref('1day');
// 对话列表数据
const dialogueList = ref([]);
// 每个对话的类型任务生成对话的type是task，文书查询对话的type是ask

// 时间筛选选项
const timeFilters = [
    { label: '1天', value: '1day' },
    { label: '3天', value: '3day' },
    { label: '5天', value: '5day' },
    { label: '7天', value: '7day' },
    { label: '更早', value: 'earlier' },
];

// 模拟获取数据
const fetchDialogueList = () => {
    // 模拟数据，实际应从API获取
    dialogueList.value = [
        {
            id: '1',
            title: '帮我查询佛山一市五区的政务服务业务办理量',
            type: 'ask',
            tags: [],
            timestamp: Date.now() - 3600000, // 1小时前
            checked: false,
        },
        {
            id: '2',
            title: '帮我查询佛山一市五区的政务服务业务办理量',
            type: 'task',
            tags: ['AI生成模板'],
            timestamp: Date.now() - 86400000, // 1天前
            checked: false,
        },
        {
            id: '3',
            title: '帮我查询佛山一市五区的政务服务业务办理量',
            type: 'ask',
            tags: [],
            timestamp: Date.now() - 86400000 * 2, // 2天前
            checked: false,
        },
        {
            id: '4',
            title: '帮我查询佛山一市五区的政务服务业务办理量',
            type: 'task',
            tags: [],
            timestamp: Date.now() - 86400000 * 4, // 4天前
            checked: false,
        },
        {
            id: '5',
            title: '帮我查询佛山一市五区的政务服务业务办理量',
            type: 'ask',
            tags: [],
            timestamp: Date.now() - 86400000 * 6, // 6天前
            checked: false,
        },
        {
            id: '6',
            title: '帮我查询佛山一市五区的政务服务业务办理量',
            type: 'task',
            tags: [],
            timestamp: Date.now() - 86400000 * 8, // 8天前
            checked: false,
        },
    ];
};

// 根据过滤条件过滤对话列表
const filteredDialogueList = computed(() => {
    let filtered = [...dialogueList.value];

    // 按类型过滤
    if (activeTab.value !== 'all') {
        filtered = filtered.filter(item => item.type === activeTab.value);
    }

    // 按时间过滤
    const now = Date.now();
    if (activeTimeFilter.value === '1day') {
        filtered = filtered.filter(item => now - item.timestamp <= 86400000);
    } else if (activeTimeFilter.value === '3day') {
        filtered = filtered.filter(item => now - item.timestamp <= 86400000 * 3);
    } else if (activeTimeFilter.value === '5day') {
        filtered = filtered.filter(item => now - item.timestamp <= 86400000 * 5);
    } else if (activeTimeFilter.value === '7day') {
        filtered = filtered.filter(item => now - item.timestamp <= 86400000 * 7);
    }
    // 'earlier' 不需要额外过滤，显示所有符合类型的记录

    return filtered;
});

// 计算已选择的数量
const selectedCount = computed(() => {
    return filteredDialogueList.value.filter(item => item.checked).length;
});

// 获取已选择的对话
const getSelectedItems = () => {
    return dialogueList.value.filter(item => item.checked);
};

// 设置当前选项卡
const setActiveTab = tab => {
    activeTab.value = tab;
};

// 设置时间过滤条件
const setTimeFilter = filter => {
    activeTimeFilter.value = filter;
};

// 处理复选框变更
const handleCheckChange = () => {
    // 可以在这里添加额外的逻辑
};

// 处理下拉菜单命令
const handleCommand = (command, index) => {
    const item = filteredDialogueList.value[index];

    if (command === 'view') {
        // 查看对话详情
        ElMessage.info(`查看对话: ${item.title}`);
    } else if (command === 'delete') {
        // 删除对话
        ElMessage.success(`删除对话: ${item.title}`);
        // 实际应用中应该调用API删除对话
        dialogueList.value = dialogueList.value.filter(i => i.id !== item.id);
    }
};

// 取消按钮处理
const handleCancel = () => {
    emit('cancel');
};

// 确认按钮处理
const handleConfirm = () => {
    const selectedItems = getSelectedItems();
    emit('confirm', selectedItems);
};

// 生命周期钩子
onMounted(() => {
    fetchDialogueList();
});
</script>

<style lang="scss" scoped>
.history-filter-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 16px;

    .filter-tabs {
        display: flex;
        border-bottom: 1px solid #e6e6e6;
        margin-bottom: 16px;

        .tab-item {
            padding: 8px 16px;
            margin-right: 16px;
            cursor: pointer;
            position: relative;
            font-size: 14px;

            &.active {
                color: #409eff;
                font-weight: 500;

                &:after {
                    content: '';
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                    width: 100%;
                    height: 2px;
                    background: #409eff;
                }
            }
        }
    }

    .time-filter {
        display: flex;
        margin-bottom: 16px;

        .time-item {
            padding: 4px 12px;
            margin-right: 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;

            &.active {
                background-color: #ecf5ff;
                color: #409eff;
                border: 1px solid #d9ecff;
            }
        }
    }

    .dialogue-list {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 16px;

        .dialogue-item {
            display: flex;
            align-items: flex-start;
            padding: 12px 8px;
            border-bottom: 1px solid #f0f0f0;

            .el-checkbox {
                margin-right: 8px;
                margin-top: 3px;
            }

            .dialogue-icon {
                margin-right: 8px;
                display: flex;
                align-items: center;
            }

            .dialogue-content {
                flex: 1;
                overflow: hidden;

                .dialogue-title {
                    font-size: 14px;
                    margin-bottom: 4px;
                    color: #333;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .dialogue-tags {
                    display: flex;
                    flex-wrap: wrap;

                    .tag {
                        background-color: #f5f7fa;
                        color: #909399;
                        padding: 0 6px;
                        height: 20px;
                        line-height: 20px;
                        font-size: 12px;
                        border-radius: 2px;
                        margin-right: 4px;
                        margin-bottom: 4px;
                    }
                }
            }

            .dialogue-info {
                display: flex;
                flex-direction: column;
                align-items: flex-end;

                .dialogue-type {
                    font-size: 12px;
                    color: #909399;
                    margin-bottom: 4px;
                }
            }
        }

        .empty-list {
            text-align: center;
            color: #909399;
            padding: 20px 0;
        }
    }

    .action-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 16px;
        border-top: 1px solid #e6e6e6;

        .selected-count {
            font-size: 14px;
            color: #606266;
        }

        .buttons {
            display: flex;
            gap: 12px;
        }
    }
}
</style>

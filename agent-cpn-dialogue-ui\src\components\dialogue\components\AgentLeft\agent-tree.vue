<template>
    <div class="container_agent_left" :class="isTreeShow ? 'agent_tree_hide' : ''">
        <div class="agent_tree" :class="{ collapsed: isTreeShow }">
            <!-- logo -->
            <div class="agent_logo" title="数据治理智能体" v-if="showHeader">
                <img :src="`${publicPath}/static/dialogue/indexlogo.png`" alt="logo" />
                <span>数据治理智能体</span>
            </div>
            <div
                class="new-chat"
                title="新对话"
                :class="{ active: clickActive }"
                @click="createNewDialogue"
                @mouseenter="newChatActive = true"
                @mouseleave="newChatActive = false"
            >
                <SvgIcon
                    name="xinduihua"
                    size="24"
                    class="mr-6"
                    :color="newChatActive || clickActive ? '#fff' : '#1570ef'"
                ></SvgIcon>
                <span class="font-bold">新对话</span>
            </div>
            <!-- <div class="gzt" :class="{ active: isWorkbenchActive }" title="工作台" @click="navigateToWorkbench">
                <Icon name="gzt" size="24" class="mr-6"></Icon>
                <span class="font-bold">工作台</span>
            </div> -->
            <!-- 菜单列表 -->
            <ul class="agent_tree_list">
                <li
                    v-for="(item, index) in menuList"
                    :key="index"
                    class="menu-item flex-center"
                    @mouseenter="handleMouseEnter(item, index)"
                    @mouseleave="handleMouseLeave"
                >
                    <Icon :name="item.icon" size="22" class="itemChild-icon mr-6"></Icon>
                    <div class="list-text">
                        {{ item.name }}
                    </div>

                    <!-- 右侧子项菜单，在hover时显示 -->
                    <div
                        v-if="item.child && item.child.length > 0"
                        class="tree_children_box"
                        :class="[
                            `agentchildtree${index + 1}`,
                            {
                                'show-child-menu': hoveredIndex === index || selectedIndex === index,
                            },
                            isTreeShow ? 'collapsed-menu' : '',
                        ]"
                    >
                        <div class="tree_children">
                            <div
                                class="list_tree_children"
                                v-for="(itemChild, childIndex) in item.child"
                                :key="childIndex"
                                @click="selectChildItem(item, index, itemChild, childIndex)"
                            >
                                <small class="list_tree_children_icon">
                                    <Icon :name="itemChild.icon" class="itemChild-icon"></Icon>
                                </small>
                                <div class="list_tree_children_word">
                                    <h4>{{ itemChild.childName }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>

            <!-- 历史记录 -->
            <HistoryRecord :is-tree-collapsed="isTreeShow" />
        </div>
    </div>
</template>

<script setup>
/**
 * 树形菜单组件
 * 提供完整的树形导航和历史记录功能
 * 支持鼠标悬停时显示子菜单
 */
import { ref, onMounted, onUnmounted, reactive, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import eventBus from '@/utils/bus.js';
import { isTreeShow } from '@/components/dialogue/store/main';
import HistoryRecord from './HistoryRecord.vue';
import { menuList } from '@/components/dialogue/store/input';
import { useSessionStore } from '@/components/dialogue/store/modules/session';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
import { publicPath } from '@/components/dialogue/store/main.js';
const { selectChildItem } = useDialogInput();

const sessionStore = useSessionStore();
const rightPanelStore = useRightPanelStore();
const router = useRouter();
const route = useRoute();

defineOptions({
    name: 'agent-tree',
});

const props = defineProps({
    collapsed: {
        type: Boolean,
        default: false,
    },
    showHeader: {
        type: Boolean,
        default: true,
    },
});

/**
 * 当前选中的菜单索引
 */
const selectedIndex = ref(null);

/**
 * 当前鼠标悬停的菜单索引
 */
const hoveredIndex = ref(null);

/**
 * 新对话按钮悬停状态
 */
const newChatActive = ref(false);

/**
 * 判断工作台按钮是否高亮
 */
const isWorkbenchActive = computed(() => {
    return route.name === 'workbench';
});
const clickActive = computed(() => {
    return route.name === 'chat';
});
/**
 * 创建新对话
 */
const createNewDialogue = () => {
    // 关闭右侧面板
    rightPanelStore.hideRightPanel();

    // 清空当前选中会话信息 跳转默认会话页面

    // 创建会话, 跳转到默认聊天
    sessionStore.createSessionBtn();
    sessionStore.requestSessionList();
};

/**
 * 导航到工作台
 */
const navigateToWorkbench = () => {
    // router.push({
    //     path: '/chat',
    //     query: { type: '工作台' },
    // });
};

/**
 * 处理鼠标进入菜单项事件
 * @param {Object} item - 菜单项数据
 * @param {Number} index - 菜单项索引
 */
const handleMouseEnter = (item, index) => {
    console.log('撒打算');
    hoveredIndex.value = index;
};

/**
 * 处理鼠标离开菜单项事件
 */
const handleMouseLeave = () => {
    hoveredIndex.value = null;
};

// 监听全局事件
onMounted(async () => {
    // 获取会话列表
});

onUnmounted(() => {
    // 清理事件监听器
    eventBus.off('tree-collapsed-change');
});
</script>

<style lang="scss" scoped>
.container_agent_left {
    height: 100%;
    .agent_tree {
        height: 100%;
        width: 250px;
        position: relative;
        transition: width 0.3s;
        .new-chat {
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            border: 1px solid #1570ef;
            cursor: pointer;
            margin-bottom: 6px;
            transition: all 0.3s ease;

            span {
                font-size: 16px;
                color: #1570ef;
            }

            &:hover {
                background-color: #1570ef;
                span {
                    color: #fff;
                }
            }

            &.active {
                background-color: #1570ef;
                span {
                    color: #fff;
                }

                .svg-icon {
                    color: #fff !important;
                }
            }
        }
        .gzt {
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            cursor: pointer;
            margin-bottom: 6px;
            color: #475767;
            transition: all 0.3s ease;

            span {
                font-size: 16px;
            }

            &:hover {
                background-color: #f5f7fa;
            }

            &.active {
                background-color: #f5f7fa;
                color: #1570ef;

                span {
                    color: #1570ef;
                }
            }
        }
        &.collapsed {
            width: 60px;

            .agent_logo {
                justify-content: center;
                margin-right: 4px;
                padding: 0;
                img {
                    margin-right: 0;
                }

                span {
                    display: none;
                }
            }

            .new-chat,
            .gzt {
                justify-content: center;
                border-radius: 20px;
                max-width: 40px;
                .mr-6 {
                    margin-right: 0 !important;
                }

                span {
                    display: none;
                }
            }

            .new-chat {
                margin-left: 2px;
                &:hover {
                    background-color: #1570ef;
                    .svg-icon {
                        color: #fff !important;
                    }
                }
            }

            .gzt {
                &:hover {
                    background-color: #fff;
                }
            }

            .agent_gzt {
                justify-content: center;

                b {
                    display: none;
                }

                span {
                    margin-right: 0;
                }
            }

            .agent_tree_list li {
                justify-content: center;

                .mr-6 {
                    margin-right: 0;
                }

                div.list-text {
                    display: none;
                }

                span {
                    margin-right: 0;
                }
            }
        }
    }
}
.menu-item {
    // padding-left: 40px;
}
.agent_logo {
    height: 60px;
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #333;

    img {
        width: 38px;
        height: 38px;
        margin-right: 8px;
    }
}

.agent_gzt {
    height: 40px;
    display: flex;
    align-items: center;
    cursor: pointer;
    justify-content: center;
    span {
        width: 20px;
        height: 20px;
        margin-right: 10px;
    }
}

.agent_tree_list {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
        height: 40px;
        display: flex;
        align-items: center;
        padding-left: 20px;
        cursor: pointer;
        position: relative;

        span {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-position: center;
            background-repeat: no-repeat;
            background-size: contain;
        }

        .list-text {
            font-size: 14px;
            font-weight: 3600;
            margin: 0;
            color: #475467;
        }
    }
}

.tree_children {
    border-radius: 4px;
    padding: 5px 0;
}

.list_tree_children {
    padding: 12px 20px;
    display: flex;
    align-items: center;
    cursor: pointer;

    &:hover {
        background-color: #f5f7fa;
    }

    .list_tree_children_icon {
        width: 24px;
        height: 24px;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .list_tree_children_word {
        h4 {
            font-size: 14px;
            font-weight: normal;
            margin: 0;
            color: #333;
        }
    }
}

// 针对智能采集到智能治理的特定样式
.agentchildtree7 {
    // 智能治理的子菜单样式
}
</style>

<template>
    <common-thinkingContentStep :stepTitle="'指标数据导入结果'">
        <template #content>
            <div class="import-result-container">
                <div class="top-title">已为您导入指标信息：</div>
                <div class="result-content">
                    <div class="result-summary">
                        <p class="summary-text" v-html="importSummary"></p>
                    </div>
                </div>
                <div class="bottom-title">希望以上内容对您有所帮助！</div>
            </div>
        </template>
    </common-thinkingContentStep>
</template>

<script setup>
import { watch, ref } from 'vue';
const props = defineProps({
    // 指标数据数组
    // 每个指标对象的结构：
    // - title: 指标标题
    // - value: 指标数值
    // - unit: 单位
    // - icon: 图标名称(字符串)或组件引用
    // - color: 颜色值(十六进制、RGB或RGBA)，影响数值和图标颜色
    data: {
        type: Object,
        default: () => {},
    },
});
// 定义组件选项
defineOptions({
    name: 'ImportResult',
});
/**
 * 导入摘要信息
 */
const importSummary = ref(
    '推进应用一体化平台与高频业务系统对接、数据共享，上线以来累计<strong>28个部门</strong><strong>49个业务系统</strong>对接应用政务中台能力，归集全市业务数据近<strong>1.14亿件</strong>，总访问量达<strong>25.11亿次</strong>，累计开通账户<strong>3.97万户</strong>，在用实名账户<strong>9978个</strong>，创建流程模板数<strong>23个</strong>，事项应用流程<strong>204563个</strong>，使用表单<strong>3339个</strong>，预约取号<strong>1404.38万次</strong>，办件取号<strong>4102万次</strong>，调用电子证照<strong>3971万次</strong>，产生物流订单<strong>6.14万单</strong>，实现政务服务集约化、一体化运行。'
);
watch(
    () => props.data,
    () => {
        importSummary.value = props.data.result.result;
    },
    {
        immediate: true,
        deep: true,
    }
);
</script>

<style scoped lang="scss">
.import-result-container {
    padding: 24px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.top-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
}

.result-content {
    margin-bottom: 20px;

    .result-summary {
        background-color: #f7f8fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #eaedf1;

        .summary-text {
            line-height: 1.8;
            color: $text-primary;
            margin: 0;
            font-size: 14px;
            text-indent: 2em;
        }
    }
}

.bottom-title {
    font-size: 14px;
    color: $text-prompt;
    margin-top: 16px;
}
</style>

<template>
    <common-thinkingContentStep :stepTitle="titleConfig.stepTitle">
        <template #content>
            <div class="data-ask-container">
                <!-- 顶部操作区 -->
                <div class="operation-bar" v-if="operationConfig.showOperationBar">
                    <div class="left-area"></div>
                    <div class="right-area">
                        <el-button v-if="operationConfig.showExportButton" type="primary" plain @click="handleExport">
                            {{ operationConfig.exportButtonText }}
                        </el-button>
                        <el-button v-if="operationConfig.showAnalyzeButton" type="primary" @click="handleAnalyze">
                            {{ operationConfig.analyzeButtonText }}
                        </el-button>
                    </div>
                </div>

                <!-- 表格展示区域 -->
                <div class="table-container">
                    <div class="display-options" v-if="displayConfig.showOptions">
                        <span>{{ displayConfig.optionsLabel }}：</span>
                        <el-radio-group v-model="displayConfig.defaultType" size="small">
                            <el-radio-button
                                v-for="option in displayConfig.options"
                                :key="option.value"
                                :label="option.value"
                            >
                                {{ option.label }}
                            </el-radio-button>
                        </el-radio-group>
                    </div>

                    <!-- 无数据返回 -->
                    <div v-if="displayConfig.defaultType === 'null'" class="indicator-card">
                        <div class="card-title">{{ indicatorConfig.indicatorName }}</div>
                        <div class="card-content">无法查询到数据，请点击查看详情</div>
                    </div>

                    <!-- 数据指标 -->
                    <div v-if="displayConfig.defaultType === 'indicator'" class="indicator-card">
                        <div class="card-title">{{ indicatorConfig.indicatorName }}</div>
                        <div class="card-content">
                            <div class="card-value">{{ indicatorConfig.indicatorValue }}</div>
                            <div class="card-unit">{{ indicatorConfig.indicatorUnit }}</div>
                            <div class="card-icon" :style="{ backgroundColor: getIconBgColor(indicatorConfig.color) }">
                                <el-icon :style="{ color: indicatorConfig.color || themeColor }">
                                    <Document />
                                </el-icon>
                            </div>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <el-table
                        v-else-if="displayConfig.defaultType === 'table'"
                        :data="tableConfig.indicatorResult"
                        stripe
                        style="width: 100%"
                        :header-cell-style="tableConfig.headerStyle"
                    >
                        <el-table-column
                            v-for="column in tableConfig.indicatorField"
                            :key="column.fieldName"
                            :prop="column.fieldName"
                            :label="column.fieldDesc"
                            align="center"
                        ></el-table-column>
                    </el-table>

                    <!-- 图表区域 -->
                    <div v-else-if="displayConfig.defaultType === 'chart'" class="chart-container">
                        <EChartComponent
                            ref="chartRef"
                            :option="chartOption"
                            height="400px"
                            @chart-ready="handleChartReady"
                        />
                    </div>
                </div>

                <!-- 下载图片按钮 -->
                <div class="download-btn" v-if="operationConfig.showDownloadButton && currentDisplayType === 'chart'">
                    <el-tooltip content="下载图片" placement="top" :show-after="300">
                        <div class="download-icon" @click="handleDownload">
                            <el-icon><Download /></el-icon>
                        </div>
                    </el-tooltip>
                </div>
            </div>
        </template>
    </common-thinkingContentStep>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import EChartComponent from '@/components/dialogue/common/EChartComponent.vue';
import { Document } from '@element-plus/icons-vue';

// 定义组件选项，包括组件名称
defineOptions({
    name: 'IndicatorData',
});
const tableData = ref([]);
const currentDisplayType = ref('');

const titleConfig = ref({
    stepTitle: '指标数据查询结果',
    titleIcon: 'el-icon-data-analysis',
    showTitleDropdown: true,
});

const operationConfig = ref({
    showOperationBar: true,
    showExportButton: true,
    exportButtonText: '导出数据',
    showAnalyzeButton: true,
    analyzeButtonText: '一键分析',
    showDownloadButton: true,
    downloadButtonText: '下载图片',
    downloadIcon: 'el-icon-download',
});
const indicatorConfig = ref({
    indicatorValue: '0',
    indicatorUnit: '个',
    icon: Document,
});
const tableConfig = ref({
    indicatorResult: [],
    indicatorField: [],
    headerStyle: { background: '#f5f7fa' },
});

const displayConfig = ref({
    showOptions: true,
    optionsLabel: '形式',
    options: [
        { value: 'indicator', label: '指标' },
        { value: 'table', label: '表格' },
        { value: 'chart', label: '图表' },
    ],
    defaultType: 'table',
});

// 主题颜色
const themeColor = {
    type: String,
    default: '#409eff',
};

// 定义组件的props
const props = defineProps({
    data: {
        type: Object,
        default: () => {},
    },
});

// 定义事件
const emit = defineEmits(['export', 'analyze', 'download', 'display-type-change']);

watch(
    () => props.data,
    () => {
        // 在组件挂载时处理props.data
        displayConfig.value.showOptions = false;
        displayConfig.value.defaultType = props.data.result.displayConfig.defaultType;
        if (displayConfig.value.defaultType === 'indicator') {
            indicatorConfig.value = props.data.result.indicatorConfig;
            operationConfig.value.showOperationBar = false;
        } else if (displayConfig.value.defaultType === 'table') {
            tableConfig.value = props.data.result.tableConfig;
        } else if (displayConfig.value.defaultType === 'chart') {
        } else if (displayConfig.value.defaultType === 'null') {
            operationConfig.value.showOperationBar = false;
            displayConfig.value.showOptions = false;
        }

        console.log('operationConfig', operationConfig.value);
        console.log('tableConfig', tableConfig.value);
        console.log('displayConfig', displayConfig.value);
    },
    {
        deep: true,
        immediate: true,
    }
);

/**
 * 图表组件引用
 */
const chartRef = ref(null);

/**
 * 图表配置
 */
const chartOption = computed(() => {
    // 准备图表数据
    const xAxisData = tableData.value.map(item => item.region);
    const seriesData = tableData.value.map(item => item.count);

    return {
        title: {
            text: props.tableConfig.columns[1].label,
            left: 'center',
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow',
            },
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
        },
        xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
                interval: 0,
                rotate: xAxisData.length > 5 ? 30 : 0,
            },
        },
        yAxis: {
            type: 'value',
        },
        series: [
            {
                name: props.tableConfig.columns[1].label,
                type: 'bar',
                data: seriesData,
                itemStyle: {
                    color: '#4080ff',
                },
            },
        ],
    };
});

/**
 * 监听显示类型变化
 */
watch(currentDisplayType, newVal => {
    emit('display-type-change', newVal);
});

/**
 * 处理图表准备完成事件
 */
const handleChartReady = () => {
    // 可以在这里进行图表实例的其他操作
};

/**
 * 处理导出数据
 * @returns {void}
 */
const handleExport = () => {
    emit('export');
    ElMessage.success('数据导出成功');
};

/**
 * 处理数据分析
 * @returns {void}
 */
const handleAnalyze = () => {
    emit('analyze');
    ElMessage.info('正在分析数据...');
};

/**
 * 处理下载图片
 * @returns {void}
 */
const handleDownload = () => {
    if (currentDisplayType.value === 'chart' && chartRef.value) {
        const chartInstance = chartRef.value.getChartInstance();
        if (chartInstance) {
            const dataURL = chartInstance.getDataURL();
            const link = document.createElement('a');
            link.download = `${props.titleConfig.stepTitle || '图表'}.png`;
            link.href = dataURL;
            link.click();
        }
    } else {
        emit('download');
    }
    ElMessage.success('图片下载成功');
};

/**
 * @description 获取图标背景颜色（带透明度）
 */
const getIconBgColor = color => {
    if (!color) return `rgba(64, 158, 255, 0.1)`;

    // 如果是十六进制颜色，转换为rgba
    if (color.startsWith('#')) {
        const hex = color.replace('#', '');
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);
        return `rgba(${r}, ${g}, ${b}, 0.1)`;
    }

    // 如果已经是rgb或rgba格式
    if (color.startsWith('rgb')) {
        if (color.startsWith('rgba')) return color;
        // 将rgb转为rgba
        return color.replace('rgb', 'rgba').replace(')', ', 0.1)');
    }

    return `rgba(64, 158, 255, 0.1)`;
};
</script>

<style scoped lang="scss">
.data-ask-container {
    background-color: #fff;
    border-radius: 8px;
    padding: 16px;
    position: relative;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.title-with-icon {
    display: flex;
    align-items: center;
    font-weight: 500;

    .el-icon {
        color: #4080ff;
        margin-right: 8px;
        font-size: 18px;
    }

    .title-text {
        color: #333;
        font-size: 16px;
    }
}

.table-container {
    margin-bottom: 20px;
}

.display-options {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 10px;
}

.download-btn {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 100;
}

.download-icon {
    width: 40px;
    height: 40px;
    background-color: #000;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    opacity: 0.7;

    &:hover {
        opacity: 1;
        transform: translateY(-2px);
    }

    img,
    .icon {
        width: 20px;
        height: 20px;
        color: white;
    }
}

/* 表格样式 */
:deep(.el-table) {
    --el-table-header-bg-color: #f5f7fa;
}

:deep(.el-table__row:nth-child(even)) {
    background-color: #f5f7fa;
}

/* 图表容器 */
.chart-container {
    width: 100%;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart {
    width: 100%;
    height: 100%;
}

.chart-placeholder {
    color: #909399;
    font-size: 16px;
}

.indicator-card {
    flex: 1;
    background-color: #f5f7fa;

    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    transition: all 0.3s;
    cursor: pointer;

    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .card-title {
        font-size: 18px;
        color: #606266;
        margin-bottom: 10px;
        font-weight: bold;
    }

    .card-content {
        display: flex;
        align-items: center;

        .card-value {
            font-size: 24px;
            font-weight: bold;
            color: v-bind('themeColor');
            margin-right: 5px;
        }

        .card-unit {
            font-size: 12px;
            color: #909399;
        }

        .card-icon {
            margin-left: auto;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .el-icon {
                font-size: 20px;
            }
        }
    }
}
</style>

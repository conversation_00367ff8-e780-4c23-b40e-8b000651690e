<template>
    <common-thinkingContentStep :stepTitle="' 指标看板生成结果'">
        <template #content>
            <div class="data-ask-container">
                <!-- 顶部按钮区域 -->
                <div class="top-buttons">
                    <el-button class="guide-btn color-#1570EF border-#1570EF">
                        <Icon name="daochu" color="#1570EF" />
                        导出数据
                    </el-button>
                    <el-button class="analysis-btn color-#16B364 border-#16B364 hover:color-#16B364">
                        <Icon name="fenxi" color="#16B364 " />
                        一键分析
                    </el-button>
                    <!-- <el-button type="primary" class="fullscreen-btn">跳转大屏</el-button> -->
                </div>

                <!-- 数据卡片区域 -->
                <div class="data-cards">
                    <div v-for="(card, index) in dataCards" :key="index" class="data-card">
                        <div class="card-icon">
                            <el-icon><Document /></el-icon>
                        </div>
                        <div class="card-content">
                            <div class="card-title">{{ card.title }}</div>
                            <div class="card-value">
                                {{ card.value }}
                                <span class="unit">{{ card.unit }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 趋势图表 -->
                <div class="chart-container">
                    <div class="chart-title">佛山市政务服务业务办理量增速</div>
                    <EChartComponent :option="trendChartOption" height="300px" @chart-ready="handleChartReady" />
                </div>

                <!-- 柱状图表 -->
                <div class="chart-container bar-chart-container">
                    <div class="chart-title">佛山市政务服务业务办理量</div>
                    <div class="chart-subtitle">单位：件</div>
                    <EChartComponent :option="barChartOption" height="300px" @chart-ready="handleChartReady" />
                </div>
            </div>
        </template>
    </common-thinkingContentStep>
</template>

<script setup>
import { Document } from '@element-plus/icons-vue';
import EChartComponent from '@/components/dialogue/common/EChartComponent.vue';

defineOptions({
    name: 'IndicatorKanban',
});

/**
 * @description 数据卡片信息
 */
const dataCards = reactive([
    {
        title: '佛山市政务服务业务办理总量',
        value: '936,121',
        unit: '件',
    },
    {
        title: '佛山市政务服务业务办理约总量',
        value: '807,00',
        unit: '件',
    },
    {
        title: '佛山市政务服务业务办理量最高月份',
        value: '8',
        unit: '月',
    },
    {
        title: '佛山市政务服务业务办理量最低月份',
        value: '1',
        unit: '月',
    },
]);

/**
 * @description 趋势图表数据
 */
const trendChartData = reactive({
    months: ['2023年1月', '2023年2月', '2023年3月', '2023年4月', '2023年5月', '2023年6月', '2023年7月', '2023年8月'],
    data: [2.8, 4.2, 5.0, 5.6, 6.2, 6.3, 6.5, 7.0],
});

/**
 * @description 柱状图表数据
 */
const barChartData = reactive({
    districts: ['市区', '禅城区', '顺德区', '高明区', '三水区', '南海区'],
    data: [183, 162, 228, 131, 142, 237],
});

/**
 * @description 趋势图表配置
 */
const trendChartOption = computed(() => {
    return {
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '3%',
            containLabel: true,
        },
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderColor: '#E0E0E0',
            borderWidth: 1,
            textStyle: {
                color: '#333',
            },
            formatter: params => {
                const param = params[0];
                return `${param.name}<br/>
                       <span style="display:inline-block;margin-right:4px;border-radius:50%;width:10px;height:10px;background-color:${param.color};"></span>
                       <span style="font-weight:bold;">增速: ${param.value}%</span>`;
            },
        },
        xAxis: {
            type: 'category',
            data: trendChartData.months,
            axisLine: {
                lineStyle: {
                    color: '#E0E0E0',
                },
            },
            axisLabel: {
                color: '#333',
                fontSize: 10,
            },
        },
        yAxis: {
            type: 'value',
            axisLine: {
                show: false,
            },
            splitLine: {
                lineStyle: {
                    color: '#E0E0E0',
                    type: 'dashed',
                },
            },
            axisLabel: {
                formatter: '{value}%',
                color: '#333',
                fontSize: 10,
            },
        },
        series: [
            {
                data: trendChartData.data,
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    color: '#4080FF',
                },
                lineStyle: {
                    color: '#4080FF',
                    width: 2,
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: 'rgba(64, 128, 255, 0.3)',
                            },
                            {
                                offset: 1,
                                color: 'rgba(64, 128, 255, 0.1)',
                            },
                        ],
                    },
                },
            },
        ],
    };
});

/**
 * @description 柱状图表配置
 */
const barChartOption = computed(() => {
    return {
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '5%',
            containLabel: true,
        },
        tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderColor: '#E0E0E0',
            borderWidth: 1,
            textStyle: {
                color: '#333',
            },
            formatter: params => {
                return `${params.name}<br/>
                       <span style="display:inline-block;margin-right:4px;border-radius:50%;width:10px;height:10px;background-color:${params.color};"></span>
                       <span style="font-weight:bold;">办理量: ${params.value}件</span>`;
            },
        },
        xAxis: {
            type: 'category',
            data: barChartData.districts,
            axisLine: {
                lineStyle: {
                    color: '#E0E0E0',
                },
            },
            axisLabel: {
                color: '#333',
                fontSize: 10,
            },
        },
        yAxis: {
            type: 'value',
            axisLine: {
                show: false,
            },
            splitLine: {
                lineStyle: {
                    color: '#E0E0E0',
                    type: 'dashed',
                },
            },
            axisLabel: {
                color: '#333',
                fontSize: 10,
            },
        },
        series: [
            {
                data: barChartData.data,
                type: 'bar',
                barWidth: '40%',
                itemStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: '#4080FF',
                            },
                            {
                                offset: 1,
                                color: '#5EBAFF',
                            },
                        ],
                    },
                    borderRadius: [3, 3, 0, 0],
                },
                label: {
                    show: true,
                    position: 'top',
                    color: '#4080FF',
                    fontSize: 12,
                },
            },
        ],
    };
});

/**
 * @description 处理图表准备完成事件
 */
const handleChartReady = chartInstance => {
    // 可以在这里对图表实例进行额外操作
    console.log('图表实例已准备好', chartInstance);
};
</script>

<style scoped lang="scss">
.data-ask-container {
    padding: 16px;
    background-color: $bg;

    min-height: 100%;

    .top-buttons {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 16px;

        .el-button {
            margin-left: 8px;
            font-size: 14px;
        }
    }

    .data-cards {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 16px;

        .data-card {
            background-color: #f5f7fa;

            padding: 16px;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;

            .card-icon {
                width: 48px;
                height: 48px;
                background-color: #ecf5ff;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 16px;

                .el-icon {
                    font-size: 24px;
                    color: #409eff;
                }
            }

            .card-content {
                flex: 1;

                .card-title {
                    font-size: 14px;
                    color: #606266;
                    margin-bottom: 8px;
                }

                .card-value {
                    font-size: 24px;
                    font-weight: bold;
                    color: #303133;

                    .unit {
                        font-size: 14px;
                        font-weight: normal;
                        margin-left: 4px;
                    }
                }
            }
        }
    }

    .chart-container {
        background-color: $bg;

        padding: 16px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        margin-bottom: 16px;

        .chart-title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 16px;
        }

        .chart-subtitle {
            font-size: 12px;
            color: #909399;
            margin-top: -12px;
            margin-bottom: 16px;
        }
    }

    .bar-chart-container {
        margin-bottom: 60px;
    }

    .bottom-button {
        position: fixed;
        bottom: 16px;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;

        .el-button {
            border-radius: 20px;
            padding: 8px 24px;
        }
    }
}
</style>

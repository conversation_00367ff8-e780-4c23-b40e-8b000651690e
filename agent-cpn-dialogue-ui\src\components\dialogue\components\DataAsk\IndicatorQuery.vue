<template>
    <ComponentsDataCollectionCjInfoTool
        :title="'指标信息查询'"
        showButton
        :infoData="resultData"
        :itemsPerRow="2"
        :configData="configData"
        :clickEdit="clickEdit"
        :clickConfirm="clickConfirm"
    ></ComponentsDataCollectionCjInfoTool>
</template>

<script setup>
const { handleActionReply } = useDialogueHandler();

defineOptions({
    name: 'IndicatorQuery',
});

// 指标问数右侧组件
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});

const resultData = ref([]);
let indicatorCode = '';
let firstMessage = '';
watch(
    () => props.data,
    () => {
        // 在组件挂载时处理props.data
        const indicatorInfo = props.data.result;
        indicatorCode = indicatorInfo.indicatorCode;
        firstMessage = indicatorInfo.message;
        let indicatorNameObj = {};
        indicatorNameObj['label'] = '指标名称';
        indicatorNameObj['value'] = indicatorInfo.indicatorName;
        let databaseNameObj = {};
        databaseNameObj['label'] = '涉及数据库';
        databaseNameObj['value'] = indicatorInfo.databaseName;
        resultData.value.push(indicatorNameObj);
        resultData.value.push(databaseNameObj);
    },
    {
        immediate: true,
        deep: true,
    }
);

const configData = ref({
    editText: '修改',
    confirmText: '确认',
});

/**
 * @description 点击修改按钮的处理函数
 */
const clickEdit = () => {
    console.log('clickEdit');
};

/**
 * @description 点击确认按钮的处理函数
 */
const clickConfirm = () => {
    console.log('clickConfirm');
    let data = {
        actionCode: 'indicatorDataQuery',
        firstMessage: firstMessage,
        msg: '确认指标基本信息',
        dialogueId: props.data.dialogueId,
        params: {
            indicatorCode: indicatorCode,
        },
    };
    handleActionReply(data);
};
</script>

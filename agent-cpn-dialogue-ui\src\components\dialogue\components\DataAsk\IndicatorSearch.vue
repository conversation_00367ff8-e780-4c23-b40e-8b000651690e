<template>
    <common-thinkingContentStep :stepTitle="'指标信息查询'">
        <template #content>
            <div class="indicator-search">
                <div class="indicator-cards">
                    <div v-for="(row, rowIndex) in formattedIndicators" :key="`row-${rowIndex}`" class="row">
                        <div
                            v-for="(indicator, index) in row"
                            :key="`indicator-${rowIndex}-${index}`"
                            class="indicator-card"
                            @click="handleIndicatorClick(indicator)"
                        >
                            <div class="card-title">{{ indicator.indicatorName }}</div>
                            <div>
                                <el-tag
                                    v-for="(tag, index) in indicator.tags"
                                    :key="`tag-${index}`"
                                    type="primary"
                                    size="small"
                                    style="margin-right: 5px"
                                >
                                    {{ tag }}
                                </el-tag>
                            </div>
                            <div class="card-content">
                                <div class="card-value">
                                    {{
                                        indicator.indicatorType && indicator.indicatorType === 'indicator'
                                            ? indicator.value
                                            : '看详情'
                                    }}
                                </div>
                                <div class="card-unit">{{ indicator.indicatorUnit }}</div>
                                <div class="card-icon" :style="{ backgroundColor: getIconBgColor(indicator.color) }">
                                    <el-icon :style="{ color: indicator.color || themeColor }">
                                        <component :is="indicator.icon" />
                                    </el-icon>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="showActionButton" class="action-button">
                    <el-button :type="actionButtonType" class="import-button" @click="handleActionClick">
                        {{ actionButtonText }}
                    </el-button>
                </div>
            </div>
        </template>
    </common-thinkingContentStep>
</template>

<script setup>
import {
    Document,
    View,
    User,
    UserFilled,
    SetUp,
    Connection,
    Timer,
    Ticket,
    Files,
    Box,
    Share,
} from '@element-plus/icons-vue';
import { DialogueService } from '@/components/dialogue/hooks/useSystemResponse.js';
import { generateDialogueMsg, reply } from '@/api/dialogue/index.js';
import { useDialogueHandler } from '@/components/dialogue/hooks/useDialogueHandler.js';
import { ref, watch } from 'vue';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';

const { setRightPanel } = useRightPanelStore();
const { replySendWs, handleActionReplyAsync } = useDialogueHandler();

defineOptions({
    name: 'IndicatorSearch',
});

/**
 * @description 组件属性定义
 */
const props = defineProps({
    // 指标数据数组
    // 每个指标对象的结构：
    // - title: 指标标题
    // - value: 指标数值
    // - unit: 单位
    // - icon: 图标名称(字符串)或组件引用
    // - color: 颜色值(十六进制、RGB或RGBA)，影响数值和图标颜色
    data: {
        type: Object,
        default: () => {},
    },
    // 每行显示的指标数量
    columnsPerRow: {
        type: Number,
        default: 3,
    },
    // 主题颜色
    themeColor: {
        type: String,
        default: '#409eff',
    },
    // 是否显示底部操作按钮
    showActionButton: {
        type: Boolean,
        default: true,
    },
    // 操作按钮文本
    actionButtonText: {
        type: String,
        default: '一键导入',
    },
    // 操作按钮类型
    actionButtonType: {
        type: String,
        default: 'primary',
    },
});

/**
 * @description 组件事件定义
 */
const emit = defineEmits(['indicator-click', 'action-click']);
/**
 * @description 将指标数据按每行显示数量分组
 */
const formattedIndicators = ref([]);
watch(
    () => props.data,
    val => {
        const result = [];
        const iconMap = {
            Document,
            View,
            User,
            UserFilled,
            SetUp,
            Connection,
            Timer,
            Ticket,
            Files,
            Box,
            Share,
        };
        // 处理图标
        const processedIndicators = val.result.data.map(indicator => ({
            ...indicator,
            icon: indicator.icon
                ? typeof indicator.icon === 'string'
                    ? iconMap[indicator.icon] || Document
                    : indicator.icon
                : Document,
            tags: indicator.keyWord ? indicator.keyWord.split(',') : [],
            value: indicator.indicatorValue ? indicator.indicatorValue : '--',
            id: indicator.processId ? indicator.processId : 0,
        }));

        // 按每行数量分组
        for (let i = 0; i < processedIndicators.length; i += props.columnsPerRow) {
            result.push(processedIndicators.slice(i, i + props.columnsPerRow));
        }
        formattedIndicators.value = result;
    },
    {
        deep: true,
        immediate: true,
    }
);
/**
 * @description 处理指标点击事件
 */
const handleIndicatorClick = async indicator => {
    let data = {
        actionCode: 'indicatorProcessQuery',
        dialogueId: props.data.dialogueId,
        params: {
            processId: indicator.id,
        },
    };
    const handleActionReply1 = await handleActionReplyAsync(data);
    let iconMap;
    let indicators = props.data.result.data.map(indicator => ({
        ...indicator,
        icon: indicator.icon
            ? typeof indicator.icon === 'string'
                ? iconMap[indicator.icon] || Document
                : indicator.icon
            : Document,
        tags: indicator.keyWord ? indicator.keyWord.split(',') : [],
        value: indicator.indicatorValue ? indicator.indicatorValue : '--',
        id: indicator.processId ? indicator.processId : 0,
    }));
    setRightPanel(
        'RightIndicatorInfo',
        {
            formValue: indicators,
            currIndicator: { ...handleActionReply1, editing: false, sqlExecuted: false },
            title: '指标信息',
        },
        {
            // 自定义事件处理函数
            confirm: indicator => {
                console.log('用户确认了编辑', indicator);
                handleIndicatorClick(indicator);
            },
            cancel: () => {
                console.log('用户取消了编辑');
            },
        }
    );
    emit('indicator-click', indicator);
};

/**
 * @description 一键导入事件
 */
const handleActionClick = () => {
    const userMessage = `一键导入指标信息`;
    let data = {
        actionCode: 'makeTemplateData',
        dialogueId: props.data.dialogueId,
        messageId: props.data.messageId,
        params: {
            firstMessage: props.data.result.firstMessage,
            data: props.data.result.data || [],
        },
    };
    // 添加用户消息到对话
    DialogueService.addUserQuestion(userMessage);

    generateDialogueMsg(data).then(async res => {
        if (res.data.code === 200) {
            let messageId = res.data.data.messageId;
            let dialogueId = res.data.data.dialogueId;
            data['messageId'] = messageId;
            data['dialogueId'] = dialogueId;
            // 发送WebSocket请求
            replySendWs(data);
            try {
                await reply(data);
            } catch (error) {
                console.error('生成任务失败:', error);
            }
        }
    });

    emit('action-click');
};

/**
 * @description 获取图标背景颜色（带透明度）
 */
const getIconBgColor = color => {
    if (!color) return `rgba(64, 158, 255, 0.1)`;

    // 如果是十六进制颜色，转换为rgba
    if (color.startsWith('#')) {
        const hex = color.replace('#', '');
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);
        return `rgba(${r}, ${g}, ${b}, 0.1)`;
    }

    // 如果已经是rgb或rgba格式
    if (color.startsWith('rgb')) {
        if (color.startsWith('rgba')) return color;
        // 将rgb转为rgba
        return color.replace('rgb', 'rgba').replace(')', ', 0.1)');
    }

    return `rgba(64, 158, 255, 0.1)`;
};
</script>

<style scoped lang="scss">
.indicator-search {
    padding: 20px;
    border-radius: 8px;
    background-color: $bg;

    .indicator-cards {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .row {
            display: flex;
            gap: 20px;

            .indicator-card {
                flex: 1;
                background-color: $background-light;
                border-radius: 8px;
                padding: 15px;
                //display: flex;
                align-items: center;
                transition: all 0.3s;
                cursor: pointer;

                &:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 4px 12px $box-shadow-base;
                }

                .card-icon-wrapper {
                    width: 36px;
                    height: 36px;
                    border-radius: 6px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 12px;
                    background: $bg;

                    .el-icon {
                        font-size: 20px;
                    }
                }

                .card-content {
                    display: flex;
                    align-items: center;

                    .card-value {
                        font-size: 24px;
                        font-weight: bold;
                        color: v-bind('themeColor');
                        margin-right: 5px;
                    }

                    .card-unit {
                        font-size: 12px;
                        color: #909399;
                    }

                    .card-icon {
                        margin-left: auto;
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        .el-icon {
                            font-size: 20px;
                        }
                    }
                }
            }
        }
    }

    .action-button {
        margin-top: 20px;
        display: flex;
        justify-content: center;

        .import-button {
            padding: 10px 30px;
            font-size: 16px;
        }
    }
}

.files-card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}
</style>

<template>
    <div v-if="hasDetails" class="expanded-details">
        <div class="details-header">
            <div class="details-title">{{ item.title }} - 详细信息</div>
            <el-button type="text" @click="onClose">
                <el-icon>
                    <Close />
                </el-icon>
            </el-button>
        </div>
        <div class="details-content">
            <common-RightEditTablePreview
                v-if="item.detailsType === 'table'"
                :showChart="item.showChart"
                :data="itemData"
                :showEdit="true"
            />
            <div v-else-if="item.detailsType === 'chart'" class="chart-container">
                <!-- 这里可以放置其他内容如图表组件 -->
                <div class="chart-placeholder">其他内容展示区域</div>
            </div>
            <div v-else class="default-details">暂无详细信息</div>
        </div>
    </div>
</template>

<script setup>
import { Close } from '@element-plus/icons-vue';

defineOptions({
    name: 'ReportDetails',
});

let itemData = ref([]);

const props = defineProps({
    item: {
        type: Object,
        default: () => ({}),
    },
    hasDetails: {
        type: Boolean,
        default: false,
    },
});

watch(
    () => props.item,
    () => {
        console.log('itemData', props.item);
        itemData.value[0] = props.item ? props.item : [];
    },
    {
        immediate: true,
        deep: true,
    }
);

const emit = defineEmits(['close']);

const onClose = () => {
    emit('close');
};
</script>

<style scoped lang="scss">
.expanded-details {
    margin-top: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .details-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #e0e0e0;

        .details-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
        }
    }

    .details-content {
        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;

            .chart-placeholder {
                color: #909399;
                font-size: 16px;
                text-align: center;
                padding: 40px;
                border: 1px dashed #dcdfe6;
                border-radius: 4px;
                width: 100%;
            }
        }

        .default-details {
            text-align: center;
            padding: 30px;
            color: #909399;
        }
    }
}
</style>

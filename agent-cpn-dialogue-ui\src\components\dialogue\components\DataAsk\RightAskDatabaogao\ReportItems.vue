<template>
    <div class="report-items">
        <div v-for="(item, index) in items" :key="index" class="report-item" @click="onItemClick(item)">
            <div class="item-icon">
                <el-icon><Document /></el-icon>
            </div>
            <div class="item-content">
                <div class="item-title">{{ item.title }}</div>
                <div class="item-value" :class="{ 'value-up': item.trend > 0, 'value-down': item.trend < 0 }">
                    {{ item.value }}
                    <span v-if="item.unit" class="item-unit">{{ item.unit }}</span>
                    <span v-if="item.trend !== undefined" class="item-trend">
                        <el-icon v-if="item.trend > 0"><CaretTop /></el-icon>
                        <el-icon v-else-if="item.trend < 0"><CaretBottom /></el-icon>
                        {{ Math.abs(item.trend) }}%
                    </span>
                </div>
            </div>
            <div class="item-expand">
                <el-icon v-if="item.expandable">
                    <ArrowDown v-if="!item.expanded" />
                    <ArrowUp v-else />
                </el-icon>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Document, CaretTop, CaretBottom, ArrowDown, ArrowUp } from '@element-plus/icons-vue';

defineOptions({
    name: 'ReportItems',
});

const props = defineProps({
    items: {
        type: Array,
        required: true,
    },
});

const emit = defineEmits(['item-click']);

const onItemClick = item => {
    emit('item-click', item);
};
</script>

<style scoped lang="scss">
.report-items {
    .report-item {
        display: flex;
        align-items: center;
        padding: 15px;
        background-color: #fff;
        border-radius: 6px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
            background-color: #f5f7fa;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .item-icon {
            margin-right: 15px;
            color: #409eff;
            font-size: 20px;
        }

        .item-content {
            flex: 1;

            .item-title {
                font-size: 14px;
                color: #606266;
                margin-bottom: 5px;
            }

            .item-value {
                font-size: 18px;
                font-weight: 600;
                color: #303133;
                display: flex;
                align-items: center;

                &.value-up {
                    color: #67c23a;
                }

                &.value-down {
                    color: #f56c6c;
                }

                .item-unit {
                    font-size: 14px;
                    margin-left: 4px;
                    color: #909399;
                }

                .item-trend {
                    margin-left: 10px;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                }
            }
        }

        .item-expand {
            margin-left: 10px;
            color: #909399;
        }
    }
}
</style>

<template>
    <div class="thumbnail-container">
        <div
            v-for="(item, index) in thumbnails"
            :key="index"
            class="thumbnail-item"
            :class="{ active: currentIndex === index }"
            @click="onThumbnailClick(index)"
        >
            <div class="thumbnail-number">{{ index + 1 }}</div>
            <div class="thumbnail-image">
                <img :src="item.imageLink" :alt="item.title" />
            </div>
        </div>
    </div>
</template>

<script setup>
defineOptions({
    name: 'ReportThumbnails',
});

const props = defineProps({
    thumbnails: {
        type: Array,
        required: true,
    },
    currentIndex: {
        type: Number,
        default: 0,
    },
});

const emit = defineEmits(['thumbnail-click']);

const onThumbnailClick = index => {
    emit('thumbnail-click', index);
};
</script>

<style scoped lang="scss">
.thumbnail-container {
    width: 120px;
    height: 100%;
    background-color: #f0f2f5;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    padding: 10px 0;

    .thumbnail-item {
        position: relative;
        width: 100px;
        height: 140px;
        margin: 0 auto 15px;
        border-radius: 4px;
        cursor: pointer;
        border: 2px solid transparent;
        overflow: hidden;
        transition: all 0.3s;

        &.active {
            border-color: #409eff;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
        }

        .thumbnail-number {
            position: absolute;
            top: 5px;
            left: 5px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: rgba(64, 158, 255, 0.8);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .thumbnail-image {
            width: 100%;
            height: 100%;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }
}

// 响应式调整
@media screen and (max-width: 768px) {
    .thumbnail-container {
        width: 100%;
        height: auto;
        padding: 10px;
        display: flex;
        overflow-x: auto;
        overflow-y: hidden;

        .thumbnail-item {
            width: 80px;
            height: 100px;
            margin: 0 8px 0 0;
        }
    }
}
</style>

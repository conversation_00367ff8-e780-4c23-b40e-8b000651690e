<template>
    <div class="right-ask-databaogao" v-if="!isShowDetail">
        <div class="databaogao-container">
            <!-- 左侧缩略图部分 -->
            <ReportThumbnails
                :thumbnails="thumbnailList"
                :current-index="currentIndex"
                @thumbnail-click="handleThumbnailClick"
            />

            <!-- 右侧报告内容部分 -->
            <div class="report-container">
                <!-- 报告内容 -->
                <div class="report-content">
                    <ReportItems :items="currentReport.items" @item-click="toggleExpand" />

                    <!-- 展开详情区域 -->
                    <ReportDetails :item="expandedItem" :has-details="hasExpandedItem" @close="closeExpandedDetails" />
                </div>
            </div>
        </div>
    </div>
    <common-MarkDown :data="data.markdown" v-else />
</template>

<script setup>
import { ref, inject, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useReportsData } from '../composables/useReportsData';
import { useReportExpand } from '../composables/useReportExpand';
import ReportThumbnails from './ReportThumbnails.vue';
import ReportItems from './ReportItems.vue';
import ReportDetails from './ReportDetails.vue';
import { reply, replySync } from '@/api/dialogue/index.js';

defineOptions({
    name: 'RightAskDatabaogao',
});

// 定义props
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});
// 是否显示详情

const isShowDetail = ref(false);

// 报告数据
const fileData = ref([]);

// 注入父组件提供的图标处理函数对象
const iconHandlers = inject('iconHandlers');

// 处理复制图标点击
const toggleShowDetail = () => {
    isShowDetail.value = !isShowDetail.value;
};

// 注册右侧icon点击处理函数
let handleClick;
onMounted(async () => {
    if (iconHandlers) {
        // 注册复制图标的处理函数，并保存取消注册函数
        handleClick = iconHandlers.register('tree-shousuo', toggleShowDetail);
    }
});

// 组件卸载时取消注册
onUnmounted(() => {
    if (handleClick) {
        handleClick();
    }
});

// 使用报告数据钩子
console.log('data1111', fileData.value);
const { currentIndex, thumbnailList, currentReport, setCurrentIndex } = useReportsData(props.data);

// 使用报告详情展开逻辑钩子
const { expandedItem, hasExpandedItem, toggleExpand, closeExpandedDetails } = useReportExpand();

// 处理缩略图点击事件
const handleThumbnailClick = index => {
    setCurrentIndex(index);
    // 关闭已展开的详情
    closeExpandedDetails();
};
</script>

<style scoped lang="scss">
.right-ask-databaogao {
    display: flex;
    height: 100%;
    width: 100%;

    .databaogao-container {
        display: flex;
        width: 100%;
        height: 100%;
        background-color: #f5f7fa;
        border-radius: 8px;
        overflow: hidden;

        // 右侧报告内容样式
        .report-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;

            .report-content {
                width: 100%;
            }
        }
    }
}

// 响应式调整
@media screen and (max-width: 768px) {
    .right-ask-databaogao {
        .databaogao-container {
            flex-direction: column;
        }
    }
}
</style>

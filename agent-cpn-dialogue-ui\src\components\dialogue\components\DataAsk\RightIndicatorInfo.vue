<!-- 指标信息 -->
<template>
    <div class="indicator-info-container">
        <!-- 操作按钮 -->
        <!--        <div class="action-buttons">-->
        <!--            <el-button type="primary" @click="handleSave">保存</el-button>-->
        <!--            <el-button @click="handleEdit">编辑</el-button>-->
        <!--        </div>-->

        <!-- 指标列表 -->
        <div class="indicator-list">
            <!-- 动态渲染指标项 -->
            <div v-for="(item, index) in indicators" :key="item.id" class="indicator-item">
                <div class="item-header">
                    <span class="item-number">{{ index + 1 }}、</span>
                    <span class="item-title">
                        {{ item.indicatorName }}
                        <span>
                            <el-tag
                                v-for="(tag, index) in item.tags"
                                type="success"
                                size="small"
                                :key="tag"
                                style="margin-left: 5px; font-weight: 400"
                            >
                                {{ tag }}
                            </el-tag>
                        </span>
                    </span>
                    <el-button
                        class="edit-btn"
                        size="small"
                        v-if="item.id === currentIndicator.id"
                        @click="handleEditItem"
                    >
                        {{ currentIndicator.editing ? '保存' : '编辑' }}
                    </el-button>
                </div>

                <div v-if="item.id === currentIndicator.id" class="item-content">
                    <div class="form-row">
                        <div class="form-item">
                            <div class="form-label">指标名称：</div>
                            <div class="form-value">
                                <el-input
                                    v-model="currentIndicator.indicatorName"
                                    :disabled="!currentIndicator.editing"
                                />
                            </div>
                        </div>
                        <div class="form-item">
                            <div class="form-label">数据来源：</div>
                            <div class="form-value">
                                <el-input
                                    v-model="currentIndicator.databaseName"
                                    :disabled="!currentIndicator.editing"
                                />
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-item">
                            <div class="form-label">业务口径：</div>
                            <div class="form-value">
                                <el-input
                                    type="textarea"
                                    v-model="currentIndicator.businessDefinition"
                                    :disabled="!currentIndicator.editing"
                                    :rows="2"
                                />
                            </div>
                        </div>
                        <div class="form-item">
                            <div class="form-label">计算公式：</div>
                            <div class="form-value">
                                <el-input
                                    type="textarea"
                                    v-model="currentIndicator.formula"
                                    :disabled="!currentIndicator.editing"
                                    :rows="2"
                                />
                            </div>
                        </div>
                    </div>

                    <div class="form-row-sql">
                        <div class="tip">
                            <div>SQL语句：</div>
                            <el-button class="sql-btn" @click="executeSQL(currentIndicator)">执行</el-button>
                        </div>

                        <div class="form-value sql-area">
                            <div class="sql-content" :class="{ editing: currentIndicator.editing }">
                                <div
                                    v-if="!currentIndicator.editing"
                                    v-html="highlightSQL(currentIndicator.sqlValue)"
                                    class="shikiji-container"
                                ></div>
                                <el-input v-else type="textarea" v-model="currentIndicator.sqlValue" :rows="6" />
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-item">
                            <div class="form-label">数值：</div>
                            <div class="form-value">
                                <el-input
                                    v-model="currentIndicator.indicatorValue"
                                    :disabled="!currentIndicator.editing"
                                />
                            </div>
                        </div>
                        <div class="form-item">
                            <div class="form-label">单位：</div>
                            <div class="form-value">
                                <el-input
                                    v-model="currentIndicator.indicatorUnit"
                                    :disabled="!currentIndicator.editing"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getHighlighter } from 'shikiji';

// 定义组件选项
defineOptions({
    name: 'IndicatorInfo',
});

const props = defineProps({
    formValue: {
        type: Object,
        default: () => {},
    },
    currIndicator: {
        type: Object,
        default: () => {},
    },
});
const currentIndicator = ref({});

watch(
    () => props.currIndicator,
    newVal => {
        if (newVal) {
            currentIndicator.value = {
                ...props.currIndicator,
                editing: false,
                sqlExecuted: false,
            };
        }
    },
    {
        deep: true,
        immediate: true,
    }
);

// 定义事件
const emit = defineEmits(['close', 'save', 'confirm']);

// 创建语法高亮实例
const highlighter = ref(null);

/**
 * 指标数据
 */
const indicators = ref([]);

const getIndicatorInfo = id => {};

onMounted(async () => {
    highlighter.value = await getHighlighter({
        themes: ['github-light'],
        langs: ['sql'],
    });
    indicators.value = props.formValue.map(item => ({
        ...item,
        editing: false,
        sqlExecuted: false,
    }));
    currentIndicator.value = {
        ...props.currIndicator,
        editing: false,
        sqlExecuted: false,
    };
});

/**
 * 高亮SQL代码
 * @param {String} code - SQL代码
 * @returns {String} 高亮后的HTML
 */
const highlightSQL = code => {
    if (!highlighter.value || !code) return '';
    return highlighter.value.codeToHtml(code, {
        lang: 'sql',
        theme: 'github-light',
    });
};

/**
 * 处理保存按钮点击
 */
const handleSave = () => {
    indicators.value.forEach(item => {
        item.editing = false;
    });

    emit('save', indicators.value);
    ElMessage.success('保存成功');
};

/**
 * 处理编辑按钮点击
 */
const handleEdit = () => {
    indicators.value.forEach(item => {
        item.editing = true;
    });

    ElMessage.info('进入编辑模式');
};

/**
 * 处理编辑单个指标项
 * @param {Number} id - 指标ID
 * @param index
 */
const handleEditItem = () => {
    currentIndicator.value.editing = !currentIndicator.value.editing;

    if (currentIndicator.value.editing) {
    } else {
        emit('confirm', currentIndicator.value);
    }
};

/**
 * 执行SQL语句
 * @param {Number} index - 指标索引
 */
const executeSQL = index => {
    if (index >= 0 && index < indicators.value.length) {
        indicators.value[index].sqlExecuted = true;
        ElMessage.success('执行SQL');
    }
};
</script>

<style scoped lang="scss">
.indicator-info-container {
    position: relative;
    width: 100%;
    background-color: $bg;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding-bottom: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;

    .title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
    }

    .close-btn {
        cursor: pointer;
        font-size: 20px;
        color: #909399;

        &:hover {
            color: #409eff;
        }
    }
}

.action-buttons {
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    gap: 10px;
}

.indicator-list {
    padding: 0 20px;
    margin-top: 20px;
}

.indicator-item {
    margin-bottom: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;

    .item-header {
        display: flex;
        align-items: center;
        padding: 10px 15px;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;
        color: #409eff;

        .item-number {
            font-weight: 600;
            margin-right: 5px;
        }

        .item-title {
            flex: 1;
            font-weight: 600;
        }

        .edit-btn {
            padding: 6px 12px;
        }
    }

    .item-content {
        padding: 15px;
    }
}
.form-row-sql {
    .shikiji-container {
        margin: 0;
        border-radius: 4px;
        overflow: auto;
        font-size: 14px;
        line-height: 1.5;
        margin: 15px 0;
        :deep(pre) {
            margin: 0;
            background-color: #f8f9fc !important;
        }
    }

    .tip {
        display: flex;
        justify-content: space-between;
    }
}

.form-row {
    display: flex;
    margin-bottom: 15px;

    .form-item {
        flex: 1;
        display: flex;
        padding: 0 10px;

        &.full-width {
            width: 100%;
        }

        .form-label {
            width: 80px;
            text-align: right;
            display: flex;
            padding-right: 10px;
            line-height: 32px;
            color: #606266;
        }

        .form-value {
            flex: 1;
        }
    }
}

.footer-info {
    background-color: #f5f7fa;
    padding: 15px;
    margin: 20px;
    border-radius: 4px;

    .info-text {
        font-size: 14px;
        color: #606266;
        line-height: 1.8;
    }
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;

        .form-item {
            margin-bottom: 10px;
        }
    }
}
</style>

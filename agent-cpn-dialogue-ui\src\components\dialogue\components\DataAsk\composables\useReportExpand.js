import { ref, computed } from 'vue';

/**
 * @description 报告项展开和收缩逻辑钩子
 */
export function useReportExpand() {
    // 当前展开的项目
    const expandedItem = ref(null);

    // 是否有展开的项目
    const hasExpandedItem = computed(() => {
        return expandedItem.value !== null;
    });

    // 切换缩略图右侧展开/折叠状态
    const toggleExpand = item => {
        if (item.expandable) {
            // 如果已经展开了其他项，先关闭
            if (expandedItem.value && expandedItem.value !== item) {
                expandedItem.value.expanded = false;
            }

            // 切换当前项的展开状态
            item.expanded = !item.expanded;

            // 设置当前展开的项
            if (item.expanded) {
                expandedItem.value = item;
            } else {
                expandedItem.value = null;
            }
        }
    };

    // 关闭展开的详情
    const closeExpandedDetails = () => {
        if (expandedItem.value) {
            expandedItem.value.expanded = false;
            expandedItem.value = null;
        }
    };

    return {
        expandedItem,
        hasExpandedItem,
        toggleExpand,
        closeExpandedDetails,
    };
}

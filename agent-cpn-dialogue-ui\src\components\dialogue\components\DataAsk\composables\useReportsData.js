import { ref, computed, watch } from 'vue';
import { reply, replySync } from '../../../../../api/dialogue/index.js';

/**
 * @description 缩略图管理钩子
 */
export function useReportsData(data) {
    let fileData = ref([]);
    watch(
        () => data.result,
        async newData => {
            let listData = newData.wordReportPage;
            if (listData) {
                for (const item of listData) {
                    const pageId = item.id;
                    let itemsData = {
                        actionCode: 'indicatorProcessList',
                        params: {
                            pageId: pageId,
                        },
                    };
                    const { data: res } = await replySync(itemsData);
                    console.log('res', res);
                    if (res.code === 200) {
                        let cur = item;
                        cur.items = res.data;
                        fileData.value.push(cur);
                    }
                }
            }
        },
        {
            deep: true,
            immediate: true,
            once: true,
        }
    );
    // 当前选中的报告索引
    const currentIndex = ref(0);
    // 缩略图列表
    const thumbnailList = ref(fileData || []);

    // 右侧数据配置项
    const reportsList = ref([
        {
            items: [
                {
                    title: '省地室"一件事"办事指南问题和网上服务标问题个数',
                    value: '1',
                    trend: undefined,
                    expandable: true,
                    expanded: false,
                    detailsType: 'table',
                },
                {
                    title: '申报专用问题个数',
                    value: '1',
                    trend: undefined,
                    expandable: true,
                    expanded: false,
                    detailsType: 'table',
                },
                {
                    title: '服务可用性问题个数',
                    value: '22',
                    trend: undefined,
                    expandable: true,
                    expanded: false,
                    detailsType: 'table',
                },
                {
                    title: '办事指南准确度问题个数',
                    value: '2',
                    trend: undefined,
                    expandable: true,
                    expanded: false,
                    detailsType: 'table',
                    showChart: true,
                },
                {
                    title: '市场变更并并整改一市五区一件事办事指南和网上服务问题个数',
                    value: '76',
                    trend: undefined,
                    expandable: true,
                    expanded: false,
                    detailsType: 'table',
                },
            ],
        },
    ]);

    // 计算当前显示的报告
    const currentReport = computed(() => {
        let curItem = reportsList.value[currentIndex.value] || reportsList.value[0];
        curItem.items = thumbnailList.value[currentIndex.value]?.items || [];
        if (curItem.items.length != 0) {
            curItem.items.forEach(item => {
                item.title = item.questionFrag;
                item.name = item.indicatorName;
                item.value = item.indicatorValue;
                item.trend = item.trend ? item.trend : undefined;
                item.expandable = item.expandable ? item.expandable : true;
                item.expanded = false;
                item.detailsType = item.detailsType ? item.detailsType : 'table';
                item.source = item.databaseName ? item.databaseName : '无';
                item.description = item.businessDefinition ? item.businessDefinition : '无';
                item.formula = item.formula ? item.formula : '无';
                item.sql = item.sqlValue ? item.sqlValue : '无';
                item.unit = item.indicatorUnit ? item.indicatorUnit : '无';
                item.editing = false;
                item.sqlExecuted = false;
            });
        }
        return curItem;
    });

    return {
        currentIndex,
        thumbnailList,
        reportsList,
        currentReport,
        setCurrentIndex: index => {
            currentIndex.value = index;
        },
    };
}

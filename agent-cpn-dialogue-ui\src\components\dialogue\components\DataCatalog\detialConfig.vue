<template>
    <div class="task_dialog">
        <div class="task_dialog_title">
            <span>详细配置</span>
            <p>
                <img :src="publicPath + `/static/dialogue/dialogicon1.png`" title="复制" />
                <img :src="publicPath + `/static/dialogue/dialogicon2.png`" title="分享" />
                <i>|</i>
                <img :src="publicPath + `/static/dialogue/dialogicon3.png`" title="关闭" @click="handleClose" />
            </p>
        </div>
        <div class="task_dialog_content">
            <el-form :model="form" ref="formRef" :rules="rules" class="my_form_edit" label-width="150px">
                <el-form-item label="文件夹名称：" prop="wjjmc">
                    <el-input v-model="form.wjjmc" placeholder="请输入文件夹名称"></el-input>
                </el-form-item>

                <el-form-item label="存储地址：" prop="ccdz">
                    <el-input v-model="form.ccdz" placeholder="请输入存储地址"></el-input>
                </el-form-item>

                <el-form-item label="文件描述：" prop="wjms">
                    <el-input
                        v-model="form.wjms"
                        placeholder="请输入文件描述"
                        style="width: calc(100% - 80px)"
                    ></el-input>
                    <a class="commonbtn2">一键输入</a>
                </el-form-item>

                <el-form-item label="文件名：" prop="wjm">
                    <el-input v-model="form.wjm" placeholder="请输入文件名"></el-input>
                </el-form-item>

                <el-form-item label="文件类型：" prop="wjlx">
                    <el-select v-model="form.wjlx" placeholder="请选择" style="width: 100%">
                        <el-option
                            v-for="item in wjlxOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item label="非结构化数据类型：" prop="fjgxsjlx">
                    <el-select v-model="form.fjgxsjlx" placeholder="请选择" style="width: 100%">
                        <el-option
                            v-for="item in fjgxsjlxOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>

                <!-- 操作按钮区域 -->
                <div class="task_thinking_btn">
                    <a class="btn0" @click="resetForm">取消</a>
                    <a class="btn2" @click.once="onSubmit">确认</a>
                </div>
            </el-form>
        </div>
    </div>
</template>

<script>
import { ElMessage } from 'element-plus';
import { publicPath } from '@/components/dialogue/store/main.js';

export default {
    name: 'detialConfig',
    components: {},
    data() {
        return {
            showTaskDialog: true, // 显示窗口
            form: {
                wjjmc: '', // 文件夹名称
                ccdz: '', // 存储地址
                wjms: '', // 文件描述
                wjm: '', // 文件名
                wjlx: '', // 文件类型
                fjgxsjlx: '', // 非结构化数据类型
            },
            // 表单验证规则
            rules: {
                wjjmc: [{ required: true, message: '请输入文件夹名称', trigger: 'blur' }],
                ccdz: [{ required: true, message: '请输入存储地址', trigger: 'blur' }],
                wjms: [{ required: true, message: '请输入文件描述', trigger: 'blur' }],
                wjm: [{ required: true, message: '请输入文件名', trigger: 'blur' }],
                wjlx: [{ required: true, message: '请选择文件类型', trigger: 'change' }],
                fjgxsjlx: [{ required: true, message: '请选择非结构化数据类型', trigger: 'change' }],
            },
            // 文件类型选项
            wjlxOptions: [
                { label: '文件类型1', value: '文件类型1' },
                { label: '文件类型2', value: '文件类型2' },
            ],
            // 非结构化数据类型选项
            fjgxsjlxOptions: [
                { label: '数据类型1', value: '数据类型1' },
                { label: '数据类型2', value: '数据类型2' },
            ],
        };
    },
    methods: {
        //确认
        onSubmit() {
            if (this.$refs.formRef) {
                this.$refs.formRef.validate(valid => {
                    if (valid) {
                        ElMessage({
                            message: '提交成功',
                            type: 'success',
                        });
                        this.resetForm();
                    } else {
                        console.log('表单验证失败');
                        return false;
                    }
                });
            }
        },
        //取消
        resetForm() {
            if (this.$refs.formRef) {
                this.$refs.formRef.resetFields();
            }
            this.$emit('handleHide', false);
        },
        //关闭
        handleClose() {
            this.$emit('handleHide', false);
        },
    },
};
</script>

<style lang="scss" scoped></style>

<template>
    <mlPzTool
        :title="title"
        :defaultFieldsConfig="defaultFieldsConfig"
        :resourceInfo="defaultResourceInfo"
        @edit="handleEditConfig"
        @confirm="handleConfirm"
    />
</template>

<script setup name="dialogueBaseInfoPzTool">
import { ref, computed } from 'vue';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
import { DialogueService } from '@/components/dialogue/hooks/useSystemResponse';
import { useDialogueHandler } from '@/components/dialogue/hooks/useDialogueHandler';
import mlPzTool from '@/components/dialogue/common/mlPzTool.vue';
import { reply } from '@/api/dialogue';

// 获取右侧面板控制函数
const { setRightPanel, hideRightPanel } = useRightPanelStore();
// 获取对话处理函数
const { replySendWs } = useDialogueHandler();

const props = defineProps({
    resourceData: {
        type: Object,
        default: () => ({}),
    },
    /**
     * 组件标题
     */
    title: {
        type: String,
        default: '目录基本信息配置工具',
    },
});
// Emits定义
const emit = defineEmits(['handleShow']);

// 默认资源字段配置
const defaultFieldsConfig = [
    { key: 'title', label: '资源标题', required: true },
    { key: 'updateDate', label: '更新日期', required: true },
    { key: 'attributeType', label: '属性分类', required: true },
    { key: 'baseType', label: '基础分类', required: false },
    { key: 'themeType', label: '主题分类', required: false },
    { key: 'department', label: '部门', required: true },
    { key: 'shareType', label: '共享类型', required: true },
    { key: 'shareCondition', label: '共享条件', required: false },
    { key: 'shareMethod', label: '共享方式', required: false },
    { key: 'shareRange', label: '共享范围', required: false },
    { key: 'shareBasis', label: '共享依据', required: false },
    { key: 'openType', label: '开放类型', required: true },
];
// 默认资源数据
const defaultResourceInfo = {
    title: '2025年人口基本信息统计演示数据',
    updateDate: '每日',
    attributeType: '基础资源信息',
    baseType: '社会信用',
    themeType: '综合政务',
    department: '公安局',
    shareType: '有条件共享',
    shareCondition: '内容',
    shareMethod: '',
    shareRange: '',
    shareBasis: '',
    openType: '依申请开放',
};
const handleEditConfig = () => {
    handleEdit();
};
/**
 * 显示编辑采集任务
 */
const handleEdit = () => {
    setRightPanel(
        'editBaseInfo',
        {
            formValue: props.resourceData,
        },
        '详细配置目录信息'
    );
};
const handleConfirm = async () => {
    // 添加用户确认信息
    const userMessage = `确认目录基本信息`;
    let data1 = {
        actionCode: 'catalogInfoCheckAction',
        msg: userMessage,
        containerId: 'confirmCatalogInfo',
        entity: {
            name: '企业基本信息目录对话生成2',
            resEngName: '',
            resAbstract: '',
            deptId: '1905183679240470529',
            resKeywords: '',
            resType: 3,
            subjectType: 1,
            baseType: '1281111441857159170',
            shareType: 3,
            shareCondition: '',
            openType: 0,
            updateCycle: 2,
            subed: false,
        },
    };
    DialogueService.addUserQuestion(userMessage);
    replySendWs(data1);
    const { data: res } = await reply(data1);
    console.log('🚀 ~ handleConfirm ~ res:', res);
    DialogueService.addSystemResponse({
        container: res.component,
        components: [
            {
                name: 'CreateTask',
                props: {
                    title: '资源类别信息填写',
                    data: res.entity,
                },
            },
        ],
    });
};
</script>

<style lang="scss" scoped></style>

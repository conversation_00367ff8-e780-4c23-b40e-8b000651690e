<template>
    <mlPzTool
        :title="title"
        :defaultFieldsConfig="defaultFieldsConfig"
        :resourceInfo="defaultResourceInfo"
        :data="editData"
        :other="other"
        @edit="handleEditConfig"
        @confirm="onConfirm"
    />
</template>

<script setup name="dialogueInfoPzTool">
import { ref, computed, watch, reactive } from 'vue';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
import { DialogueService } from '@/components/dialogue/hooks/useSystemResponse';
import { useDialogueHandler } from '@/components/dialogue/hooks/useDialogueHandler';
import mlPzTool from '@/components/dialogue/common/mlPzTool.vue';
import { generateDialogueMsg, reply } from '@/api/dialogue';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/components/dialogue/store/modules/user';
import { useSessionStore } from '@dialogue/store/modules/session.js';

const userStore = useUserStore();
const sessionStore = useSessionStore();

// 获取右侧面板控制函数
const { setRightPanel, hideRightPanel } = useRightPanelStore();
// 获取对话处理函数
const { replySendWs } = useDialogueHandler();

const props = defineProps({
    resourceData: {
        type: Object,
        default: () => ({}),
    },
    /**
     * 组件标题
     */
    title: {
        type: String,
        default: '目录基本信息配置工具',
    },
    /**
     * 自定义确认事件处理函数
     */
    onConfirmHandler: {
        type: Function,
        default: null,
    },

    data: {
        type: Object,
        default: () => ({}),
    },
});
// Emits定义
const emit = defineEmits(['handleShow', 'confirm']);

// 默认资源字段配置
const defaultFieldsConfig = [
    { key: 'title', label: '资源标题', required: true, isShow: true },
    { key: 'updateDate', label: '更新日期', required: true, isShow: true },
    { key: 'attributeType', label: '属性分类', required: true, isShow: true },
    { key: 'baseType', label: '基础分类', required: false, isShow: true },
    { key: 'themeType', label: '主题分类', required: false, isShow: true },
    { key: 'department', label: '部门', required: true, isShow: true },
    { key: 'shareType', label: '共享类型', required: true, isShow: true },
    {
        key: 'shareCondition',
        label: '共享条件',
        required: false,
        isShow: props.data?.result.entity.shareType === 1,
    },
    { key: 'shareMethod', label: '共享方式', required: false, isShow: props.data?.result.entity.shareType !== 3 },
    { key: 'shareRange', label: '共享范围', required: false, isShow: props.data?.result.entity.shareType !== 3 },
    { key: 'shareReason', label: '不共享理由', required: false, isShow: props.data?.result.entity.shareType === 3 },
    { key: 'shareBasis', label: '共享依据', required: false, isShow: props.data?.result.entity.shareType === 1 },
    { key: 'openType', label: '开放类型', required: true, isShow: true },
];
// 默认资源数据
const defaultResourceInfo = {
    title: '',
    updateDate: '',
    attributeType: '',
    baseType: '',
    themeType: '',
    department: '',
    shareType: '',
    shareCondition: '',
    shareMethod: '',
    shareRange: '',
    shareBasis: '',
    openType: '',
};
const handleEditConfig = data => {
    handleEdit(data);
};
let editData = reactive({ ...props.data });
let other = 'info';
/**
 * 显示编辑采集任务
 */
const handleEdit = () => {
    setRightPanel(
        'editBaseInfo',
        {
            formValue: editData.result.entity,
        },
        {
            // 自定义事件处理函数
            confirm: formData => {
                if (formData.shareType == 1) {
                    defaultFieldsConfig.find(item => item.key === 'shareCondition').isShow = true;
                    defaultFieldsConfig.find(item => item.key === 'shareBasis').isShow = true;
                } else {
                    defaultFieldsConfig.find(item => item.key === 'shareCondition').isShow = false;
                    defaultFieldsConfig.find(item => item.key === 'shareBasis').isShow = false;
                }
                if (formData.shareType == 3) {
                    defaultFieldsConfig.find(item => item.key === 'shareMethod').isShow = false;
                    defaultFieldsConfig.find(item => item.key === 'shareRange').isShow = false;
                } else {
                    defaultFieldsConfig.find(item => item.key === 'shareMethod').isShow = true;
                    defaultFieldsConfig.find(item => item.key === 'shareRange').isShow = true;
                }
                // 更新资源配置
                Object.assign(editData.result.entity, formData);
                // 可以在这里执行其他操作，如调用API保存数据等
            },
            cancel: () => {},
        }
    );
};

/**
 * 确认按钮点击处理函数
 * 根据是否有传入的onConfirmHandler或者父组件是否监听confirm事件来决定使用哪个处理函数
 */
const onConfirm = async formData => {
    // 判断是否有自定义的确认事件处理器或监听的confirm事件
    if (props.onConfirmHandler) {
        // 使用传入的确认事件处理函数
        await props.onConfirmHandler(formData);
    } else {
        // 使用默认的确认事件处理函数
        await handleConfirm(formData);
    }
};

/**
 * 默认的确认事件处理函数
 */
const handleConfirm = async formData => {
    let messageId = '';
    let userInfo = userStore.userInfo;
    let dialogueId = sessionStore.dialogueId;
    formData = {
        ...formData,
        dialogueId,
    };
    generateDialogueMsg(formData).then(res => {
        if (res.data.code === 200) {
            messageId = res.data.data.messageId;
            // 添加用户确认信息
            const userMessage = `确认目录基本信息`;
            let data1 = {
                actionCode: 'catalogInfoCheckAction',
                msg: userMessage,
                messageId: messageId,
                dialogueId: dialogueId,
                userId: userInfo.user_id,
                entity: { ...editData.result.entity },
            };
            DialogueService.addUserQuestion(userMessage);
            replySendWs(data1);
            //动作回复,调用回复接口，结果通过websocket返回
            reply(data1);
            console.log('🚀 ~ handleConfirm ~ res:', res);
        }
    });
};
</script>

<style lang="scss" scoped></style>

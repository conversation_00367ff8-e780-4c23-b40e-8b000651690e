<template>
    <div class="task-container">
        <!-- 流程步骤区域 -->
        <div class="t2_info_box">
            <CreateTask
                :config="taskConfig"
                :buttonText="'生成数据采集任务'"
                :useThinkingStep="false"
                @generate-task="handleGenerateTask"
                @form-change="handleFormChange"
            />
        </div>
    </div>
</template>

<script setup name="mlCreate">
import { ref, computed, reactive } from 'vue';

// 资源类别信息填写
import CreateTask from '@/components/dialogue/common/CreateTask.vue';

// 定义事件
const emit = defineEmits(['handleShow']);

const Texts = {
    // 字段标签
    category: '该资源目录是',
    system: '应用系统是',
    db: '来源数据库是',
    schema: '模式名称',
    table: '数据表是',
    range: '数据范围',
    level: '数据分级',
    dateRange: '时间范围',
    frequency: '更新频率',

    // 字段前缀
    categoryPrefix: '',
    systemPrefix: '，',
    dbPrefix: '，',
    schemaPrefix: ' ',
    tablePrefix: '，',
    rangePrefix: '，',
    levelPrefix: '，',
    dateRangePrefix: '，',
    frequencyPrefix: '，',

    // 字段后缀
    categorySuffix: '类别',
    systemSuffix: '',
    dbSuffix: '',
    schemaSuffix: '',
    tableSuffix: '',
    rangeSuffix: '',
    levelSuffix: '',
    dateRangeSuffix: '',
    frequencySuffix: '。',

    // 占位符
    categoryPlaceholder: '类别名',
    systemPlaceholder: '系统名',
    dbPlaceholder: '库名',
    schemaPlaceholder: '名称',
    tablePlaceholder: '表名',
    rangePlaceholder: '范围',
    levelPlaceholder: '级别',
    dateRangePlaceholder: '2024-01-01—2024-12-31',
    frequencyPlaceholder: '频率',

    // 选择提示
    categorySelectPrompt: '请选择类别',
    systemSelectPrompt: '请选择系统',
    dbSelectPrompt: '请选择数据库',
    schemaSelectPrompt: '请选择模式',
    tableSelectPrompt: '请选择数据表',
    rangeSelectPrompt: '请选择范围',
    levelSelectPrompt: '请选择级别',
    frequencySelectPrompt: '请选择频率',

    // 其他
    emptyTip: '必填项为空，请补充相关信息。',
};
// 采集任务配置
const taskConfig = reactive({
    // 显示设置
    showIcon: true,
    hideEnding: false,

    // 自定义文本内容
    texts: Texts,

    // 自定义字段顺序
    fieldOrder: ['category', 'system', 'table', 'range', 'level', 'frequency', 'targetTable'],

    // 必填字段配置
    fieldsConfig: [],

    // 数据源选项
    categories: [
        { label: 'MySQL', value: 'mysql' },
        { label: 'PostgreSQL', value: 'postgresql' },
        { label: 'Oracle', value: 'oracle' },
        { label: 'SQL Server', value: 'sqlserver' },
    ],

    // 数据库选项
    systems: [
        { label: 'policies_db', value: 'policies_db' },
        { label: 'government_db', value: 'government_db' },
        { label: 'public_db', value: 'public_db' },
    ],

    // 数据表选项
    tables: [
        { label: 'policies', value: 'policies' },
        { label: 'policy_details', value: 'policy_details' },
        { label: 'regulations', value: 'regulations' },
    ],

    // 数据范围选项
    ranges: [
        { label: '全量', value: 'full' },
        { label: '增量', value: 'incremental' },
        { label: '部分', value: 'partial' },
    ],

    // 目标数据源（使用level字段表示）
    levels: [
        { label: 'MySQL', value: 'mysql' },
        { label: 'PostgreSQL', value: 'postgresql' },
        { label: 'Oracle', value: 'oracle' },
    ],

    // 目标数据库（使用frequency字段表示）
    frequencies: [
        { label: 'integrated_db', value: 'integrated_db' },
        { label: 'warehouse_db', value: 'warehouse_db' },
        { label: 'analytics_db', value: 'analytics_db' },
    ],

    // 目标表选项
    targetTables: [
        { label: 'target_policies', value: 'target_policies' },
        { label: 'integrated_policies', value: 'integrated_policies' },
    ],

    // 默认值
    defaultValues: {
        category: 'mysql',
        system: 'policies_db',
        table: 'policies',
        range: 'full',
        level: 'mysql',
        frequency: 'integrated_db',
        targetTable: 'target_policies',
    },

    // 自定义占位符
    placeholders: {
        category: '选择源数据源',
        system: '选择源数据库',
        table: '选择源数据表',
        range: '选择数据范围',
        level: '选择目标数据源',
        frequency: '选择目标数据库',
        targetTable: '选择目标数据表',
    },
});

/**
 * 处理表单变化
 */
const handleFormChange = formData => {
    console.log('表单数据变化:', formData);
};

/**
 * 处理生成任务
 */
const handleGenerateTask = data => {
    console.log('生成采集任务:', data);
    // 如果需要触发右侧面板显示，发送事件
    emit('handleShow', true);
};
</script>

<style lang="scss" scoped>
.task-container {
    width: 100%;
}

.t2_info_box {
    margin-bottom: 16px;
}
</style>

<template>
    <div class="task_dialog">
        <div class="task_dialog_title">
            <span>详细配置目录信息</span>
            <p>
                <img :src="publicPath + `/static/dialogue/dialogicon1.png`" title="复制" />
                <img :src="publicPath + `/static/dialogue/dialogicon2.png`" title="分享" />
                <i>|</i>
                <img :src="publicPath + `/static/dialogue/dialogicon3.png`" title="关闭" @click="handleClose" />
            </p>
        </div>
        <div class="task_dialog_content">
            <el-form :model="form" :rules="rules" ref="formRef" class="my_form_edit" label-width="130px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="英文名称：" prop="ywmc">
                            <el-input v-model="form.ywmc" placeholder="请输入英文名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="关键字：" prop="gjz">
                            <el-input v-model="form.gjz" placeholder="请输入关键字"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="任务名称：" prop="rwmc">
                            <el-select v-model="form.rwmc" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in rwmcOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="应用场景：" prop="yysj">
                            <el-select v-model="form.yysj" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in yysjOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="行业分类：" prop="hyfl">
                            <el-select v-model="form.hyfl" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in hyflOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="服务分类：" prop="fwfl">
                            <el-select v-model="form.fwfl" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in fwflOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="所属领域：" prop="ssly">
                            <el-select v-model="form.ssly" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in sslyOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="资源格式：" prop="zygs">
                            <el-select v-model="form.zygs" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in zygsOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="业务数据领域：" prop="ywsjly">
                            <el-select v-model="form.ywsjly" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in ywsjlyOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="主题分类：" prop="ztfl">
                            <el-select v-model="form.ztfl" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in ztflOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="标签：" prop="bq">
                    <el-select v-model="form.bq" placeholder="请选择" style="width: 100%">
                        <el-option
                            v-for="item in bqOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="摘要:" prop="zy">
                    <el-input type="textarea" v-model="form.zy" placeholder="请输入摘要" rows="6"></el-input>
                </el-form-item>

                <!-- 操作按钮区域 -->
                <div class="task_thinking_btn">
                    <a class="btn0" @click="resetForm">取消</a>
                    <!-- <a class="btn1">修改</a> -->
                    <a class="btn2" @click.once="onSubmit">确认</a>
                </div>
            </el-form>
        </div>
    </div>
</template>

<script>
import { ElMessage } from 'element-plus';
import { publicPath } from '@/components/dialogue/store/main.js';

export default {
    name: 'resourceCatalog',
    components: {},
    data() {
        return {
            showTaskDialog: true, // 显示窗口
            form: {
                ywmc: '',
                gjz: '',
                rwmc: '',
                yysj: '',
                hyfl: '',
                fwfl: '',
                ssly: '',
                zygs: '',
                ywsjly: '',
                ztfl: '',
                bq: '',
                zy: '',
            },
            // 添加表单验证规则
            rules: {},
            formRef: null,

            // 添加下拉选项数据
            rwmcOptions: [
                { label: '任务名称1', value: '任务名称1' },
                { label: '任务名称2', value: '任务名称2' },
            ],
            yysjOptions: [
                { label: '应用场景1', value: '应用场景1' },
                { label: '应用场景2', value: '应用场景2' },
            ],
            hyflOptions: [
                { label: '行业分类1', value: '行业分类1' },
                { label: '行业分类2', value: '行业分类2' },
            ],
            fwflOptions: [
                { label: '服务分类1', value: '服务分类1' },
                { label: '服务分类2', value: '服务分类2' },
            ],
            sslyOptions: [
                { label: '任务所属领域名称', value: '所属领域1' },
                { label: '所属领域2', value: '所属领域2' },
            ],
            zygsOptions: [
                { label: '资源格式1', value: '资源格式1' },
                { label: '资源格式2', value: '资源格式2' },
            ],
            ywsjlyOptions: [
                { label: '业务数据领域1', value: '业务数据领域1' },
                { label: '业务数据领域2', value: '业务数据领域2' },
            ],
            ztflOptions: [
                { label: '主题分类1', value: '主题分类1' },
                { label: '主题分类2', value: '主题分类2' },
            ],
            bqOptions: [
                { label: '标签1', value: '标签1' },
                { label: '标签2', value: '标签2' },
            ],
        };
    },
    methods: {
        //确认
        onSubmit() {
            if (this.$refs.formRef) {
                this.$refs.formRef.validate(valid => {
                    if (valid) {
                        ElMessage({
                            message: '提交成功',
                            type: 'success',
                        });
                        this.resetForm();
                    } else {
                        console.log('表单验证失败');
                        return false;
                    }
                });
            }
        },
        //取消
        resetForm() {
            if (this.$refs.formRef) {
                this.$refs.formRef.resetFields();
            }
            this.$emit('handleHide', false);
        },
        //关闭
        handleClose() {
            if (this.$refs.formRef) {
                this.$refs.formRef.resetFields();
            }
            this.$emit('handleHide', false);
        },
    },
};
</script>

<style lang="scss" scoped></style>

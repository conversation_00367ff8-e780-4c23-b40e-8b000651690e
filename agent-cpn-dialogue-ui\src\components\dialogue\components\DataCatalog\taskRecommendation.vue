<template>
    <thinkingContentStep :stepTitle="title">
        <template #content>
            <div class="default_ask_block">
                <div class="default_ask_list" v-for="(item, index) in taskList" :key="index">
                    <h4>
                        <img :src="publicPath + `/static/dialogue/default_icon.png`" />
                        <span>{{ item.title }}</span>
                        <a @click="handleTaskClick(item)">去生成 ></a>
                    </h4>
                    <p>{{ item.description }}</p>
                </div>
            </div>
        </template>
    </thinkingContentStep>
</template>

<script setup name="taskRecommendation">
/**
 * 任务推荐组件
 * @description 用于展示推荐的后续任务列表
 */
import { defineProps, defineEmits } from 'vue';
import thinkingContentStep from '@/components/dialogue/common/thinkingContentStep.vue';
import { publicPath } from '@/components/dialogue/store/main.js';

// 定义事件
const emit = defineEmits(['taskClick']);

// 定义props
const props = defineProps({
    // 组件标题
    title: {
        type: String,
        default: '任务推荐',
    },
    // 任务列表数据
    taskList: {
        type: Array,
        default: () => [
            {
                title: '01.数据采集',
                description: '采集数据，查看采集情况',
            },
            {
                title: '02.API生成',
                description: '生成接口，调用接口测试',
            },
        ],
    },
});

// 处理任务点击事件
const handleTaskClick = task => {
    console.log('任务点击:', task);
    emit('taskClick', task);
};
</script>

<style scoped lang="scss">
.default_ask_block {
    .default_ask_list {
        margin-bottom: 15px;

        &:last-child {
            margin-bottom: 0;
        }

        h4 {
            margin: 0 0 5px;
            display: flex;
            align-items: center;

            img {
                margin-right: 8px;
            }

            span {
                flex: 1;
            }

            a {
                color: #409eff;
                cursor: pointer;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        p {
            margin: 0;
            padding-left: 24px;
            color: #666;
            font-size: 14px;
        }
    }
}
</style>

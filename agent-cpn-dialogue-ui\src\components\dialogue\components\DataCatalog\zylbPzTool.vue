<template>
    <thinkingContentStep :stepTitle="'资源类别配置工具'">
        <template #titleSlot></template>
        <template #content>
            <!-- 查看表单内容 -->
            <div class="my_form_default">
                <table class="dl_table_style">
                    <tbody>
                        <tr>
                            <td class="common_label_title_r td_width">
                                <b class="red">*</b>
                                目录分类：
                            </td>
                            <td>工信局和科创局</td>
                            <td class="common_label_title_r td_width">
                                <b class="red">*</b>
                                应用系统：
                            </td>
                            <td>政企服务平台</td>
                        </tr>
                        <tr>
                            <td class="common_label_title_r td_width">
                                <b class="red">*</b>
                                来源数据库：
                            </td>
                            <td>政策原文表</td>
                            <td class="common_label_title_r td_width">模式名称：</td>
                            <td>xxxods层</td>
                        </tr>
                        <tr>
                            <td class="common_label_title_r td_width">
                                <b class="red">*</b>
                                表名：
                            </td>
                            <td>政策原文表</td>
                            <td class="common_label_title_r td_width">
                                <b class="red">*</b>
                                数据范围：
                            </td>
                            <td>xxxods层</td>
                        </tr>
                        <tr>
                            <td class="common_label_title_r td_width">
                                <b class="red">*</b>
                                数据分级：
                            </td>
                            <td>政策原文表</td>
                            <td class="common_label_title_r td_width">
                                <b class="red">*</b>
                                时间范围：
                            </td>
                            <td>xxxods层</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="selectpzbtn">
                <a>
                    <label>
                        <input type="radio" />
                        更新配置
                    </label>
                </a>
                <a>
                    <label>
                        <input type="radio" />
                        更新配置
                    </label>
                </a>
            </div>
        </template>
        <template #secondSlot>
            <!-- <thinkingResult :resultData="'生成数据采集任务完成'"></thinkingResult> -->
        </template>
    </thinkingContentStep>
</template>
<script>
export default {
    name: 'zylbPzTool',
    data() {
        return {};
    },
    methods: {},
};
</script>
<style scoped lang="scss"></style>

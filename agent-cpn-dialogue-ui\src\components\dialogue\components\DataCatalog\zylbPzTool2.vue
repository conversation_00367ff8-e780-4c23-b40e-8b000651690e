<template>
    <thinkingContentStep :stepTitle="'资源类别配置工具'">
        <template #titleSlot></template>
        <template #content>
            <!-- 查看表单内容 -->
            <div class="my_form_default">
                <table class="dl_table_style">
                    <tbody>
                        <tr>
                            <td class="common_label_title_r td_width">应用系统：</td>
                            <td>工信局和科创局</td>
                            <td class="common_label_title_r td_width">服务：</td>
                            <td>政企服务平台</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </template>
        <template #secondSlot>
            <!-- <thinkingResult :resultData="'生成数据采集任务完成'"></thinkingResult> -->
        </template>
    </thinkingContentStep>
</template>
<script>
export default {
    name: 'zylbPzTool',
    data() {
        return {};
    },
    methods: {},
};
</script>
<style scoped lang="scss"></style>

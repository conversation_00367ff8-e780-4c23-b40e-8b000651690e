<template>
    <mlPzTool
        :title="title"
        :defaultFieldsConfig="defaultFieldsConfig"
        :resourceInfo="resourceConfig"
        :other="other"
        @edit="handleEdit"
        @confirm="handleConfirm"
        @detailConfig="handleDetail"
    />
</template>

<script setup name="zymlConfig">
import { onMounted, ref } from 'vue';

// 目录基本信息配置工具
import { DialogueService } from '@/components/dialogue/hooks/useSystemResponse';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
import mlPzTool from '@/components/dialogue/common/mlPzTool.vue';
import { useDialogueHandler } from '@/components/dialogue/hooks/useDialogueHandler';
import { generateDialogueMsg, reply } from '@/api/dialogue';
import { useSessionStore } from '@/components/dialogue/store/modules/session.js';
import { useUserStore } from '@/components/dialogue/store/modules/user';

const userStore = useUserStore();
const sessionStore = useSessionStore();
const { setRightPanel, hideRightPanel } = useRightPanelStore();
const { replySendWs } = useDialogueHandler();

const props = defineProps({
    /**
     * 组件标题
     */
    title: {
        type: String,
        default: '资源类别配置工具',
    },
    /**
     * 资源数据
     */
    resourceData: {
        type: Object,
        default: () => ({}),
    },
    data: {
        type: Object,
        default: () => ({}),
    },
});

// Emits定义
const emit = defineEmits(['handleShow']);

// 默认字段配置，根据图片所示内容
const defaultFieldsConfig = ref([
    { key: 'category', label: '目录分类', required: true, isShow: true },
    { key: 'system', label: '应用系统', required: true, isShow: true },
    { key: 'db', label: '来源数据库', required: true, isShow: true },
    { key: 'schema', label: '模式名称', required: false, isShow: true },
    { key: 'table', label: '表名', required: true, isShow: true },
    { key: 'range', label: '数据范围', required: true, isShow: true },
    { key: 'level', label: '数据分级', required: true, isShow: true },
    { key: 'dateRange', label: '时间范围', required: true, isShow: true },
]);
let other = 'source';
// 资源配置数据，与图片匹配
const resourceConfig = ref({});
// let data = {
//     actionCode: 'catalogSourceGenAction',
//     msg: '123',
//     entity: {
//         name: '企业基本信息目录对话生成2',
//         resEngName: '',
//         resAbstract: '',
//         deptId: '1905183679240470529',
//         resKeywords: '',
//         resType: 3,
//         subjectType: 1,
//         baseType: '1281111441857159170',
//         shareType: 3,
//         shareCondition: '',
//         openType: 0,
//         updateCycle: 2,
//         subed: false,
//     },
// };
// 处理详细配置点击事件
const handleDetail = () => {
    setRightPanel(
        'updataConfig',
        {
            formData: resourceConfig.value,
            title: '详细配置信息',
        },
        {
            // 自定义事件处理函数
            confirm: formData => {
                let mapping = {
                    getWay: formData.dsjcqfs,
                    updateFiled: formData.ywgxsjzd,
                    incrementFiled: formData.zlcqzd,
                    isMain: formData.sfwzsj,
                    isThisSystem: formData.sfbxtcssj,
                    isOtherSub: formData.sfwbdysj,
                    subSystem: formData.dylyxt,
                    subDept: formData.dylydy,
                    subTable: formData.dylybmc,
                };
                // 更新资源配置
                resourceConfig.value = {
                    ...resourceConfig.value,
                    ...mapping,
                };
            },
            cancel: () => {
                console.log('用户取消了编辑');
            },
        }
    );
};

/**
 * 显示编辑采集任务
 */
const handleEdit = () => {
    setRightPanel(
        'editCategory',
        {
            formData: resourceConfig.value,
            title: '编辑目录资源信息',
            config: {
                title: '编辑目录资源信息',
                successMessage: '目录资源信息更新成功',
            },
        },
        {
            // 自定义事件处理函数
            confirm: formData => {
                let mapping = {
                    catalogType: formData.category,
                    sourceSystemId: formData.appSystem,
                    sourceDatabaseId: formData.sourceDatabase,
                    sourceFileServerId: formData.sourceFileServer,
                    serviceId: formData.service,
                    sourceFolderPath: formData.sourceFolderPath,
                    sourceSchema: formData.schemaName,
                    sourceTableName: formData.tableName,
                    dataRange: formData.dataScope,
                    dataLevel: formData.dataLevel,
                    timeRange: formData.dateRange,
                };
                // 更新资源配置
                Object.assign(resourceConfig.value, mapping);
                // 可以在这里执行其他操作，如调用API保存数据等
                // 更新配置
                makeConfig(mapping.catalogType);
            },
            cancel: () => {
                console.log('用户取消了编辑');
            },
        }
    );
};

// 处理确认按钮点击
const handleConfirm = () => {
    // 调用生成目录函数
    generateCatalog();
};

const dataEnd = {
    actionCode: 'catalogCreateAction',
    msg: '确认生成目录',
    entity: {
        name: '企业基本信息目录对话生成1',
        resEngName: '',
        resAbstract: '',
        deptId: '1905183679240470529',
        resKeywords: '',
        resType: 3,
        catalogType: 1,
        shareType: 3,
        shareCondition: '',
        sourceSystemId: '1425411186199318529',
        sourceDatabaseId: '1909521746676019202',
        sourceSchema: '',
        sourceTableName: 'sys_web_enterprise_baseinfo',
        openType: 0,
        updateCycle: 2,
        subed: false,
        resItems: [
            {
                name: '统一社会信用代码',
                itemEngName: 'shxydm',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 1,
            },
            {
                name: '上市板块',
                itemEngName: 'ssbk',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 2,
            },
            {
                name: '上市代码',
                itemEngName: 'ssdm',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 3,
            },
            {
                name: '所属街道',
                itemEngName: 'ssjd',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 4,
            },
            {
                name: '上市情况',
                itemEngName: 'ssqk',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 5,
            },
            {
                name: '商事主体类型',
                itemEngName: 'ssztlx',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 6,
            },
            {
                name: '员工数',
                itemEngName: 'staffnum',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 7,
            },
            {
                name: '研发机构数量',
                itemEngName: 'yfjgs',
                dataFormat: 'INT',
                dataLength: 10,
                isPk: 0,
                isNull: 1,
                editId: 8,
            },
            {
                name: '营业状态',
                itemEngName: 'yyzt',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 9,
            },
            {
                name: '总部地址',
                itemEngName: 'zbdz',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 10,
            },
            {
                name: '注册地址',
                itemEngName: 'zcdz',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 11,
            },
            {
                name: '注册机关',
                itemEngName: 'zcjg',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 12,
            },
            {
                name: '注册时间',
                itemEngName: 'zcsj',
                dataFormat: 'TIMESTAMP',
                dataLength: 19,
                isPk: 0,
                isNull: 1,
                editId: 13,
            },
            {
                name: '注册资本(万)',
                itemEngName: 'zczb',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 14,
            },
            {
                name: '行业名称-统计局',
                itemEngName: 'hymc_from_tjj',
                dataFormat: 'VARCHAR',
                dataLength: 200,
                isPk: 0,
                isNull: 1,
                editId: 15,
            },
        ],
        dataLevel: 2,
        timeRange: ['2025-01-01 20:00:00', '2025-12-31 23:59:59'],
        getWay: 1,
        updateFiled: 'time',
        incrementFiled: 'time',
        isMain: true,
        isThisSystem: true,
        isOtherSub: true,
        subSystem: 'asd',
        subDept: 'asd',
        subTable: 'asd',
    },
};

const handleSubmit = async () => {
    console.log('submit');
};

const generateCatalog = async () => {
    let userInfo = userStore.userInfo;
    let dialogueId = sessionStore.dialogueId;
    let data = {
        actionCode: 'catalogCreateAction',
        msg: '确认生成目录',
        messageId: props.data.messageId,
        userId: userInfo.user_id,
        dialogueId: dialogueId,
        entity: {
            ...props.data.result.entity,
            ...resourceConfig.value,
        },
    };
    //提前生成对话ID和消息ID
    generateDialogueMsg(data).then(res => {
        if (res.data.code === 200) {
            data['messageId'] = res.data.data.messageId;

            const userMessage = `确认生成数据编目`;
            DialogueService.addUserQuestion(userMessage);
            replySendWs(data);
            reply(data);
            // DialogueService.addSystemResponse({
            //   container: res.messageId,
            //   components: [
            //     {
            //       name: 'cjTaskRecommend',
            //     },
            //   ],
            // });
        }
    });
};

const makeConfig = type => {
    if (type) {
        switch (type) {
            case 1:
                defaultFieldsConfig.value = [
                    { key: 'category', label: '目录分类', required: true },
                    { key: 'system', label: '应用系统', required: true },
                    { key: 'db', label: '来源数据库', required: true },
                    { key: 'schema', label: '模式名称', required: false },
                    { key: 'table', label: '表名', required: true },
                    { key: 'range', label: '数据范围', required: true },
                    { key: 'level', label: '数据分级', required: true },
                    { key: 'dateRange', label: '时间范围', required: true },
                ];
                break;
            case 2:
                defaultFieldsConfig.value = [
                    { key: 'category', label: '目录分类', required: true },
                    { key: 'system', label: '应用系统', required: true },
                    { key: 'file', label: '文件系统', required: true },
                    { key: 'path', label: '文件路径', required: true },
                ];
                break;
            case 3:
                defaultFieldsConfig.value = [
                    { key: 'category', label: '目录分类', required: true },
                    { key: 'system', label: '应用系统', required: true },
                    { key: 'service', label: '接口', required: true },
                ];
                break;
        }
    }
};

// 组件挂载时处理数据
onMounted(() => {
    // 如果有传入的resourceData，则使用传入的数据
    if (props.data && Object.keys(props.data).length > 0) {
        resourceConfig.value = {
            // ...resourceConfig.value,
            ...props.data.result.entity,
        };
        makeConfig(props.data.result.entity.catalogType);
    }
});
</script>

<style lang="scss" scoped></style>

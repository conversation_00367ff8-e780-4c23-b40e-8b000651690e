<template>
    <thinkingContentStep :stepTitle="'采集任务生成工具'">
        <template #titleSlot></template>
        <template #content>
            <!-- 查看表单内容 -带背景-->
            <cj-form-table v-model:config="formConfig" @action="handleAction" @change="handleChange" />
            <div class="task_thinking_btn">
                <!-- <a class="btn0">取消</a> -->
                <a class="btn1" @click="handleEdit">修改</a>
                <a class="btn2" @click.once="handleConfirm">确认</a>
            </div>
        </template>
    </thinkingContentStep>
</template>
<script setup name="aggTaskGenerateTool">
/**
 * 采集任务生成工具组件
 * 显示数据采集任务的配置和生成结果
 *
 * 该组件展示了数据采集任务的详细参数，包括待采集表、目标表和汇聚方式
 */
import { ref, watch } from 'vue';
import thinkingContentStep from '@/components/dialogue/common/thinkingContentStep.vue';
import CjFormTable from '@/components/dialogue/components/fromComponents/DataCollection/cj-form-table.vue';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
import { getDictionary } from '@/api/system/dict';
import { getColumns } from '@/api/dialogue/data-cpn-util-api';
import { useUserStore } from '@/components/dialogue/store/modules/user';
import { useDialogueHandler } from '@dialogue/hooks/useDialogueHandler.js';

const userStore = useUserStore();
const { handleActionReply } = useDialogueHandler();

// 获取右侧面板状态管理
const { setRightPanel } = useRightPanelStore();

// 定义组件的props
const props = defineProps({
    data: {
        type: Object,
        default: () => {},
    },
});
const formConfig = ref([
    {
        id: 'sourceTable',
        type: 'display',
        label: '待采集表',
        value: '',
        tableName: '',
        database: '',
        showConfig: false,
        showPreview: true,
        isShow: true,
        colspan: 5,
    },
    {
        id: 'targetTable',
        type: 'display',
        label: '目标表',
        value: '',
        tableName: '',
        database: '',
        showConfig: false,
        showPreview: true,
        isShow: true,
        colspan: 5,
    },
    {
        id: 'collectType',
        type: 'radio',
        label: '汇聚方式',
        name: 'collectType',
        value: '',
        options: [],
        isShow: true,
        colspan: 5,
    },
    {
        id: 'incrementType',
        type: 'select',
        label: '增量机制',
        name: 'incrementType',
        value: '',
        options: [],
        isShow: false,
        colspan: 5,
    },
    {
        id: 'sourceIncrementField',
        type: 'select',
        label: '源表标识字段',
        name: 'sourceIncrementField',
        value: '',
        options: [],
        isShow: false,
        colspan: 5,
    },
    {
        id: 'pendingMark',
        type: 'input',
        label: '待汇聚标识',
        name: 'pendingMark',
        value: '',
        isShow: false,
        colspan: 5,
    },
    {
        id: 'completedMark',
        type: 'input',
        label: '已汇聚标识',
        name: 'completedMark',
        value: '',
        isShow: false,
        colspan: 5,
    },
]);
const rightPanelTitle = ref('');
const aggregrationTaskInfo = ref({});
const sourceTableColumn = ref([]);

const sourceTableIndex = formConfig.value.findIndex(item => item.id === 'sourceTable');
const targetTableIndex = formConfig.value.findIndex(item => item.id === 'targetTable');
const collectTypeIndex = formConfig.value.findIndex(item => item.id === 'collectType');
const incrementTypeIndex = formConfig.value.findIndex(item => item.id === 'incrementType');
// 找到源表标识字段的变更
const sourceIncrementFieldIndex = formConfig.value.findIndex(item => item.id === 'sourceIncrementField');
const pendingMarkIndex = formConfig.value.findIndex(item => item.id === 'pendingMark');
const completedMarkIndex = formConfig.value.findIndex(item => item.id === 'completedMark');
watch(
    () => props.data,
    async () => {
        console.log(props.data);
        // 在组件挂载时处理props.data
        const swapTypeDict = await getDictionary({ code: 'aggregation_mode' }).then(res => {
            let data = res.data.data;
            return data;
        });
        const incrementTypeDict = await getDictionary({ code: 'incremental_mechanism' }).then(res => {
            let data = res.data.data;
            return data;
        });
        let result = props.data.result;
        //将result中的数据赋值给aggregrationInfo.value，用于下一步的输入
        aggregrationTaskInfo.value = result;

        await getColumns(result.sourceDatabaseUnid, result.sourceTableObj.name).then(res => {
            const result = res.data;
            if (result.success) {
                result.data.forEach(val => {
                    sourceTableColumn.value.push({
                        dictKey: val.name,
                        dictValue: val.comment,
                    });
                });
            }
        });

        // 设置源数据表数据
        formConfig.value[sourceTableIndex].value =
            result.sourceTableObj.comment + '(' + result.sourceTableObj.name + ')';
        formConfig.value[sourceTableIndex].tableName = result.sourceTableObj.name;
        formConfig.value[sourceTableIndex].database = result.sourceDatabaseUnid;

        //设置目标数据表数据
        formConfig.value[targetTableIndex].value =
            result.targetTableObj.comment + '(' + result.targetTableObj.name + ')';
        formConfig.value[targetTableIndex].tableName = result.targetTableObj.name;
        formConfig.value[targetTableIndex].database = result.targetDatabaseUnid;

        //设置汇聚方式
        formConfig.value[collectTypeIndex].value = result.swapType;
        formConfig.value[collectTypeIndex].options = swapTypeDict;

        // 设置增量机制
        formConfig.value[incrementTypeIndex].value = result.incrementType;
        formConfig.value[incrementTypeIndex].options = incrementTypeDict;
        // 根据汇聚方式显示增量机制
        if (result.swapType === '2') {
            formConfig.value[incrementTypeIndex].isShow = true;
        } else {
            formConfig.value[sourceIncrementFieldIndex].isShow = false;
            formConfig.value[pendingMarkIndex].isShow = false;
            formConfig.value[completedMarkIndex].isShow = false;
        }

        // 设置源表标识字段
        formConfig.value[sourceIncrementFieldIndex].value = result.sourceIncrementField;
        formConfig.value[sourceIncrementFieldIndex].options = sourceTableColumn.value;
        // 增量机制为”按标识增量“时，显示源表标识字段配置项
        if (result.incrementType === '2') {
            formConfig.value[sourceIncrementFieldIndex].isShow = true;
            formConfig.value[pendingMarkIndex].isShow = true;
            formConfig.value[completedMarkIndex].isShow = true;
        } else {
            formConfig.value[sourceIncrementFieldIndex].isShow = false;
            formConfig.value[pendingMarkIndex].isShow = false;
            formConfig.value[completedMarkIndex].isShow = false;
        }

        // 设置待汇聚标识
        formConfig.value[pendingMarkIndex].value = result.pendingMark;

        // 设置已汇聚标识
        formConfig.value[completedMarkIndex].value = result.completedMark;
    },
    {
        deep: true,
        immediate: true,
    }
);

/**
 * 处理通用操作事件
 * @param {Object} payload - 事件载荷对象
 */
const handleAction = payload => {
    const { action, item } = payload;

    console.log(`点击: ${action}，配置项: `, item);

    // 根据不同操作类型执行对应处理
    if (action === 'config') {
        handleConfig(item);
    } else if (action === 'preview') {
        if (item.id === 'sourceTable') {
            handlePreviewSource(item);
        } else if (item.id === 'targetTable') {
            handlePreviewTarget(item);
        }
    }
};
/**
 * 处理通用属性修改事件
 * @param {Object} payload - 事件载荷对象
 */
const handleChange = payload => {
    const { propertyName, propertyValue } = payload;
    console.log(`属性变更: ${propertyName}，新值: ${propertyValue}`);

    // 根据不同操作类型执行对应处理
    if (propertyName === 'collectType') {
        formConfig.value[collectTypeIndex].value = propertyValue;
        if (propertyValue === '2') {
            formConfig.value[incrementTypeIndex].isShow = true;
            if (formConfig.value[incrementTypeIndex].value === '2') {
                formConfig.value[sourceIncrementFieldIndex].isShow = true;
                formConfig.value[pendingMarkIndex].isShow = true;
                formConfig.value[completedMarkIndex].isShow = true;
            }
        } else {
            formConfig.value[sourceIncrementFieldIndex].isShow = false;
            formConfig.value[pendingMarkIndex].isShow = false;
            formConfig.value[completedMarkIndex].isShow = false;
        }
    } else if (propertyName === 'incrementType') {
        formConfig.value[incrementTypeIndex].value = propertyValue;
        // 增量机制为”按标识增量“时，显示源表标识字段配置项
        if (propertyValue === '2') {
            formConfig.value[sourceIncrementFieldIndex].isShow = true;
            formConfig.value[pendingMarkIndex].isShow = true;
            formConfig.value[completedMarkIndex].isShow = true;
            // return;
        } else {
            formConfig.value[sourceIncrementFieldIndex].isShow = false;
            formConfig.value[pendingMarkIndex].isShow = false;
            formConfig.value[completedMarkIndex].isShow = false;
            // return;
        }
    }
};

/**
 * 处理配置按钮点击事件
 * @param {Object} item - 配置项
 */
const handleConfig = item => {
    console.log(`配置按钮点击，配置项ID: ${item.id}，当前值: ${item.value}`);
    // 这里可以实现打开配置弹窗等逻辑
};

/**
 * 处理预览源表按钮点击事件
 * @param {Object} item - 配置项
 */
const handlePreviewSource = item => {
    console.log(`预览源表: ${item.value}`);
    rightPanelTitle.value = item.value;
    let data = {
        actionCode: 'tablePreview',
        // msg: '确认指标基本信息',
        dialogueId: props.data.dialogueId,
        params: {
            tableName: item.tableName,
            databaseId: item.database,
        },
        callBack: previewTable,
    };
    handleActionReply(data);
};

const previewTable = result => {
    console.log('previewTable', result);
    let tableRow = result.data.columns;
    let tableData = result.data.rowData;
    // 可以实现预览源表数据的逻辑
    setRightPanel('policiesYwb', {
        title: rightPanelTitle.value,
        rowData: {
            tableRow: tableRow,
            tableData: tableData,
        },
    });
};

/**
 * 处理预览目标表按钮点击事件
 * @param {Object} item - 配置项
 */
const handlePreviewTarget = item => {
    console.log(`预览源表: ${item.value}`);
    rightPanelTitle.value = item.value;
    let data = {
        actionCode: 'tablePreview',
        // msg: '确认指标基本信息',
        dialogueId: props.data.dialogueId,
        params: {
            tableName: item.tableName,
            databaseId: item.database,
        },
        callBack: previewTable,
    };
    handleActionReply(data);
};

/**
 * 处理修改按钮点击事件
 * 显示右侧编辑采集任务组件
 */
const handleEdit = () => {
    // 打开右侧编辑面板，显示editCjrw组件
    setRightPanel('editCjrw', {
        title: '编辑采集任务',
        formData: formConfig.value,
        confirmCallback: confirmCallback,
    });
};
const confirmCallback = editedFormConfig => {
    formConfig.value = editedFormConfig;
    console.log('confirmCallback', editedFormConfig);
};

/**
 * 处理确认按钮点击事件
 */
const handleConfirm = () => {
    // addUserQuestion('确认生成数据采集任务');
    const userInfo = userStore.userInfo;
    let data = {
        actionCode: 'generateCollectionTask',
        msg: '确认生成数据采集任务',
        dialogueId: props.data.dialogueId,
        userId: userInfo.user_id,
        entity: aggregrationTaskInfo.value,
    };
    handleActionReply(data);
};

/**
 * 处理汇聚方式变更事件
 * @param {string} type - 汇聚方式类型
 */
const handleCollectTypeChange = type => {
    console.log(`汇聚方式变更为: ${type}`);
    // 根据汇聚方式变更执行相应逻辑
};
</script>
<style scoped lang="scss">
/* 可以添加组件特定的样式 */
</style>

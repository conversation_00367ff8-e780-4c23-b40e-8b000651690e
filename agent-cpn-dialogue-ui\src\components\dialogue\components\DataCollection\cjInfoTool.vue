<template>
    <thinkingContentStep :stepTitle="title">
        <template #content>
            <div class="my_form_default">
                <table class="dl_table_style">
                    <tbody>
                        <tr v-for="(row, rowIndex) in formattedData" :key="rowIndex" class="table-tr">
                            <template v-for="(item, itemIndex) in row" :key="itemIndex">
                                <td class="common_label_title_r">{{ item.label }}：</td>
                                <td class="r-value-weight">{{ item.value }}</td>
                            </template>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="task_thinking_btn" v-if="showButton">
                <a class="btn1" @click="handleEdit">{{ buttonConfig.editText }}</a>
                <a class="btn2" @click.once="handleConfirm">{{ buttonConfig.confirmText }}</a>
            </div>
        </template>
    </thinkingContentStep>
</template>

<script setup>
import { computed } from 'vue';
import thinkingContentStep from '@/components/dialogue/common/thinkingContentStep.vue';

defineOptions({
    name: 'CjInfoTool',
});

/**
 * @typedef {Object} InfoItem
 * @property {string} label - 信息项标签
 * @property {string} value - 信息项值
 */

/**
 * @typedef {Object} ButtonConfig
 * @property {string} editText - 编辑按钮文本
 * @property {string} confirmText - 确认按钮文本
 */

// 定义组件的props
const props = defineProps({
    data: {
        type: Object,
        default: () => {},
    },
});

const title = ref('采集信息查询工具');
const infoData = ref([]);
const formattedData = ref([]);
const configData = ref({
    editText: '修改',
    confirmText: '确认',
});
const showButton = ref(false);
const itemsPerRow = ref(2);
const clickEdit = ref(null);
const clickConfirm = ref(null);

// const props = defineProps({
//     /**
//      * @description 组件标题
//      */
//     title: {
//         type: String,
//         default: '采集信息查询工具',
//     },
//     /**
//      * @description 要显示的信息数据数组
//      */
//     infoData: {
//         type: Array,
//         default: () => [
//             { label: '来源部门', value: '工信局和科创局' },
//             { label: '数据源', value: '政企服务平台' },
//             { label: '数据库', value: '达梦dm-0x7000c' },
//             { label: '待采集数据表', value: '政策原文表' },
//             { label: '目标数据库', value: 'xxxods层' },
//             { label: '目标数据表', value: '贴源层-政策原文表' },
//         ],
//     },
//     /**
//      * @description 按钮配置对象
//      */
//     configData: {
//         type: Object,
//         default: () => ({
//             editText: '修改',
//             confirmText: '确认',
//         }),
//     },
//     /**
//      * @description 是否显示操作按钮
//      */
//     showButton: {
//         type: Boolean,
//         default: false,
//     },
//     /**
//      * @description 每行显示的项目数
//      */
//     itemsPerRow: {
//         type: Number,
//         default: 3,
//     },
//     /**
//      * @description 自定义编辑事件处理函数
//      */
//     clickEdit: {
//         type: Function,
//         default: null,
//     },
//     /**
//      * @description 自定义确认事件处理函数
//      */
//     clickConfirm: {
//         type: Function,
//         default: null,
//     },
// });

const emit = defineEmits(['edit', 'confirm']);

watch(
    () => props.data,
    () => {
        // 在组件挂载时处理props.data
        let result = props.data.result;

        const rows = [];
        infoData.value = [
            { label: '来源数据源', value: result.sourceDatabase },
            { label: '待采集数据表', value: result.sourceTable },
            { label: '目标数据源', value: result.targetDatabase },
            { label: '目标数据表', value: result.targetTable },
        ];
        // 将项目分组为行
        for (let i = 0; i < infoData.value.length; i += itemsPerRow.value) {
            rows.push(infoData.value.slice(i, i + itemsPerRow.value));
        }
        formattedData.value = rows;
    },
    {
        deep: true,
        immediate: true,
    }
);

/**
 * @description 按钮配置的计算属性
 */
const buttonConfig = computed(() => props.configData || { editText: '修改', confirmText: '确认' });

/**
 * @description 处理编辑按钮点击事件
 */
const handleEdit = () => {
    if (props.clickEdit) {
        props.clickEdit();
    } else {
        emit('edit');
    }
};

/**
 * @description 处理确认按钮点击事件
 */
const handleConfirm = () => {
    if (props.clickConfirm) {
        props.clickConfirm();
    } else {
        emit('confirm');
    }
};
</script>

<style scoped lang="scss">
.my_form_default {
    margin-bottom: 15px;
}

.dl_table_style {
    width: 100%;
    border-collapse: collapse;

    .table-tr {
        line-height: 28px;
    }

    .common_label_title_r {
        color: #666;
        text-align: right;
        padding-right: 8px;
        white-space: nowrap;
        width: 100px;
    }

    .r-value-weight {
        color: #333;
        font-weight: 500;
        padding-right: 20px;
    }
}

.task_thinking_btn {
    display: flex;
    justify-content: center;
    margin-top: 15px;

    .btn1,
    .btn2 {
        padding: 6px 16px;
        margin: 0 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }

    .btn1 {
        background-color: $bg;
        border: 1px solid #dcdfe6;
        color: #606266;

        &:hover {
            color: #409eff;
            border-color: #c6e2ff;
            background-color: #ecf5ff;
        }
    }

    .btn2 {
        background-color: #409eff;
        border: 1px solid #409eff;
        color: $bg;

        &:hover {
            background-color: #66b1ff;
            border-color: #66b1ff;
        }
    }
}
</style>

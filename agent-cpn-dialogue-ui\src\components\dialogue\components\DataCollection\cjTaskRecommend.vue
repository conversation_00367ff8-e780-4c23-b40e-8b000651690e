<template>
    <thinkingContentStep :stepTitle="title">
        <template #titleSlot></template>
        <template #content>
            <div class="default_ask_block">
                <div class="default_ask_list" v-for="(item, index) in taskList" :key="index">
                    <h4>
                        <img :src="item.icon || publicPath + '/static/dialogue/default_icon.png'" />
                        <span>{{ item.title }}</span>
                        <a @click="handleTaskSelect(item)">{{ actionText }} ></a>
                    </h4>
                    <p>{{ item.description }}</p>
                </div>
            </div>
        </template>
        <template #secondSlot>
            <!-- <thinkingResult :resultData="'生成数据采集任务完成'"></thinkingResult> -->
        </template>
    </thinkingContentStep>
</template>

<script setup name="cjTaskRecommend">
/**
 * 任务推荐组件
 * @description 用于展示推荐任务列表，支持自定义任务数据和点击操作
 */
import { ref, computed } from 'vue';
import thinkingContentStep from '@/components/dialogue/common/thinkingContentStep.vue';
import { publicPath } from '@/components/dialogue/store/main.js';

// 定义Props
const props = defineProps({
    /**
     * 组件标题
     */
    title: {
        type: String,
        default: '任务推荐',
    },
    /**
     * 任务列表数据
     */
    tasks: {
        type: Array,
        default: () => [],
    },
    /**
     * 操作按钮文本
     */
    actionText: {
        type: String,
        default: '去生成',
    },
});

// 定义事件
const emit = defineEmits(['taskSelect']);

// 默认任务列表
const defaultTasks = [
    {
        title: '01.数据加工处理',
        description: '根据上述任务信息，帮你的数据进行处理',
        icon: publicPath + '/static/dialogue/default_icon.png',
        type: 'dataProcessing',
    },
    {
        title: '02.API生成',
        description: '根据上述任务信息，帮你生成你想要的API',
        icon: publicPath + '/static/dialogue/default_icon.png',
        type: 'apiGeneration',
    },
];

// 计算得到最终的任务列表
const taskList = computed(() => {
    return props.tasks && props.tasks.length > 0 ? props.tasks : defaultTasks;
});

/**
 * 处理任务选择
 * @param {Object} task - 选择的任务项
 */
const handleTaskSelect = task => {
    emit('taskSelect', task);
};
</script>

<style scoped lang="scss">
.default_ask_block {
    padding: 10px 0;
}
.default_ask_list {
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 12px 16px;
    margin-bottom: 12px;

    h4 {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-weight: 500;

        img {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }

        span {
            flex: 1;
            font-size: 14px;
            color: #333;
        }

        a {
            cursor: pointer;
            color: #409eff;
            font-size: 13px;

            &:hover {
                text-decoration: underline;
            }
        }
    }

    p {
        margin: 0;
        font-size: 13px;
        color: #666;
        line-height: 1.5;
    }
}
</style>

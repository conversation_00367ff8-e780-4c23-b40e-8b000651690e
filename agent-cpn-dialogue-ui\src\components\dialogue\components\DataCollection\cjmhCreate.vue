<template>
    <div class="ask_title">
        <div>{{ title }}</div>
    </div>
    <div class="cjmh-create-container">
        <data-selection-form ref="selectionForm" :template="template" />
        <div v-if="showButton" class="btn-container">
            <el-button type="primary" class="generate-btn" @click="generateTask">
                <img :src="publicPath + `/static/dialogue/task_btnimg.png`" class="btn-icon" />
                {{ buttonText }}
            </el-button>
        </div>
    </div>
</template>

<script setup name="CjmhCreate">
import { ref } from 'vue';
import DataSelectionForm from '@/components/dialogue/common/DataSelectionForm.vue';
import { publicPath } from '@/components/dialogue/store/main.js';

const props = defineProps({
    /**
     * 是否显示按钮
     */
    showButton: {
        type: Boolean,
        default: true,
    },
    /**
     * 按钮文本
     */
    buttonText: {
        type: String,
        default: '生成数据采集任务',
    },
    /**
     * 标题文本
     */
    title: {
        type: String,
        default: '好的，为你生成一个数据采集任务，请根据你的需求描述生成的数据采集信息。',
    },
    /**
     * 表单模板
     */
    template: {
        type: String,
        default:
            '帮我将{source:源名}数据源，{db:库名}数据库，{table:表名}数据表的数据以{format:全量}的方式汇聚到{targetSource:源名}数据源，{targetDb:库名}数据库，{targetTable:表名}数据表。',
    },
});

// 引用表单组件
const selectionForm = ref(null);

/**
 * 生成任务
 */
const generateTask = () => {
    if (selectionForm.value && selectionForm.value.isFormValid) {
        const taskConfig = selectionForm.value.getFormData();
        emit('create-task', taskConfig);
    }
};

// 定义组件事件
const emit = defineEmits(['create-task']);
</script>

<style scoped lang="scss">
.ask_title {
    font-size: 14px;
    margin-bottom: 16px;
}

.cjmh-create-container {
    border-radius: 4px;
}

.btn-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    button {
        height: 28px;
        padding: 0px 16px;
        font-size: 14px;
        gap: 4px;
        background: linear-gradient(90deg, #00c8ff 0%, #1570ef 100%);
        border-radius: 14px;
    }
}
</style>

<template>
    <thinkingContentStep :stepTitle="title">
        <template #content>
            <!-- 查看表单内容 -->
            <div class="my_form_default">
                <table class="dl_table_style">
                    <tbody>
                        <tr>
                            <td class="common_label_title_r td_width0">生成结果：</td>
                            <td colspan="4">
                                <p class="td_block_box">
                                    <span v-if="resultStatus === 'success'" class="td_block td_wc_color">
                                        {{ resultContent }}
                                    </span>
                                    <span v-if="resultStatus === 'failure'" class="td_block td_fail_color">
                                        {{ resultContent }}
                                    </span>
                                </p>
                            </td>
                        </tr>
                        <!--                        <tr>-->
                        <!--                            <td class="common_label_title_r td_width0" style="vertical-align: top">采集进度：</td>-->
                        <!--                            <td colspan="5" style="padding-top: 7px">-->
                        <!--                                <div class="progress_box">-->
                        <!--                                    <el-progress :percentage="percentage" :color="customColor" />-->
                        <!--                                    <p class="progress_info">-->
                        <!--                                        {{ progressInfo }}-->
                        <!--                                        <a v-if="showPreview" @click="handlePreview">预览数据</a>-->
                        <!--                                    </p>-->
                        <!--                                </div>-->
                        <!--                            </td>-->
                        <!--                        </tr>-->
                    </tbody>
                </table>
            </div>
            <!-- 提示信息 -->
            <p class="tip_info">{{ tipInfo }}</p>
        </template>
    </thinkingContentStep>
</template>

<script setup name="taskGenerateResult">
/**
 * 采集结果组件
 * @description 用于展示数据采集的结果和进度
 */
import { defineProps, computed } from 'vue';
import thinkingContentStep from '@/components/dialogue/common/thinkingContentStep.vue';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
import { getDictionary } from '@/api/system/dict.js';

// 获取右侧面板状态管理
const { setRightPanel } = useRightPanelStore();
// 定义组件的props
const props = defineProps({
    data: {
        type: Object,
        default: () => {},
    },
});
const resultStatus = ref('');
const resultContent = ref('');
const title = ref('采集任务生成结果');

watch(
    () => props.data,
    async () => {
        // 在组件挂载时处理props.data
        console.log('taskGenerateResult', props.data);
        resultStatus.value = props.data?.result.resultStatus;
        resultContent.value = props.data?.result.resultContent;
    },
    {
        deep: true,
        immediate: true,
    }
);

// 定义props
// const props = defineProps({
//     // 组件标题
//     title: {
//         type: String,
//         default: '采集任务生成结果',
//     },
//     // 执行结果状态
//     resultStatus: {
//         type: String,
//         default: '生成成功',
//     },
//     // 进度百分比
//     percentage: {
//         type: Number,
//         default: 65,
//     },
//     // 进度信息
//     progressInfo: {
//         type: String,
//         default: '数据采集完成，已汇聚到贴源层-政策原文表，点击',
//     },
//     // 是否显示预览按钮
//     showPreview: {
//         type: Boolean,
//         default: true,
//     },
//     // 提示信息
//     tipInfo: {
//         type: String,
//         default:
//             '提示：采集任务结束后，会在【历史记录】给您发消息通知，请注意查看。如需修改，请完成该采集任务后，重新发起对话。',
//     },
// });

// 自定义进度条颜色
const customColor = computed(() => {
    if (props.percentage < 30) {
        return '#909399';
    } else if (props.percentage < 70) {
        return '#e6a23c';
    } else {
        return '#67c23a';
    }
});

// 处理预览点击事件
const handlePreview = () => {
    console.log('预览数据');
    // 在右侧面板显示policiesYwb组件
    setRightPanel('policiesYwb', {
        title: '贴源层-政策原文表',
        // 可以根据需要传递其他数据
        sourceData: {
            tableName: '贴源层-政策原文表',
            totalCount: Math.round(props.percentage), // 使用进度百分比作为示例数据
        },
    });
};
</script>

<style scoped lang="scss">
.tip_info {
    color: #999;
    font-size: 14px;
    margin: 10px 0 0;
    padding-left: 5px;
}

.progress_box {
    width: 100%;

    .progress_info {
        margin: 5px 0 0;
        color: #666;

        a {
            color: #409eff;
            cursor: pointer;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}
</style>

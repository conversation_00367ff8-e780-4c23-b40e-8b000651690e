<template>
    <div class="message-wrapper">
        <slot />
    </div>
</template>

<script setup>
import { provide } from 'vue';

defineOptions({
    name: 'MessageWrapper',
});

const props = defineProps({
    messageId: {
        type: String,
        required: true,
    },
});

// 向子组件提供messageId
provide('messageId', props.messageId);
</script>

<style lang="scss" scoped>
.message-wrapper {
    // 透明包装器，不添加额外样式
}
</style>

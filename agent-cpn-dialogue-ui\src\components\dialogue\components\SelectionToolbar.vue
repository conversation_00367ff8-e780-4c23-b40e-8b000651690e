<template>
    <transition name="slide-up">
        <div v-if="hasSelectedMessages" class="selection-toolbar">
            <div class="toolbar-content">
                <div class="selection-info">
                    <span class="selected-count">已选择 {{ selectedCount }} 条消息</span>
                </div>

                <div class="toolbar-actions">
                    <el-button type="danger" size="small" @click="handleDelete" :loading="isDeleting">
                        <el-icon><Delete /></el-icon>
                        删除选中
                    </el-button>

                    <el-button size="small" @click="handleCancel">
                        <el-icon><Close /></el-icon>
                        取消选择
                    </el-button>
                    <el-button size="small" @click="handleSelectAll">
                        <el-icon><Check /></el-icon>
                        {{ isAllSelected ? '取消全选' : '全选' }}
                    </el-button>
                </div>
            </div>
        </div>
    </transition>
</template>

<script setup>
import { Delete, Download, DocumentCopy, Close, Check } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    hasSelectedMessages,
    selectedCount,
    selectedMessageIds,
    clearSelection,
    deleteSelectedMessages,
    selectAllMessages,
} from '@/components/dialogue/store/input';
import { inject, ref, computed } from 'vue';
import { useSessionStore } from '@/components/dialogue/store/modules/session';

const sessionStore = useSessionStore();

defineOptions({
    name: 'SelectionToolbar',
});

// 加载状态
const isDeleting = ref(false);

// 计算是否全选
const isAllSelected = computed(() => {
    if (!sessionStore.messageList.length) return false;
    // 只统计有id的消息
    const allIds = sessionStore.messageList.filter(m => m.id).map(m => m.id);
    return allIds.length > 0 && allIds.every(id => selectedMessageIds.value.includes(id));
});

// 处理删除选中消息
const handleDelete = async () => {
    try {
        await ElMessageBox.confirm(`确定要删除选中的 ${selectedCount.value} 条消息吗？此操作不可撤销。`, '确认删除', {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
        });

        isDeleting.value = true;

        // 执行删除操作
        const filteredList = deleteSelectedMessages(sessionStore.messageList);
        sessionStore.messageList.splice(0, sessionStore.messageList.length, ...filteredList);

        ElMessage.success(`已删除 ${selectedCount.value} 条消息`);
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除消息失败:', error);
            ElMessage.error('删除消息失败，请重试');
        }
    } finally {
        isDeleting.value = false;
    }
};

// 处理取消选择
const handleCancel = () => {
    clearSelection();
    ElMessage.info('已取消选择');
};

// 全选/取消全选
const handleSelectAll = () => {
    selectAllMessages(sessionStore.messageList);
};
</script>

<style lang="scss" scoped>
.selection-toolbar {
    position: fixed;
    bottom: 200px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid #e4e7ed;
    z-index: 1000;
    max-width: 90vw;
}

.toolbar-content {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 20px;
}

.selection-info {
    .selected-count {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
    }
}

.toolbar-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

// 动画效果
.slide-up-enter-active,
.slide-up-leave-active {
    transition: all 0.3s ease;
}

.slide-up-enter-from {
    transform: translateX(-50%) translateY(100%);
    opacity: 0;
}

.slide-up-leave-to {
    transform: translateX(-50%) translateY(100%);
    opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
    .toolbar-content {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }

    .toolbar-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
}
</style>

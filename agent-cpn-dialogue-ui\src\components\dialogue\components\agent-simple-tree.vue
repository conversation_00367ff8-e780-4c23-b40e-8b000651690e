<template>
    <ul class="customer_list_block">
        <li v-for="(item, index) in menuList" :key="index" class="list-li">
            <Icon :name="item.icon" size="20" class="item-icon"></Icon>
            <div class="list_block_word">
                <h3>{{ item.name }}</h3>
                <p class="info">{{ item.info }}</p>
            </div>

            <!-- 选项菜单 -->
            <div
                class="select_options_box"
                v-if="
                    item.selectOption && item.selectOption.length && showSelectOptions && selectedParentIndex === index
                "
            >
                <div class="select_options">
                    <div
                        class="select_option_item"
                        v-for="(option, optionIndex) in item.selectOption"
                        :key="optionIndex"
                        @click="selectOption(item, option, index, optionIndex)"
                    >
                        <span>{{ option.label }}</span>
                    </div>
                </div>
            </div>

            <!-- 二级菜单 -->
            <div
                class="block_children_box"
                :class="`childtree${index + 1}`"
                v-if="item.child.length && (!item.selectOption || !showSelectOptions || selectedParentIndex !== index)"
            >
                <div class="option-children">
                    <div
                        class="list_block_children"
                        v-for="(itemChild, childIndex) in item.child"
                        :key="childIndex"
                        @click="selectChildItem(item, index, itemChild, childIndex)"
                    >
                        <div class="mt-1">
                            <Icon :name="itemChild.icon" class="itemChild-icon"></Icon>
                        </div>
                        <div class="list_block_children_word">
                            <h4>{{ itemChild.childName }}</h4>
                            <h5>{{ itemChild.childInfo }}</h5>
                        </div>
                    </div>
                </div>
            </div>
        </li>
    </ul>
</template>

<script setup>
/**
 * 提供任务类型选择功能的树形导航组件
 */
import { ref } from 'vue';

defineOptions({
    name: 'agent-simple-tree',
});
const { selectChildItem, getSelectData } = useDialogInput();

// 状态变量
const showSelectOptions = ref(false);
const selectedParentIndex = ref(null);
const router = useRouter();
/**
 * 客户任务列表数据
 */
const menuList = ref([
    {
        name: '数据采集',
        info: '根据需求采集数据，查看采集情况',
        icon: 'cj',

        child: [
            {
                childName: '采集数据到数据表',
                childInfo: '将某库表里的数据采集到系统的数据表里',
                value: 'cj1', // 业务类型标识
                icon: 'cj1',
            },
            // {
            //     childName: '上传数据采集到系统库表',
            //     childInfo: '点击右侧按钮上传数据采集到系统数据表里',
            //     value: 'cj2', // 业务类型标识
            //     icon: 'cj2',
            // },
        ],
    },
    {
        name: '数据编目',
        info: '将数据生成目录，提供目录订阅',
        icon: 'sjbm',
        child: [
            {
                childName: '系统信息资源生成目录',
                childInfo: '将某库表里的数据生成资源目录',
                value: 'bm1', // 业务类型标识
                icon: 'ml1',
            },
            // {
            //     childName: '上传数据一本帐文件生成资源目录',
            //     childInfo: '上传数据表到对话页面自动生成资源目录',
            //     value: 'bm2', // 业务类型标识
            //     icon: 'ml2',
            // },
            // {
            //     childName: '上传非结构化数据文件生成资源目录',
            //     childInfo: '上传非结构化数据文件到对话页面自动生成资源目录',
            //     value: 'bm3', // 业务类型标识
            //     icon: 'ml3',
            // },
        ],
    },
    {
        name: '智能问数',
        info: '查询数据，生成可视化数据图表',
        icon: 'sjcx',

        selectOption: [
            // {
            //     label: '问指标',
            //     value: 'ws1',
            // },
            {
                label: '模板问数',
                value: 'ws2',
            },

            // {
            //     label: '问大屏看板',
            //     value: 'ws3',
            // },
        ],
        child: [
            // {
            //     childName: '问指标',
            //     childInfo: '帮助您用自然语言查询指标',
            //     value: 'ws1',
            //     icon: 'wzb',
            // },
            {
                childName: '模板问数',
                childInfo: '帮助您将数据导入您所需文本/表格文档模板',
                value: 'ws2',
                icon: 'mbws',
            },
            // {
            //     childName: '问大屏看板',
            //     childInfo: '帮助您用自然语言查询指标大屏',
            //     value: 'ws3',
            //     icon: 'wdp',
            // },
        ],
    },
    {
        name: '数据共享',
        info: '根据需求生成接口，提供共享服务',
        icon: 'zngx',

        child: [],
    },
    {
        name: '数据画像',
        info: '生成数据画像，查看企业总体情况',
        icon: 'sjhx',

        child: [],
    },
    {
        name: '数据打标',
        info: '生成标签，为想要的数据打标',
        icon: 'sjdb',

        child: [],
    },
    {
        name: '数据治理',
        info: '处理与加工数据，提高数据质量',
        icon: 'sjzl',

        child: [],
    },
]);

/**
 * 选择选项菜单项
 * @param {Object} parentItem - 父项数据
 * @param {Object} option - 选中的选项数据
 * @param {Number} parentIndex - 父项索引
 * @param {Number} optionIndex - 选项索引
 */
const selectOption = (parentItem, option, parentIndex, optionIndex) => {
    // 查找对应的子项
    const childItem = parentItem.child.find(child => child.value === option.value) || {};

    // 构建返回数据
    const data = {
        parent: parentItem,
        child: childItem,
        parentIndex: parentIndex,
        childIndex: parentItem.child.findIndex(child => child.value === option.value),
        value: option.value,
        fromSelectOption: true,
    };

    getSelectData(data);
    // 重置状态
    showSelectOptions.value = false;
    selectedParentIndex.value = null;
};
</script>

<style lang="scss" scoped>
.customer_list_block {
    list-style: none;
    padding: 0;

    li {
        position: relative;
    }

    .list_block_word {
        cursor: pointer;
    }
}
.option-box {
    position: absolute;
    left: 92%;
    padding-left: 40px;
    display: none;
    width: 450px;
    z-index: 20;
}
.option-children {
    border-radius: 8px;
    padding: 8px 12px;
    box-shadow: 0px 0px 10px 0px rgba(222, 230, 240, 0.6);
    margin-bottom: 8px;
    display: inline-block;
    background-color: #fff;
    color: #475467;

    &:hover {
        .icon {
        }
    }
    .list_block_children {
        list-style: none;
        display: flex;
        border-radius: 4px;
        padding: 10px 10px 10px 0px;
        cursor: pointer;

        .list_block_children_icon {
            width: 40px;
            height: 40px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .list_block_children_word {
            > h4 {
                color: #1d2939;
                font-size: 14px;
                margin-bottom: 5px;
                font-weight: normal;
            }

            > h5 {
                color: #475467;
                font-size: 12px;
                font-weight: normal;
            }
        }

        &:hover {
            background-color: #eff8ff;

            .list_block_children_word {
                > h4 {
                    color: #1570ef;
                }
            }
        }
    }
}
.select_options_box {
    position: relative;
    margin-top: 5px;

    .select_options {
        background-color: $bg;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .select_option_item {
            padding: 8px 16px;
            cursor: pointer;

            &:hover {
                background-color: #f5f7fa;
            }
        }
    }
}
</style>

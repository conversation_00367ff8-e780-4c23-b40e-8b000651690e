<template>
    <div class="my_form_default my_form_default_bj">
        <table class="dl_table_style">
            <tbody>
                <tr v-for="(item, index) in filterFormConfig" :key="index">
                    <td class="common_label_title_r td_width0">{{ item.label }}：</td>
                    <td :colspan="item.colspan || 5">
                        <div v-if="item.type === 'display'" class="td_list">
                            <span>{{ item.value }}</span>
                            <p>
                                <a v-if="item.showConfig" class="pz" @click="onActionClick('config', item)">配置</a>
                                <a v-if="item.showPreview" class="yl" @click="onActionClick('preview', item)">预览</a>
                            </p>
                        </div>
                        <div v-else-if="item.type === 'radio'" class="radio-group">
                            <label v-for="(option, optIndex) in item.options" :key="optIndex" class="radio-label">
                                <input
                                    disabled
                                    type="radio"
                                    :name="item.name"
                                    :value="option.dictKey"
                                    :checked="option.dictKey === item.value"
                                    class="radio-input"
                                />
                                <span class="radio-text">{{ option.dictValue }}</span>
                            </label>
                        </div>
                        <div v-else-if="item.type === 'select'">
                            <el-select disabled v-model="item.value" :placeholder="item.placeholder || '请选择'">
                                <el-option
                                    v-for="option in item.options"
                                    :key="option.dictKey"
                                    :label="option.dictValue"
                                    :value="option.dictKey"
                                />
                            </el-select>
                        </div>
                        <div v-else-if="item.type === 'input'">
                            <el-input disabled v-model="item.value" :placeholder="'请输入'"></el-input>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script setup name="CjFormTable">
/**
 * 采集任务表单组件
 *
 * @description 显示采集任务的源表、目标表和汇聚方式等信息，支持配置式驱动
 */
import { ref, watch, computed } from 'vue';

const props = defineProps({
    /**
     * 表单配置项
     */
    config: {
        type: Array,
        default: () => [],
    },
    /**
     * 源数据表名称
     */
    sourceTable: {
        type: String,
        default: '未查到数据表',
    },
    /**
     * 目标数据表名称
     */
    targetTable: {
        type: String,
        default: '未查到数据表',
    },
    /**
     * 汇聚方式，默认为全量
     */
    collectType: {
        type: String,
        default: '1',
    },
});

const emit = defineEmits(['config', 'previewSource', 'previewTarget', 'change', 'action', 'update:config']);

/**
 * 根据props生成默认表单配置
 */
const defaultConfig = computed(() => [
    {
        id: 'sourceTable',
        type: 'display',
        label: '待采集表',
        value: props.sourceTable,
        showConfig: false,
        showPreview: false,
        colspan: 5,
    },
    {
        id: 'targetTable',
        type: 'display',
        label: '目标表',
        value: props.targetTable,
        showConfig: false,
        showPreview: false,
        colspan: 5,
    },
    {
        id: 'collectType',
        type: 'radio',
        label: '汇聚方式',
        name: 'collectType',
        value: props.collectType,
        options: [
            { dictValue: '全量', dictKey: 'full' },
            { dictValue: '增量', dictKey: 'increment' },
        ],
        colspan: 5,
    },
]);

/**
 * 实际使用的表单配置，优先使用传入的config，否则使用默认配置
 */
const formConfig = ref(props.config);
const filterFormConfig = computed(() => {
    let filterProps = props.config.filter(item => {
        return item.isShow;
    });
    // return filterProps;
    return filterProps.length > 0 ? filterProps : defaultConfig.value;
});

/**
 * 处理操作按钮点击事件
 * @param {string} action - 操作类型
 * @param {Object} item - 配置项
 */
const onActionClick = (action, item) => {
    // 统一触发action事件，方便外部处理
    emit('action', { action, item });
};
</script>

<style scoped lang="scss">
.my_form_default {
    background-color: #f4f9fe;

    .td_list {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: $bg;

        p {
            display: flex;
            gap: 10px;

            a {
                cursor: pointer;
                padding: 2px 8px;
                border-radius: 4px;
                text-decoration: none; /* 去除默认的下划线 */
                outline: none; /* 去除旧版浏览器的点击后的外虚线框 */
                &.pz {
                    color: $bg;
                    background-color: #67c23a;

                    &:hover {
                        background-color: #85ce61;
                    }
                }

                &.yl {
                    color: $bg;
                    background-color: #409eff;

                    &:hover {
                        background-color: #66b1ff;
                    }
                }
            }
        }
    }

    .radio-group {
        display: flex;
        align-items: center;

        .radio-label {
            display: flex;
            align-items: center;
            margin-right: 20px;
            cursor: pointer;

            .radio-input {
                position: relative;
                width: 16px;
                height: 16px;
                margin-right: 8px;
                appearance: none;
                border: 1px solid #dcdfe6;
                border-radius: 50%;
                transition: all 0.2s;

                &:checked {
                    border-color: #409eff;
                    background-color: $bg;

                    &::after {
                        content: '';
                        position: absolute;
                        width: 8px;
                        height: 8px;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        border-radius: 50%;
                        background-color: #409eff;
                    }
                }

                &:hover {
                    border-color: #409eff;
                }
            }

            .radio-text {
                font-size: 14px;
                color: #606266;
            }
        }
    }
}
</style>

<template>
    <div>
        <common-FileUploadList :autoUpload="false"></common-FileUploadList>
    </div>
</template>

<script setup name="intelligentQueryForm">
/**
 * 智能问数表单组件
 * 用于智能问数业务表单填写
 */
import { ref, reactive, watch } from 'vue';

/**
 * 组件属性
 */
const props = defineProps({
    /**
     * 表单数据对象
     */
    form: {
        type: Object,
        required: true,
    },
});

/**
 * 组件事件
 */
const emit = defineEmits(['getFormData']);

// 表单数据
const formData = reactive({
    indicatorName: props.form.indicatorName || '',
    queryType: props.form.queryType || '统计查询',
    dataSource: props.form.dataSource || '',
    timeRange: props.form.timeRange || '',
    regionRange: props.form.regionRange || '佛山市',
});

// 查询方式选项
const queryTypes = ref([
    { label: '统计查询', value: '统计查询' },
    { label: '明细查询', value: '明细查询' },
    { label: '对比查询', value: '对比查询' },
]);

// 数据源选项
const dataSources = ref([
    { label: '办件信息库', value: '办件信息库' },
    { label: '企业基本信息库', value: '企业基本信息库' },
    { label: '人口基本信息库', value: '人口基本信息库' },
]);

// 区域范围选项
const regionRanges = ref([
    { label: '佛山市', value: '佛山市' },
    { label: '禅城区', value: '禅城区' },
    { label: '南海区', value: '南海区' },
    { label: '顺德区', value: '顺德区' },
    { label: '高明区', value: '高明区' },
    { label: '三水区', value: '三水区' },
]);

/**
 * 监听表单数据变化
 */
watch(
    formData,
    () => {
        emit('getFormData', formData);
    },
    { deep: true }
);

/**
 * 单选框选项变更处理
 */
const onOptionChange = (field, value) => {
    formData[field] = value;
};

/**
 * 提交表单
 */
const submitForm = () => {
    emit('getFormData', formData);
};
</script>

<style scoped lang="scss">
.my_form_default {
    padding: 20px;

    .radio-group {
        display: flex;
        flex-wrap: wrap;

        .radio-label {
            margin-right: 20px;
            display: flex;
            align-items: center;
            cursor: pointer;

            .radio-input {
                margin-right: 5px;
            }
        }
    }

    .form_btn {
        margin-top: 20px;
        text-align: center;
    }
}
</style>

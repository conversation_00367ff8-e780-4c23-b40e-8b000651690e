<template>
    <div class="aggregation-excel-form">
        <el-form ref="formRef" :model="formData" :rules="formRules" label-width="80px" class="aggregation-form">
            <div class="upload-header">
                <span class="section-title">上传文件</span>
                <span class="upload-tip">文件格式不限, 最多只能上传1份文件</span>
            </div>

            <div class="form-container">
                <div class="upload-section">
                    <div class="upload-content">
                        <FileUploadList />
                    </div>
                </div>

                <div class="target-section">
                    <h3 class="section-title">目标汇聚</h3>
                    <div class="target-content">
                        <el-form-item label="数据源:" prop="dataSource">
                            <el-select
                                v-model="formData.dataSource"
                                placeholder="请选择数据源"
                                class="form-select"
                                clearable
                            >
                                <el-option label="源名" value="source1" />
                                <el-option label="数据源2" value="source2" />
                                <el-option label="数据源3" value="source3" />
                            </el-select>
                        </el-form-item>

                        <el-form-item label="数据库:" prop="database">
                            <el-select
                                v-model="formData.database"
                                placeholder="请选择数据库"
                                class="form-select"
                                clearable
                            >
                                <el-option label="库名" value="db1" />
                                <el-option label="数据库2" value="db2" />
                                <el-option label="数据库3" value="db3" />
                            </el-select>
                        </el-form-item>

                        <el-form-item label="数据表:" prop="table">
                            <el-select
                                v-model="formData.table"
                                placeholder="请选择数据表"
                                class="form-select"
                                clearable
                            >
                                <el-option label="表名" value="table1" />
                                <el-option label="数据表2" value="table2" />
                                <el-option label="数据表3" value="table3" />
                            </el-select>
                        </el-form-item>
                    </div>
                </div>
            </div>

            <!-- <div class="form-actions">
                <el-button type="primary" @click="handleSubmit">提交</el-button>
                <el-button @click="handleReset">重置</el-button>
            </div> -->
        </el-form>
    </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import FileUploadList from '@/components/dialogue/common/FileUploadList.vue';

defineOptions({
    name: 'AggregationExcelForm',
});

/**
 * @description 表单引用
 */
const formRef = ref();

/**
 * @description 表单数据
 */
const formData = reactive({
    dataSource: '',
    database: '',
    table: '',
});

/**
 * @description 表单验证规则
 */
const formRules = {
    dataSource: [{ required: true, message: '请选择数据源', trigger: 'change' }],
    database: [{ required: true, message: '请选择数据库', trigger: 'change' }],
    table: [{ required: true, message: '请选择数据表', trigger: 'change' }],
};

/**
 * @description 提交表单
 */
const handleSubmit = async () => {
    try {
        await formRef.value.validate();
        ElMessage.success('表单提交成功');
        console.log('表单数据:', formData);
        // 这里可以添加实际的提交逻辑
    } catch (error) {
        ElMessage.error('请检查表单填写是否缺少必填项');
        console.error('表单验证失败:', error);
    }
};
watch(
    formData,
    async () => {
        try {
            await formRef.value.validate();
            ElMessage.success('表单提交成功');
            console.log('表单数据:', formData);
            // 这里可以添加实际的提交逻辑
        } catch (error) {
            ElMessage.error('请检查表单填写是否缺少必填项');
            console.error('表单验证失败:', error);
        }
    },
    { deep: true }
);
/**
 * @description 重置表单
 */
const handleReset = () => {
    formRef.value.resetFields();
    ElMessage.info('表单已重置');
};
</script>

<style scoped lang="scss">
.aggregation-excel-form {
    width: 100%;

    .aggregation-form {
        .form-container {
            display: flex;
            gap: 24px;
            margin-bottom: 24px;
        }

        .upload-section,
        .target-section {
            flex: 1;
            font-size: 14px;
            border-radius: 8px;
            padding: 8px;
            background: #fff;
        }

        .upload-section {
            border: 1px solid #e9f2fd;
        }

        .section-title {
            font-weight: 500;
            color: #1d2939;
            margin-bottom: 16px;
        }

        .upload-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;

            .section-title {
                font-weight: 500;
                color: #1d2939;
                margin-bottom: 0;
            }

            .upload-tip {
                color: #98a2b3;
                font-size: 12px;
                margin-left: 8px;
                font-weight: normal;
            }
        }

        .target-content {
            .el-form-item {
                margin-bottom: 16px;

                .form-select {
                    width: 100%;

                    :deep(.el-input__wrapper) {
                        background-color: #f0f9ff;
                    }
                }
            }
        }

        .form-actions {
            display: flex;
            justify-content: center;
            gap: 12px;
            margin-top: 24px;
        }
    }
}

@media (max-width: 768px) {
    .aggregation-excel-form {
        .aggregation-form {
            .form-container {
                flex-direction: column;
            }
        }
    }
}
</style>

<template>
    <el-form ref="formRef" :model="formData" :rules="formRules" class="my_form_edit" label-width="100px">
        <div class="customer_write_form_box">
            <div class="customer_write_form_box_block">
                <div class="label-flex">待汇聚</div>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="数据源:" prop="sourceDatabase" required>
                            <el-select
                                v-model="formData.sourceDatabase"
                                @change="getSourceTableList"
                                placeholder="请选择"
                                style="width: 100%"
                                clearable
                            >
                                <el-option
                                    v-for="item in sourceDatabaseList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.name"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="数据表:" prop="sourceTable" required>
                            <el-select
                                v-model="formData.sourceTable"
                                @change="getColumnList()"
                                placeholder="请选择"
                                clearable
                            >
                                <el-option
                                    v-for="item in sourceTableList"
                                    :key="item.id"
                                    :label="item.comment"
                                    :value="item.comment"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                        <el-form-item label="汇聚方式:" prop="aggregationType" required>
                            <el-select
                                v-model="formData.aggregationType"
                                placeholder="请选择"
                                style="width: 100%"
                                clearable
                            >
                                <el-option label="全量" value="全量"></el-option>
                                <el-option label="增量" value="增量"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8" v-if="formData.aggregationType && formData.aggregationType === '增量'">
                        <el-form-item label="增量机制:" prop="incrementMechanism" required>
                            <el-select
                                v-model="formData.incrementMechanism"
                                placeholder="请选择"
                                style="width: 100%"
                                clearable
                            >
                                <el-option
                                    v-for="item in incrementTypeList"
                                    :key="item.id"
                                    :label="item.dictValue"
                                    :value="item.dictValue"
                                >
                                    {{ item.dictValue }}
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col
                        :span="8"
                        v-if="
                            formData.aggregationType &&
                            formData.aggregationType === '增量' &&
                            formData.incrementMechanism
                        "
                    >
                        <el-form-item label="源表标识:" prop="sourceIdentifier">
                            <el-select v-model="formData.sourceIdentifier" placeholder="请选择" clearable>
                                <el-option
                                    v-for="item in sourceIdentifierList"
                                    :key="item.id"
                                    :label="item.comment"
                                    :value="item.comment"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row
                    v-if="
                        formData.aggregationType &&
                        formData.aggregationType === '增量' &&
                        formData.incrementMechanism &&
                        formData.incrementMechanism === '按标识增量'
                    "
                >
                    <el-col :span="8">
                        <el-form-item label="待汇聚标识:" prop="pendingIdentifier">
                            <el-input v-model="formData.pendingIdentifier" placeholder="标识" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="已汇聚标识:" prop="aggregatedIdentifier">
                            <el-input v-model="formData.aggregatedIdentifier" placeholder="标识" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8"></el-col>
                </el-row>
            </div>
            <div class="customer_write_form_box_block">
                <div class="label-flex">目标汇聚</div>
                <el-row>
                    <el-col :span="8">
                        <el-form-item label="数据源:" prop="targetDatabase" required>
                            <el-select
                                v-model="formData.targetDatabase"
                                @change="getTargetTableList"
                                placeholder="请选择"
                                style="width: 100%"
                                clearable
                            >
                                <el-option
                                    v-for="item in targetDatabaseList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.name"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="数据表:" prop="targetTable" required>
                            <el-select
                                v-model="formData.targetTable"
                                placeholder="请选择"
                                style="width: 100%"
                                clearable
                            >
                                <el-option
                                    v-for="item in targetTableList"
                                    :key="item.id"
                                    :label="item.comment"
                                    :value="item.comment"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </div>
        </div>
    </el-form>
</template>

<script setup name="aggregationTableForm">
/**
 * 数据表汇聚表单组件
 * 用于配置数据表的汇聚信息
 */
import { ref, watch, onMounted, onUnmounted, useTemplateRef } from 'vue';
import { ElMessage } from 'element-plus';
import { selectDictByDB as selectDataBaseDict, getTables, getColumns } from '@/api/dialogue/data-cpn-util-api';
import { getDictionary } from '@/api/system/dict.js';
import { isDisabled, form } from '@/components/dialogue/store/input.js';

/**
 * 组件事件
 */
const emit = defineEmits(['getFormData']);

const formRef = useTemplateRef('formRef');

/**
 * 表单数据的本地副本
 * @type {<Object>}
 */
const formData = ref({
    // 源数据
    sourceDataSource: form.sourceDataSource || '',
    sourceDatabase: form.sourceDatabase || '',
    sourceTable: form.sourceTable || '',
    // 汇聚配置
    aggregationType: form.aggregationType || '全量',
    incrementMechanism: form.incrementMechanism || '',
    sourceIdentifier: form.sourceIdentifier || '',
    pendingIdentifier: form.pendingIdentifier || '',
    aggregatedIdentifier: form.aggregatedIdentifier || '',
    // 目标数据
    targetDataSource: form.targetDataSource || '',
    targetDatabase: form.targetDatabase || '',
    targetTable: form.targetTable || '',
});

/**
 * 表单验证规则
 */
const formRules = {
    sourceDatabase: [{ required: true, message: '请选择源数据源', trigger: 'change' }],
    sourceTable: [{ required: true, message: '请选择源数据表', trigger: 'change' }],
    aggregationType: [{ required: true, message: '请选择汇聚方式', trigger: 'change' }],
    incrementMechanism: [
        {
            required: true,
            message: '请选择增量机制',
            trigger: 'change',
            validator: (rule, value, callback) => {
                if (formData.value.aggregationType === '增量' && !value) {
                    callback(new Error('请选择增量机制'));
                } else {
                    callback();
                }
            },
        },
    ],
    targetDatabase: [{ required: true, message: '请选择目标数据源', trigger: 'change' }],
    targetTable: [{ required: true, message: '请选择目标数据表', trigger: 'change' }],
    pendingIdentifier: [
        {
            required: true,
            message: '请输入待汇聚标识',
            trigger: 'blur',
            validator: (rule, value, callback) => {
                if (
                    formData.value.aggregationType === '增量' &&
                    formData.value.incrementMechanism === '按标识增量' &&
                    !value
                ) {
                    callback(new Error('请输入待汇聚标识'));
                } else {
                    callback();
                }
            },
        },
    ],
    aggregatedIdentifier: [
        {
            required: true,
            message: '请输入已汇聚标识',
            trigger: 'blur',
            validator: (rule, value, callback) => {
                if (
                    formData.value.aggregationType === '增量' &&
                    formData.value.incrementMechanism === '按标识增量' &&
                    !value
                ) {
                    callback(new Error('请输入已汇聚标识'));
                } else {
                    callback();
                }
            },
        },
    ],
};

const sourceDatabaseList = ref([]);
const targetDatabaseList = ref([]);
const sourceTableList = ref([]);
const targetTableList = ref([]);
const sourceIdentifierList = ref([]);
const incrementTypeList = ref([]);

/**
 * @description 统一监听表单相关字段变化，优化依赖收集与重置逻辑
 */
watch(
    formData,
    (newVal, oldVal) => {
        // 监听汇聚方式变化，重置相关字段
        if (oldVal.aggregationType !== newVal.aggregationType) {
            if (newVal.aggregationType !== '增量') {
                formData.value.incrementMechanism = '';
                formData.value.sourceIdentifier = '';
                formData.value.pendingIdentifier = '';
                formData.value.aggregatedIdentifier = '';
            }
        }
        // 监听增量机制变化，重置相关字段
        if (oldVal.incrementMechanism !== newVal.incrementMechanism) {
            if (newVal.incrementMechanism !== '按标识增量') {
                formData.value.pendingIdentifier = '';
                formData.value.aggregatedIdentifier = '';
            }
        }
        getFormData();

        handleSubmit();
    },
    { deep: true }
);

/**
 * 在组件挂载完成后触发componentMounted事件
 */
onMounted(() => {
    getDictList();
    handleSubmit();
});
onUnmounted(() => {
    isDisabled.value = false;
});

/**
 * 提交表单
 */
async function handleSubmit() {
    try {
        await formRef.value.validate();
        // ElMessage.success('表单提交成功');
        // 添加提交逻辑
        isDisabled.value = false;
    } catch (error) {
        // ElMessage.error('请检查表单填写是否缺少必填项');
        isDisabled.value = true;
    }
}

/**
 * 向父组件发送表单数据
 * 转换为父组件期望的格式
 */
const getFormData = () => {
    // 确保所有字段都有值，避免undefined
    const convertedData = {
        sourceDataSourceName: formData.value.sourceDataSourceName || '',
        sourceDataSourceId: formData.value.sourceDataSourceId || '',
        sourceDatabase: formData.value.sourceDatabase || '',
        sourceTable: formData.value.sourceTable || '',
        aggregationType: formData.value.aggregationType || '全量',
        incrementMechanism: formData.value.incrementMechanism || '',
        sourceIdentifier: formData.value.sourceIdentifier || '',
        pendingIdentifier: formData.value.pendingIdentifier || '',
        aggregatedIdentifier: formData.value.aggregatedIdentifier || '',
        targetDataSourceName: formData.value.targetDataSourceName || '',
        targetDataSourceId: formData.value.targetDataSourceId || '',
        targetDatabase: formData.value.targetDatabase || '',
        targetTable: formData.value.targetTable || '',
    };

    // 立即触发事件，通知父组件更新
    emit('getFormData', convertedData);

    return convertedData;
};

// 定义方法
async function getDictList() {
    //入口数据库
    selectDataBaseDict({ dbType: '1', state: 1 }).then(res => {
        let data = res.data.data;
        sourceDatabaseList.value = data;
    });
    //数仓数据库
    selectDataBaseDict({ dbType: '2', state: 1 }).then(res => {
        let data = res.data.data;
        targetDatabaseList.value = data;
    });
    incrementTypeList.value = await getDictionary({ code: 'incremental_mechanism' }).then(res => {
        let data = res.data.data;
        return data;
    });
}

// 获取源表列表
function getSourceTableList() {
    const sourceDatabase = sourceDatabaseList.value.find(option => option.name === formData.value.sourceDatabase);
    getTables(sourceDatabase.id).then(res => {
        const result = res.data;
        if (result.success) {
            sourceTableList.value = [];
            result.data.forEach(val => {
                sourceTableList.value.push({
                    name: val.name,
                    comment: val.comment,
                });
            });
        }
    });
}

// 获取目标表列表
function getTargetTableList() {
    const targetDatabase = targetDatabaseList.value.find(option => option.name === formData.value.targetDatabase);
    getTables(targetDatabase.id).then(res => {
        const result = res.data;
        if (result.success) {
            targetTableList.value = [];
            result.data.forEach(val => {
                targetTableList.value.push({
                    name: val.name,
                    comment: val.comment,
                });
            });
        }
    });
}

// 获取源表列表
function getColumnList() {
    console.log('getColumnList');
    const sourceDatabase = sourceDatabaseList.value.find(option => option.name === formData.value.sourceDatabase);
    const sourceTable = sourceTableList.value.find(option => option.comment === formData.value.sourceTable);
    console.log(sourceDatabase);
    console.log(sourceTable);
    getColumns(sourceDatabase.id, sourceTable.name).then(res => {
        const result = res.data;
        if (result.success) {
            result.data.forEach(val => {
                sourceIdentifierList.value.push({
                    name: val.name,
                    comment: val.comment,
                });
            });
        }
    });
}
</script>
<style scoped lang="scss">
.my_form_edit {
    font-size: 14px;
    .label-flex {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        margin-bottom: 10px;
    }

    .form-actions {
        display: flex;
        justify-content: center;
        gap: 12px;
        margin-top: 24px;
    }
}
</style>

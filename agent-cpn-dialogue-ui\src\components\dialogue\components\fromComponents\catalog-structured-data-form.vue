<template>
    <el-skeleton :loading="loading" animated>
        <template #template>
            <!-- 这里可以自定义骨架屏内容 -->
            <el-skeleton :rows="9" animated />
        </template>
        <template #default>
            <el-form :model="formData" :rules="formRules" class="my_form_edit" label-width="100px" ref="formRef">
                <div class="customer_write_form_box">
                    <div class="customer_write_form_box_block">
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="资源标题:" prop="resourceTitle" required>
                                    <el-input
                                        v-model="formData.resourceTitle"
                                        placeholder="请输入资源标题"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="更新周期:" prop="updateFrequency" required>
                                    <el-select
                                        v-model="formData.updateFrequency"
                                        placeholder="请选择"
                                        style="width: 100%"
                                        clearable
                                    >
                                        <el-option
                                            :value="item.dictValue"
                                            :label="item.dictValue"
                                            v-for="(item, index) in dictData.updateFrequency"
                                            :key="index"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="属性分类:" prop="resourceCategory" required>
                                    <el-select v-model="formData.resourceCategory" placeholder="请选择" clearable>
                                        <el-option
                                            :value="item.dictValue"
                                            :label="item.dictValue"
                                            v-for="(item, index) in dictData.resourceCategory"
                                            :key="index"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="基础分类:">
                                    <el-tree-select
                                        v-model="formData.baseCategory"
                                        :data="dictData.baseCategory"
                                        :render-after-expand="false"
                                        clearable
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="主题分类:">
                                    <el-tree-select
                                        v-model="formData.topicCategory"
                                        :data="dictData.topicCategory"
                                        :render-after-expand="false"
                                        clearable
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="部门:" prop="department" required>
                                    <el-tree-select
                                        v-model="formData.department"
                                        :data="dictData.department"
                                        :render-after-expand="false"
                                        clearable
                                    />
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="开放类型:" prop="openType" required>
                                    <el-select
                                        v-model="formData.openType"
                                        placeholder="请选择"
                                        style="width: 100%"
                                        clearable
                                    >
                                        <el-option
                                            :value="item.dictValue"
                                            :label="item.dictValue"
                                            v-for="(item, index) in dictData.openType"
                                            :key="index"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="共享类型:" prop="shareType" required>
                                    <el-select
                                        v-model="formData.shareType"
                                        placeholder="请选择"
                                        style="width: 100%"
                                        clearable
                                    >
                                        <el-option
                                            :value="item.dictValue"
                                            :label="item.dictValue"
                                            v-for="(item, index) in dictData.shareType"
                                            :key="index"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" v-if="formData.shareType === '不共享'">
                                <el-form-item label="不共享理由:" prop="shareReason">
                                    <el-input
                                        v-model="formData.shareReason"
                                        placeholder="请输入不共享理由"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" v-if="formData.shareType === '有条件共享'">
                                <el-form-item label="共享条件:" prop="shareCondition" required>
                                    <el-input
                                        v-model="formData.shareCondition"
                                        placeholder="请输入共享条件"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" v-if="formData.shareType === '有条件共享'">
                                <el-form-item label="共享依据:" prop="shareBasis" required>
                                    <el-input
                                        v-model="formData.shareBasis"
                                        placeholder="请输入共享依据"
                                        clearable
                                    ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" v-if="formData.shareType !== '不共享'">
                                <el-form-item label="共享方式:">
                                    <el-select
                                        v-model="formData.shareMethod"
                                        placeholder="请选择"
                                        style="width: 100%"
                                        clearable
                                    >
                                        <el-option
                                            :value="item.dictValue"
                                            :label="item.dictValue"
                                            v-for="(item, index) in dictData.shareMethod"
                                            :key="index"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8" v-if="formData.shareType !== '不共享'">
                                <el-form-item label="共享范围:">
                                    <el-select
                                        v-model="formData.shareScope"
                                        placeholder="请选择"
                                        style="width: 100%"
                                        clearable
                                    >
                                        <el-option
                                            :value="item.dictValue"
                                            :label="item.dictValue"
                                            v-for="(item, index) in dictData.shareScope"
                                            :key="index"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </el-form>
        </template>
    </el-skeleton>
</template>

<script setup>
defineOptions({
    name: 'catalogStructuredDataForm',
});

/**
 * 结构化数据目录表单组件
 * 用于配置结构化数据的资源目录信息
 */
import { onMounted, reactive, ref, useTemplateRef, watch, nextTick } from 'vue';
import { getDictionary } from '@/api/system/dict';
import { selectClassification } from '@/api/dialogue/data-catalog-util-api';
import { getDeptTree } from '@/api/system/dept';
import { isDisabled } from '@/components/dialogue/store/input.js';

const loading = ref(true); // 新增loading状态
const formRef = useTemplateRef('formRef');
/**
 * @description 初始化加载字典及分类等数据，并发逻辑
 */
onMounted(async () => {
    loading.value = true;
    try {
        const requestMap = [
            { req: getDictionary({ code: 'update_cycle' }), key: 'updateFrequency' },
            { req: getDictionary({ code: 'res_type' }), key: 'resourceCategory' },
            { req: selectClassification({ type: '4' }), key: 'baseCategory', isTree: true },
            { req: selectClassification({ type: '1' }), key: 'topicCategory', isTree: true },
            { req: getDeptTree(), key: 'department', isTree: true },
            { req: getDictionary({ code: 'open_type' }), key: 'openType' },
            { req: getDictionary({ code: 'share_type' }), key: 'shareType' },
            { req: getDictionary({ code: 'share_way' }), key: 'shareMethod' },
            { req: getDictionary({ code: 'share_scope' }), key: 'shareScope' },
        ];
        const results = await Promise.allSettled(requestMap.map(item => item.req));
        results.forEach((result, idx) => {
            if (result.status === 'fulfilled') {
                const key = requestMap[idx].key;
                const isTree = requestMap[idx].isTree;
                const data = result.value?.data?.data || [];
                dictData[key] = isTree ? formatTreeData(data) : data;
            }
        });

        // 检查是否有请求失败，提示用户
        if (results.some(r => r.status === 'rejected')) {
            // console.error();
        }
    } catch (e) {
        console.error('数据加载失败，请重试', e);
    } finally {
        loading.value = false;
    }
    await nextTick();
    handleSubmit();
});
onUnmounted(() => {
    isDisabled.value = false;
});
/**
 * 遍历格式化树形结构内容，且保持树形结构
 */
function formatTreeData(data) {
    data.forEach(item => {
        item.label = item.title;
        item.value = item.title;
        if (item.children) {
            formatTreeData(item.children);
        }
    });
    return data;
}

const dictData = reactive({
    updateFrequency: [],
    resourceCategory: [],
    baseCategory: [],
    topicCategory: [],
    department: [],
    openType: [],
    shareType: [],
    shareMethod: [],
    shareScope: [],
});
/**
 * 表单属性
 */
const props = defineProps({
    /**
     * 表单数据对象
     */
    form: {
        type: Object,
        required: true,
    },
});

/**
 * 组件事件
 */
const emit = defineEmits(['getFormData']);

/**
 * 表单数据的本地副本
 * @type {<Object>}
 */
const formData = ref({ ...props.form });

/**
 * 表单验证规则
 */
const formRules = reactive({
    resourceTitle: [
        { required: true, message: '请输入资源标题', trigger: 'blur' },
        { min: 1, max: 100, message: '资源标题长度在1到100个字符', trigger: 'blur' },
    ],
    updateFrequency: [{ required: true, message: '请选择更新周期', trigger: 'change' }],
    resourceCategory: [{ required: true, message: '请选择属性分类', trigger: 'change' }],
    department: [{ required: true, message: '请选择部门', trigger: 'change' }],
    openType: [{ required: true, message: '请选择开放类型', trigger: 'change' }],
    shareType: [{ required: true, message: '请选择共享类型', trigger: 'change' }],
    shareCondition: [
        {
            validator: (rule, value, callback) => {
                if (formData.value.shareType === '有条件共享') {
                    if (!value || value.trim() === '') {
                        callback(new Error('请输入共享条件'));
                    } else if (value.length > 200) {
                        callback(new Error('共享条件不能超过200个字符'));
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            },
            trigger: 'blur',
        },
    ],
    shareBasis: [
        {
            validator: (rule, value, callback) => {
                if (formData.value.shareType === '有条件共享') {
                    if (!value || value.trim() === '') {
                        callback(new Error('请输入共享依据'));
                    } else if (value.length > 200) {
                        callback(new Error('共享依据不能超过200个字符'));
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            },
            trigger: 'blur',
        },
    ],
    shareReason: [
        {
            validator: (rule, value, callback) => {
                if (formData.value.shareType === '不共享') {
                    if (!value || value.trim() === '') {
                        callback(new Error('请输入不共享理由'));
                    } else if (value.length > 200) {
                        callback(new Error('不共享理由不能超过200个字符'));
                    } else {
                        callback();
                    }
                } else {
                    callback();
                }
            },
            trigger: 'blur',
        },
    ],
});

/**
 * 监听表单数据变化，向父组件传递更新后的数据
 */
watch(
    formData,
    () => {
        getFormData();
        handleSubmit();
    },
    { deep: true }
);
/**
 * 提交表单
 */
async function handleSubmit() {
    try {
        await formRef.value.validate();
        // ElMessage.success('表单提交成功');
        // 添加提交逻辑
        isDisabled.value = false;
    } catch (error) {
        // ElMessage.error('请检查表单填写是否缺少必填项');
        isDisabled.value = true;
    }
}
/**
 * 专门监听共享类型变化
 */
watch(
    () => formData.value.shareType,
    newValue => {
        if (newValue === '不共享') {
            // 如果选择"不共享"，清空共享方式和共享范围，保留不共享理由
            formData.value.shareMethod = '';
            formData.value.shareScope = '';
            formData.value.shareBasis = '';
            // 清除相关字段的验证错误
            nextTick(() => {
                formRef.value?.clearValidate(['shareCondition', 'shareBasis']);
            });
        } else if (newValue === '无条件共享') {
            // 如果选择"无条件共享"，清空共享条件和共享依据
            formData.value.shareCondition = '';
            formData.value.shareBasis = '';
            // 清除相关字段的验证错误
            nextTick(() => {
                formRef.value?.clearValidate(['shareCondition', 'shareBasis', 'shareReason']);
            });
        } else if (newValue !== '有条件共享') {
            // 如果选择其他共享类型（非有条件共享），清空共享条件和共享依据
            formData.value.shareCondition = '';
            formData.value.shareBasis = '';
            // 清除相关字段的验证错误
            nextTick(() => {
                formRef.value?.clearValidate(['shareCondition', 'shareBasis', 'shareReason']);
            });
        }
        // 立即触发表单数据更新
        getFormData();
    }
);

/**
 * 向父组件发送表单数据
 */
const getFormData = () => {
    // 确保所有字段都有默认值，避免undefined
    const data = {
        resourceTitle: formData.value.resourceTitle || '',
        updateFrequency: formData.value.updateFrequency || '',
        resourceCategory: formData.value.resourceCategory || '',
        baseCategory: formData.value.baseCategory || '',
        topicCategory: formData.value.topicCategory || '',
        department: formData.value.department || '',
        openType: formData.value.openType || '',
        shareType: formData.value.shareType || '',
        shareCondition: formData.value.shareCondition || '',
        shareReason: formData.value.shareReason || '',
        shareBasis: formData.value.shareBasis || '',
        shareMethod: formData.value.shareMethod || '',
        shareScope: formData.value.shareScope || '',
    };

    // 立即触发事件，通知父组件更新
    emit('getFormData', data);

    return data;
};
</script>

<template>
    <div class="catalog-unstructured-data-form">
        <!-- 文件上传区域 -->
        <div class="upload-section">
            <div class="upload-header">
                <span class="section-title label-flex">非结构化数据文件</span>
                <span class="upload-tip">文件格式不限, 最多只能上传1份文件</span>
            </div>
        </div>
        <!-- 表单区域 -->
        <div class="form-section">
            <div class="upload-section">
                <div class="upload-content">
                    <common-file-upload-list />
                </div>
            </div>
            <el-form ref="formRef" :model="formData" :rules="formRules" label-width="auto">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="资源标题" prop="resourceTitle" required>
                            <el-input v-model="formData.resourceTitle" placeholder="请输入资源标题" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="更新周期" prop="updateFrequency" required>
                            <el-select v-model="formData.updateFrequency" placeholder="周期" style="width: 100%">
                                <el-option label="每日" value="每日" />
                                <el-option label="每周" value="每周" />
                                <el-option label="每月" value="每月" />
                                <el-option label="每季度" value="每季度" />
                                <el-option label="每年" value="每年" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="属性分类" prop="resourceCategory" required>
                            <el-select v-model="formData.resourceCategory" placeholder="类别名" style="width: 100%">
                                <el-option label="部门信息资源" value="部门信息资源" />
                                <el-option label="政策文件" value="政策文件" />
                                <el-option label="技术文档" value="技术文档" />
                                <el-option label="业务数据" value="业务数据" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="基础分类" prop="baseCategory">
                            <el-select v-model="formData.baseCategory" placeholder="类别名" style="width: 100%">
                                <el-option label="社会信用" value="社会信用" />
                                <el-option label="政府文件" value="政府文件" />
                                <el-option label="企业文件" value="企业文件" />
                                <el-option label="个人文件" value="个人文件" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="主题分类" prop="topicCategory">
                            <el-select v-model="formData.topicCategory" placeholder="类别名" style="width: 100%">
                                <el-option label="综合政务" value="综合政务" />
                                <el-option label="经济发展" value="经济发展" />
                                <el-option label="社会民生" value="社会民生" />
                                <el-option label="科技创新" value="科技创新" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="部门" prop="department" required>
                            <el-select v-model="formData.department" placeholder="部门名" style="width: 100%">
                                <el-option label="大数据局" value="大数据局" />
                                <el-option label="发改委" value="发改委" />
                                <el-option label="科技局" value="科技局" />
                                <el-option label="教育局" value="教育局" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="开放类型" prop="openType" required>
                            <el-select v-model="formData.openType" placeholder="类型名" style="width: 100%">
                                <el-option label="不开放" value="不开放" />
                                <el-option label="公开" value="公开" />
                                <el-option label="内部" value="内部" />
                                <el-option label="保密" value="保密" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="共享类型" prop="shareType" required>
                            <el-select v-model="formData.shareType" placeholder="类型名" style="width: 100%">
                                <el-option label="不共享" value="不共享" />
                                <el-option label="完全共享" value="完全共享" />
                                <el-option label="部分共享" value="部分共享" />
                                <el-option label="有条件共享" value="有条件共享" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>

        <!-- 隐藏的文件输入框 -->
        <input ref="fileInputRef" type="file" multiple style="display: none" @change="handleFileChange" />
    </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { Document, Close } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useDialogInput } from '@/components/dialogue/hooks/useDialogInput';

defineOptions({
    name: 'CatalogUnstructuredDataForm',
});

// 获取对话框输入相关的 store
const dialogInputStore = useDialogInput();

// 文件列表
const fileList = ref([]);
const fileInputRef = ref();

// 表单数据
const formData = reactive({
    resourceTitle: '企业基本信息目录对话生成',
    updateFrequency: '每日',
    resourceCategory: '部门信息资源',
    baseCategory: '社会信用',
    topicCategory: '综合政务',
    department: '大数据局',
    openType: '不开放',
    shareType: '不共享',
});

// 表单验证规则
const formRules = {
    resourceTitle: [{ required: true, message: '请输入资源标题', trigger: 'blur' }],
    updateFrequency: [{ required: true, message: '请选择更新周期', trigger: 'change' }],
    resourceCategory: [{ required: true, message: '请选择属性分类', trigger: 'change' }],
    department: [{ required: true, message: '请选择部门', trigger: 'change' }],
    openType: [{ required: true, message: '请选择开放类型', trigger: 'change' }],
    shareType: [{ required: true, message: '请选择共享类型', trigger: 'change' }],
};

const formRef = ref();

/**
 * @description 组件挂载时初始化对话框内容
 */
onMounted(() => {
    // 将表单数据同步到全局 store
    dialogInputStore.updateFormData(formData);
    // 更新对话框内容
    dialogInputStore.updateDialogContent();
});

/**
 * @description 监听表单数据变化，更新对话框内容
 */
watch(
    () => formData,
    newFormData => {
        // 将表单数据同步到全局 store
        dialogInputStore.updateFormData(newFormData);
        // 更新对话框内容
        dialogInputStore.updateDialogContent();
    },
    { deep: true }
);

/**
 * @description 处理文件选择变化
 * @param {Event} event - 文件选择事件
 */
const handleFileChange = event => {
    const files = Array.from(event.target.files);

    if (fileList.value.length + files.length > 5) {
        ElMessage.warning('最多只能上传5份文件');
        return;
    }

    files.forEach(file => {
        fileList.value.push({
            name: file.name,
            size: file.size,
            file: file,
        });
    });

    // 清空input值，允许重复选择相同文件
    event.target.value = '';
};

/**
 * @description 移除文件
 * @param {number} index - 文件索引
 */
const removeFile = index => {
    fileList.value.splice(index, 1);
};

/**
 * @description 格式化文件大小
 * @param {number} bytes - 文件字节数
 * @returns {string} 格式化后的文件大小
 */
const formatFileSize = bytes => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * @description 获取表单数据
 * @returns {Object} 表单数据和文件列表
 */
const getFormData = () => {
    return {
        formData: { ...formData },
        files: fileList.value.map(item => item.file),
    };
};

/**
 * @description 验证表单
 * @returns {Promise<boolean>} 验证结果
 */
const validateForm = async () => {
    try {
        await formRef.value.validate();
        return true;
    } catch (error) {
        return false;
    }
};

/**
 * @description 重置表单
 */
const resetForm = () => {
    formRef.value.resetFields();
    fileList.value = [];
};

// 暴露方法给父组件
defineExpose({
    getFormData,
    validateForm,
    resetForm,
});
</script>

<style scoped lang="scss">
.catalog-unstructured-data-form {
    font-size: 14px;

    .upload-section {
        margin-bottom: 24px;

        .upload-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            .section-title {
                font-weight: 500;
                color: #1d2939;
                margin-bottom: 0;
            }
            .upload-tip {
                color: #98a2b3;
                font-size: 12px;
                margin-left: 8px;
                font-weight: normal;
            }
        }
    }

    .form-section {
        width: 100%;
        .el-form-item {
            // margin-bottom: 20px;
        }
    }
    .label-flex {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        line-height: 24px;
        text-align: left;
        font-style: normal;
        margin-bottom: 10px;
    }
}
</style>

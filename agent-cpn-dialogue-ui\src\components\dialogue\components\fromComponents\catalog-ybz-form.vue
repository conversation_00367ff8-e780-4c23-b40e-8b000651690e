<script setup>
/**
 * @description 数据一本帐文件上传与资源标题输入表单
 */
defineOptions({
    name: 'CatalogYbzForm',
});
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
    form: {
        type: Object,
        required: true,
    },
});
const emit = defineEmits(['getFormData']);

const formData = ref({ ...props.form });

// 资源标题输入同步
watch(
    () => formData.value.resourceTitle,
    val => {
        emit('getFormData', { resourceTitle: val });
    }
);
</script>

<template>
    <el-form :model="formData" :inline="true" class="form-container">
        <el-row align="top" gutter="24">
            <el-col :span="12">
                <div required class="item-align">
                    <div class="upload-header">
                        <span class="section-title">上传文件</span>
                        <span class="upload-tip">文件格式不限, 最多只能上传1份文件</span>
                    </div>
                    <div class="file-upload-row mt-2 ml-2">
                        <common-FileUploadList />
                    </div>
                </div>
            </el-col>
            <el-col :span="12">
                <el-form-item required>
                    <template #label>
                        <div class="label-flex">
                            <span>资源标题：</span>
                        </div>
                    </template>
                    <el-input v-model="formData.resourceTitle" placeholder="请输入资源标题" class="input-align" />
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>
</template>

<style scoped lang="scss">
.form-container {
    width: 100%;
}
.item-align {
    align-items: flex-start;
    margin-bottom: 0;
}
.label-flex {
    display: flex;
    align-items: center;
    gap: 4px;
}
.required {
    color: #f56c6c;
    font-size: 16px;
    margin-right: 2px;
}
.desc {
    color: #999;
    font-size: 12px;
    margin-left: 8px;
    font-weight: normal;
}
.file-upload-row {
    display: flex;
    align-items: center;
    min-height: 40px;
}
.input-align {
    height: 40px;
    line-height: 40px;
    min-width: 260px;
}
.upload-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    .section-title {
        font-weight: 500;
        color: #1d2939;
        margin-bottom: 0;
    }
    .upload-tip {
        color: #98a2b3;
        font-size: 12px;
        margin-left: 8px;
        font-weight: normal;
    }
}
</style>

<template>
    <div class="my_form_default my_form_default_bj">
        <table class="dl_table_style">
            <tbody>
                <tr>
                    <td class="common_label_title_r td_width0">指标名称：</td>
                    <td colspan="5">
                        <div class="td_list">
                            <el-input v-model="formData.indicatorName" placeholder="请输入指标名称" />
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="common_label_title_r td_width0">查询方式：</td>
                    <td colspan="5">
                        <div class="radio-group">
                            <label v-for="(option, index) in queryTypes" :key="index" class="radio-label">
                                <input
                                    type="radio"
                                    name="queryType"
                                    :value="option.value"
                                    :checked="option.value === formData.queryType"
                                    @change="onOptionChange('queryType', option.value)"
                                    class="radio-input"
                                />
                                <span class="radio-text">{{ option.label }}</span>
                            </label>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="common_label_title_r td_width0">涉及数据：</td>
                    <td colspan="5">
                        <div class="td_list">
                            <el-select v-model="formData.dataSource" placeholder="请选择数据源">
                                <el-option
                                    v-for="(option, index) in dataSources"
                                    :key="index"
                                    :label="option.label"
                                    :value="option.value"
                                />
                            </el-select>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="common_label_title_r td_width0">时间范围：</td>
                    <td colspan="5">
                        <div class="td_list">
                            <el-date-picker
                                v-model="formData.timeRange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                format="YYYY-MM-DD"
                                value-format="YYYY-MM-DD"
                            />
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="common_label_title_r td_width0">区域范围：</td>
                    <td colspan="5">
                        <div class="td_list">
                            <el-select v-model="formData.regionRange" placeholder="请选择区域范围">
                                <el-option
                                    v-for="(option, index) in regionRanges"
                                    :key="index"
                                    :label="option.label"
                                    :value="option.value"
                                />
                            </el-select>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="form_btn">
            <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
    </div>
</template>

<script setup name="intelligentQueryForm">
/**
 * 智能问数表单组件
 * 用于智能问数业务表单填写
 */
import { ref, reactive, watch } from 'vue';

/**
 * 组件属性
 */
const props = defineProps({
    /**
     * 表单数据对象
     */
    form: {
        type: Object,
        required: true,
    },
});

/**
 * 组件事件
 */
const emit = defineEmits(['getFormData']);

// 表单数据
const formData = reactive({
    indicatorName: props.form.indicatorName || '',
    queryType: props.form.queryType || '统计查询',
    dataSource: props.form.dataSource || '',
    timeRange: props.form.timeRange || '',
    regionRange: props.form.regionRange || '佛山市',
});

// 查询方式选项
const queryTypes = ref([
    { label: '统计查询', value: '统计查询' },
    { label: '明细查询', value: '明细查询' },
    { label: '对比查询', value: '对比查询' },
]);

// 数据源选项
const dataSources = ref([
    { label: '办件信息库', value: '办件信息库' },
    { label: '企业基本信息库', value: '企业基本信息库' },
    { label: '人口基本信息库', value: '人口基本信息库' },
]);

// 区域范围选项
const regionRanges = ref([
    { label: '佛山市', value: '佛山市' },
    { label: '禅城区', value: '禅城区' },
    { label: '南海区', value: '南海区' },
    { label: '顺德区', value: '顺德区' },
    { label: '高明区', value: '高明区' },
    { label: '三水区', value: '三水区' },
]);

/**
 * 监听表单数据变化
 */
watch(
    formData,
    () => {
        emit('getFormData', formData);
    },
    { deep: true }
);

/**
 * 单选框选项变更处理
 */
const onOptionChange = (field, value) => {
    formData[field] = value;
};

/**
 * 提交表单
 */
const submitForm = () => {
    emit('getFormData', formData);
};
</script>

<style scoped lang="scss">
.my_form_default {
    padding: 20px;

    .radio-group {
        display: flex;
        flex-wrap: wrap;

        .radio-label {
            margin-right: 20px;
            display: flex;
            align-items: center;
            cursor: pointer;

            .radio-input {
                margin-right: 5px;
            }
        }
    }

    .form_btn {
        margin-top: 20px;
        text-align: center;
    }
}
</style>

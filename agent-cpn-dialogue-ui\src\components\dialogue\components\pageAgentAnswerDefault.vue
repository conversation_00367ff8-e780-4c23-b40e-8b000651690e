<template>
    <div class="ask">
        <div class="ask_icon">
            <img :src="publicPath + `/static/dialogue/ask_icon.png`" />
        </div>
        <div class="ask_content">
            <div>
                <div class="ask_title">
                    <p>欢迎使用智能助手～</p>
                    <small>
                        我是您的人工智能助手，很高兴为您服务。在这里，您可以问我任何问题，我会尽我所能帮助您。
                    </small>
                </div>
                <!--                <div class="ask_block">-->
                <!--                    <h2>-->
                <!--                        <span>你可以试着生成任务：</span>-->
                <!--                        <a>-->
                <!--                            <img :src="`/static/dialogue/sx.png`" @click="refreshHandle" />-->
                <!--                            换一换-->
                <!--                        </a>-->
                <!--                    </h2>-->
                <!--                    <div class="default_ask_block">-->
                <!--                        <div class="default_ask_list" v-for="(item, index) in defaultAskList" :key="index">-->
                <!--                            <h4>-->
                <!--                                <img :src="`/static/dialogue/default_icon.png`" />-->
                <!--                                <span>{{ item.title }}</span>-->
                <!--                                <a>去生成 ></a>-->
                <!--                            </h4>-->
                <!--                            <p>{{ item.description }}</p>-->
                <!--                        </div>-->
                <!--                    </div>-->
                <!--                </div>-->
            </div>
        </div>
    </div>
</template>

<script setup name="pageAgentAnswerDefault">
/**
 * 默认回答页面组件
 * 显示欢迎信息和示例任务
 */
import { ref } from 'vue';
import { publicPath } from '@/components/dialogue/store/main.js';

/**
 * 默认任务列表
 */
const defaultAskList = ref([
    {
        title: '01.数据采集',
        description: '采集数据，查看采集情况',
    },
    {
        title: '02.API生成',
        description: '生成接口，调用接口测试',
    },
]);

/**
 * 刷新任务列表
 */
const refreshHandle = () => {
    console.log('换一批');
};
</script>

<style lang="scss" scoped></style>

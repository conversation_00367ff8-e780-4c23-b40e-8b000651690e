<template>
    <div class="edit-task-container">
        <el-form :model="formDataRef" label-width="100px" class="task-form" label-position="left">
            <!-- 关联需求 -->
            <el-form-item label="关联需求:" required>
                <el-select v-model="formDataRef.relatedRequirement" placeholder="数据采集需求" class="full-width-input">
                    <el-option label="数据采集需求" value="数据采集需求"></el-option>
                </el-select>
            </el-form-item>

            <!-- 任务名称 -->
            <!-- 任务名称 -->
            <el-form-item label="任务名称:" required>
                <el-input v-model="formDataRef.taskName" placeholder="数据采集任务" class="full-width-input"></el-input>
            </el-form-item>

            <!-- 优先级 -->
            <!--            <el-form-item label="优先级:" required>-->
            <!--                <el-select v-model="formDataRef.priority" placeholder="高" class="full-width-input">-->
            <!--                    <el-option label="高" value="高"></el-option>-->
            <!--                    <el-option label="中" value="中"></el-option>-->
            <!--                    <el-option label="低" value="低"></el-option>-->
            <!--                </el-select>-->
            <!--            </el-form-item>-->

            <!-- 执行方式 -->
            <!--            <el-form-item label="执行方式:" required>-->
            <!--                <el-select v-model="formDataRef.executionMode" placeholder="定时执行" class="input-with-select">-->
            <!--                    <el-option label="定时执行" value="定时执行"></el-option>-->
            <!--                    <el-option label="手动执行" value="手动执行"></el-option>-->
            <!--                </el-select>-->
            <!--            </el-form-item>-->

            <!-- 周期选择 -->
            <!--            <el-form-item label="周期选择:">-->
            <!--                <el-date-picker-->
            <!--                    v-model="formDataRef.cronExpression"-->
            <!--                    type="datetime"-->
            <!--                    placeholder="0201-02562"-->
            <!--                    class="full-width-input"-->
            <!--                    format="YYYY-MM-DD HH:mm:ss"-->
            <!--                    value-format="YYYY-MM-DD HH:mm:ss"-->
            <!--                ></el-date-picker>-->
            <!--            </el-form-item>-->

            <!-- 数据源 -->
            <el-form-item label="来源数据源:" required>
                <el-select
                    v-model="formDataRef.sourceDatabase"
                    @change="getSourceTableList"
                    placeholder="请选择"
                    style="width: 100%"
                >
                    <el-option
                        v-for="item in sourceDatabaseList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>

            <!-- 目标数据库 -->
            <el-form-item label="目标数据源:" required>
                <el-select
                    v-model="formDataRef.targetDatabase"
                    @change="getTargetTableList"
                    placeholder="请选择"
                    style="width: 100%"
                >
                    <el-option
                        v-for="item in targetDatabaseList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    ></el-option>
                </el-select>
            </el-form-item>

            <!-- 待汇聚的表 -->
            <div class="section-block">
                <div class="section-title">
                    <span>待汇聚的表</span>
                    <el-button type="primary" plain size="small" class="add-button" @click="showSourceTableDialog">
                        修改
                    </el-button>
                </div>

                <div class="table-search">
                    <el-input placeholder="搜索表" v-model="tableSearch" prefix-icon="Search"></el-input>
                </div>

                <div class="tables-list">
                    <div class="table-item" v-for="table in selectedTables" :key="table.id">
                        <div class="table-name">
                            <span class="doc-icon"></span>
                            <span>{{ table.name }}</span>
                        </div>
                        <div class="table-actions">
                            <el-button type="text" class="action-btn preview" @click="handlePreview(table.id)">
                                预览
                            </el-button>
                            <el-button type="text" class="action-btn delete" @click="removeTable(table.id)">
                                删除
                            </el-button>
                        </div>
                    </div>
                </div>

<!--                <div class="table-summary" v-if="selectedTables.length > 0">共{{ selectedTables.length }}张表</div>-->
            </div>

            <!-- 汇聚配置 -->
            <div class="section-block">
                <div class="section-title">
                    <span>汇聚配置</span>
                </div>

                <!-- 汇聚方式 -->
                <el-form-item label="汇聚方式:" required>
                    <div class="radio-group">
                        <el-radio
                            v-model="formDataRef.collectType"
                            v-for="option in formDataRef.collectTypeOptions"
                            :key="option.dictKey"
                            :label="option.dictKey"
                            @change="changeSwapType"
                            class="radio-item"
                        >
                            {{ option.dictValue }}
                        </el-radio>
                    </div>
                </el-form-item>

                <!-- 目标表 -->
                <el-form-item label="目标表:" required>
                    <el-select v-model="formDataRef.targetTable" placeholder="请选择" style="width: 100%">
                        <el-option
                            v-for="item in targetTableList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>

                <!-- 增量模式下显示的字段 -->
                <template v-if="formDataRef.collectType === '2'">
                    <!-- 增量机制 -->
                    <el-form-item label="增量机制:" required>
                        <el-select
                            v-model="formDataRef.incrementType"
                            placeholder="按标识增量"
                            @change="changeIncrementType"
                            class="full-width-input"
                        >
                            <el-option
                                v-for="option in formDataRef.incrementTypeOptions"
                                :key="option.dictKey"
                                :label="option.dictValue"
                                :value="option.dictKey"
                            ></el-option>
                        </el-select>
                    </el-form-item>

                    <!-- 源表标识字段 -->
                    <el-form-item label="源表标识字段:">
                        <el-select
                            v-model="formDataRef.sourceIncrementField"
                            @change="changeSourceIncrementField"
                            placeholder="请选择"
                            class="full-width-input"
                        >
                            <el-option
                                v-for="option in sourceTableColumn"
                                :key="option.name"
                                :label="option.comment"
                                :value="option.name"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </template>

                <!-- 待汇聚标识 -->
                <el-form-item v-if="isShowAggMark" label="待汇聚标识:">
                    <el-input v-model="formDataRef.pendingMark" class="full-width-input"></el-input>
                </el-form-item>

                <!-- 已汇聚标识 -->
                <el-form-item v-if="isShowAggMark" label="已汇聚标识:">
                    <el-input v-model="formDataRef.completedMark" class="full-width-input"></el-input>
                </el-form-item>
            </div>

            <!-- 操作按钮 -->
            <div class="form-actions">
                <el-button @click="hideRightPanel()">取消</el-button>
                <el-button type="primary" @click="confirm()">确定</el-button>
            </div>
        </el-form>

        <!-- 源表选择弹窗 -->
        <el-dialog v-model="sourceTableDialogVisible" title="源表选择" width="80%" :close-on-click-modal="false">
            <div class="dialog-content">
                <!-- 左侧待选择表 -->
                <div class="source-tables">
                    <div class="table-section-header">
<!--                        <el-checkbox-->
<!--                            v-model="selectAllSourceTables"-->
<!--                            :indeterminate="isIndeterminate"-->
<!--                            @change="handleSelectAllSourceTables"-->
<!--                        >-->
<!--                        </el-checkbox>-->
                        请选择需要配置的表

                        <!--                        <span class="count">0/49</span>-->
                    </div>
                    <div class="search-box">
                        <el-input
                            placeholder="请输入表名称"
                            prefix-icon="Search"
                            v-model="sourceTableSearch"
                        ></el-input>
                    </div>
                    <div class="table-list">
                        <el-radio-group v-model="checkedSourceTables" @change="handleSourceTableChange">
                            <el-radio v-for="item in filteredSourceTables" :key="item.id" :label="item.id">
                                {{ item.name }}
                            </el-radio>
                        </el-radio-group>
                    </div>
                    <div class="pagination">
                        <el-button icon="ArrowLeft" circle></el-button>
                        <el-button icon="ArrowRight" circle></el-button>
                    </div>
                </div>

<!--                &lt;!&ndash; 中间操作按钮 &ndash;&gt;-->
<!--                <div class="transfer-actions">-->
<!--                    <el-button-->
<!--                        type="primary"-->
<!--                        icon="ArrowRight"-->
<!--                        circle-->
<!--                        :disabled="checkedSourceTables.length === 0"-->
<!--                        @click="addToSelectedTables"-->
<!--                    ></el-button>-->
<!--                    <el-button-->
<!--                        type="primary"-->
<!--                        icon="ArrowLeft"-->
<!--                        circle-->
<!--                        :disabled="checkedSelectedTables.length === 0"-->
<!--                        @click="removeFromSelectedTables"-->
<!--                    ></el-button>-->
<!--                </div>-->

                <!-- 右侧已选择表 -->
<!--                <div class="selected-tables">-->
<!--                    <div class="table-section-header">-->
<!--                        <el-checkbox-->
<!--                            v-model="selectAllSelectedTables"-->
<!--                            :indeterminate="isSelectedIndeterminate"-->
<!--                            @change="handleSelectAllSelectedTables"-->
<!--                        >-->
<!--                            已选择的表-->
<!--                        </el-checkbox>-->
<!--                        <span class="count">{{ dialogSelectedTables.length }}/{{ dialogSelectedTables.length }}</span>-->
<!--                    </div>-->
<!--                    <div class="search-box">-->
<!--                        <el-input-->
<!--                            placeholder="请输入表名称"-->
<!--                            prefix-icon="Search"-->
<!--                            v-model="selectedTableSearch"-->
<!--                        ></el-input>-->
<!--                    </div>-->
<!--                    <div class="table-list">-->
<!--                        <el-checkbox-group v-model="checkedSelectedTables" @change="handleSelectedTableChange">-->
<!--                            <el-checkbox v-for="item in filteredDialogSelectedTables" :key="item.id" :label="item.id">-->
<!--                                {{ item.name }}-->
<!--                            </el-checkbox>-->
<!--                        </el-checkbox-group>-->
<!--                    </div>-->
<!--                </div>-->
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="sourceTableDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmSelectTables">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="editCjrw">
import { ref, computed, onMounted, watch } from 'vue';
import { selectDictByDB as selectDataBaseDict, getTables, getColumns } from '@/api/dialogue/data-cpn-util-api';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
const { handleActionReply } = useDialogueHandler();

/**
 * 在组件挂载完成后触发componentMounted事件
 */
onMounted(() => {
    getDictList();
    // getSourceTableList();
    // getTargetTableList();
    if (formDataRef.value.incrementType === '2') {
        isShowAggMark.value = true;
    } else {
        isShowAggMark.value = false;
    }
});
const props = defineProps({
    formData: {
        type: Array,
        default: () => {},
    },
    confirmCallback: {
        type: Function,
        default: () => {},
    },
});
// 这样声明会将父组件传递的formData赋值给editedFormData，并且保持响应性。
// const editedFormData = ref(props.formData);
const editedFormData = ref();
// 表单数据
const formDataRef = ref({
    relatedRequirement: '为企业查找可申报政策',
    taskName: '政策发布数据汇聚任务',
    priority: '高',
    executionMode: '定时执行',
    cronExpression: '0201-02562',
    sourceDatabase: '',
    targetDatabase: '',
    collectType: 'all',
    collectTypeOptions: [],

    targetTable: '',
    incrementType: '',
    incrementTypeIsShow: '',
    incrementTypeOptions: [],
    sourceIncrementField: '',
    sourceIncrementFieldIsShow: '',
    pendingMark: '',
    pendingMarkIsShow: '',
    completedMark: '',
    completedMarkIsShow: '',
});

// 表搜索
const tableSearch = ref('');

// 已选择的表（主页面显示）
const selectedTables = ref([]);

// 源表选择弹窗相关
const sourceTableDialogVisible = ref(false);
const selectAllSourceTables = ref(false);
const selectAllSelectedTables = ref(false);
const isIndeterminate = ref(false);
const isSelectedIndeterminate = ref(false);
const checkedSourceTables = ref('');
const checkedSelectedTables = ref([]);
const sourceTableSearch = ref('');
const selectedTableSearch = ref('');
const sourceDatabaseList = ref([]);
const targetDatabaseList = ref([]);
const sourceTableList = ref([]);
const targetTableList = ref([]);
const selectedSourceDatabase = ref({});
const sourceTableColumn = ref([]);
const isShowAggMark = ref(false);
const isShowSourceIncrementField = ref(false);

// 弹窗中的已选择表（临时存储，点击确定后才会添加到主界面）
const dialogSelectedTables = ref([]);

watch(
    () => props.formData,
    () => {
        let tableId = '';
        let tableName = '';
        selectedTables.value = [];
        props.formData.forEach(item => {
            editedFormData.value = props.formData.map(item => ({ ...item, index: item.index }));
            if (item.id === 'sourceTable') {
                formDataRef.value.sourceTable = item.tableName;
                formDataRef.value.sourceDatabase = item.database;
                tableId = item.tableName;
                tableName = item.value;
                selectedTables.value.push({ id: tableId, name: tableName });
            } else if (item.id === 'targetTable') {
                formDataRef.value.targetTable = item.tableName;
                formDataRef.value.targetDatabase = item.database;
            } else if (item.id === 'collectType') {
                formDataRef.value.collectType = item.value;
                formDataRef.value.collectTypeOptions = item.options;
            } else if (item.id === 'incrementType') {
                formDataRef.value.incrementType = item.value;
                formDataRef.value.incrementTypeOptions = item.options;
            } else if (item.id === 'sourceIncrementField') {
                formDataRef.value.sourceIncrementField = item.value;
                formDataRef.value.sourceIncrementFieldIsShow = item.isShow;
            } else if (item.id === 'pendingMark') {
                formDataRef.value.pendingMark = item.value;
                formDataRef.value.pendingMarkIsShow = item.isShow;
            } else if (item.id === 'completedMark') {
                formDataRef.value.completedMark = item.value;
                formDataRef.value.completedMarkIsShow = item.isShow;
            }
        });
        //TODO 属性显隐暂时未解决
        console.log('formDataRef', formDataRef.value);
    },
    {
        deep: true,
        immediate: true,
    }
);

// 修改这些变量存储的是当前编辑的配置，而不是直接修改editedFormData
const tempEditState = ref({
    collectType: '',
    incrementType: '',
    sourceIncrementField: '',
    isShowSourceIncrementField: false,
    isShowAggMark: false,
});

// 源表列表
const sourceTables = ref([
    { id: 1, name: '基本条件主题（aa_test_source）' },
    { id: 2, name: '基本条件主题（aa_test_target）' },
    { id: 3, name: '企业基本标签表（base_policylab_enterprise_tag）' },
    { id: 4, name: '企业资助概况（biz_enterprise_grant）' },
    { id: 5, name: '企业统计信息概况（biz_enterprise_statistic）' },
    { id: 6, name: '精准匹配条件表（cfg_permit_bf_condition）' },
    { id: 7, name: '政策项目表（cfg_permit_item_info）' },
]);

// 过滤后的源表
const filteredSourceTables = computed(() => {
    if (!sourceTableSearch.value) return sourceTableList.value;
    return sourceTableList.value.filter(table =>
        table.name.toLowerCase().includes(sourceTableSearch.value.toLowerCase())
    );
});

// 过滤后的已选择表
const filteredDialogSelectedTables = computed(() => {
    if (!selectedTableSearch.value) return dialogSelectedTables.value;
    return dialogSelectedTables.value.filter(table =>
        table.name.toLowerCase().includes(selectedTableSearch.value.toLowerCase())
    );
});

// 显示源表选择弹窗
const showSourceTableDialog = () => {
    // 打开弹窗时，初始化弹窗中的已选择表为主界面中已有的表
    checkedSourceTables.value = JSON.parse(JSON.stringify(selectedTables.value))[0]?.id;
    sourceTableDialogVisible.value = true;
    // 清空选中状态
    // checkedSourceTables.value = [];
    // checkedSelectedTables.value = [];
};

// 从左侧添加到右侧
const addToSelectedTables = () => {
    // 将选中的表从源表列表添加到已选择的表列表
    const tablesToAdd = checkedSourceTables.value
        .map(id => {
            return sourceTableList.value.find(item => item.id === id);
        })
        .filter(Boolean);

    // 检查是否已存在，避免重复添加
    tablesToAdd.forEach(table => {
        if (!dialogSelectedTables.value.some(item => item.id === table.id)) {
            dialogSelectedTables.value.push({ ...table });
        }
    });

    // 清空选中状态
    checkedSourceTables.value = [];
    selectAllSourceTables.value = false;
    isIndeterminate.value = false;
};

// 从右侧移回左侧
const removeFromSelectedTables = () => {
    // 移除选中的表
    dialogSelectedTables.value = dialogSelectedTables.value.filter(
        table => !checkedSelectedTables.value.includes(table.id)
    );

    // 清空选中状态
    checkedSelectedTables.value = [];
    selectAllSelectedTables.value = false;
    isSelectedIndeterminate.value = false;
};

// 确认选择表格，将弹窗中的已选择表添加到主界面
const confirmSelectTables = () => {
    // 更新主界面中的表列表
    selectedTables.value = [sourceTableList.value.find(item => item.id === checkedSourceTables.value)];
    console.log('selectedTables', selectedTables.value);
    console.log('checkedSourceTables', checkedSourceTables.value);
    // 关闭弹窗
    sourceTableDialogVisible.value = false;
};

// 删除主界面中的表
const removeTable = id => {
    selectedTables.value = selectedTables.value.filter(table => table.id !== id);
};

// 处理全选源表
const handleSelectAllSourceTables = val => {
    checkedSourceTables.value = val ? filteredSourceTables.value.map(item => item.id) : [];
    isIndeterminate.value = false;
};

// 处理全选已选择表
const handleSelectAllSelectedTables = val => {
    checkedSelectedTables.value = val ? filteredDialogSelectedTables.value.map(item => item.id) : [];
    isSelectedIndeterminate.value = false;
};

// 处理源表选择变化
const handleSourceTableChange = value => {
    // const checkedCount = value.length;
    // selectAllSourceTables.value = checkedCount === filteredSourceTables.value.length && checkedCount > 0;
    // isIndeterminate.value = checkedCount > 0 && checkedCount < filteredSourceTables.value.length;

};

// 处理已选择表选择变化
const handleSelectedTableChange = value => {
    const checkedCount = value.length;
    selectAllSelectedTables.value = checkedCount === filteredDialogSelectedTables.value.length && checkedCount > 0;
    isSelectedIndeterminate.value = checkedCount > 0 && checkedCount < filteredDialogSelectedTables.value.length;
};

// 定义方法
function getDictList() {
    //入口数据库
    selectDataBaseDict({ dbType: '1', state: 1 }).then(res => {
        let data = res.data.data;
        sourceDatabaseList.value = data;
        getSourceTableList();
    });
    //数仓数据库
    selectDataBaseDict({ dbType: '2', state: 1 }).then(res => {
        let data = res.data.data;
        targetDatabaseList.value = data;
        getTargetTableList();
    });
}

// 获取源表列表
function getSourceTableList() {
    const sourceDatabase = sourceDatabaseList.value.find(option => option.id === formDataRef.value.sourceDatabase);
    selectedSourceDatabase.value = sourceDatabase;
    getTables(sourceDatabase.id).then(res => {
        const result = res.data;
        if (result.success) {
            result.data.forEach(val => {
                sourceTableList.value.push({
                    id: val.name,
                    name: val.comment + '(' + val.name + ')',
                });
            });
            getColumnList();
        }
    });
}

// 获取目标表列表
function getTargetTableList() {
    const targetDatabase = targetDatabaseList.value.find(option => option.id === formDataRef.value.targetDatabase);

    getTables(targetDatabase.id).then(res => {
        const result = res.data;
        if (result.success) {
            result.data.forEach(val => {
                targetTableList.value.push({
                    id: val.name,
                    name: val.comment + '(' + val.name + ')',
                });
            });
        }
    });
}

// 获取右侧面板状态管理
const { setRightPanel, hideRightPanel } = useRightPanelStore();
const rightPanelTitle = ref('');
/**
 * 处理预览源表按钮点击事件
 * @param {Object} item - 配置项
 */
const handlePreview = item => {
    let data = {
        actionCode: 'tablePreview',
        // msg: '确认指标基本信息',
        // dialogueId: props.data.dialogueId,
        params: {
            tableName: item,
            databaseId: selectedSourceDatabase.value.id,
        },
        callBack: previewTable,
    };
    handleActionReply(data);
};

const previewTable = result => {
    let tableRow = result.data.columns;
    let tableData = result.data.rowData;
    // 可以实现预览源表数据的逻辑
    setRightPanel('policiesYwb', {
        title: rightPanelTitle.value,
        rowData: {
            tableRow: tableRow,
            tableData: tableData,
        },
    });
};

// 修改确认逻辑，只有在点击确认按钮时才更新editedFormData
const confirm = () => {
    // 创建一个深拷贝，避免直接修改
    const updatedFormData = JSON.parse(JSON.stringify(editedFormData.value));

    updatedFormData.forEach(item => {
        if (item.id === 'sourceTable') {
            item.database = formDataRef.value.sourceDatabase;
            if (selectedTables.value.length > 0) {
                item.value = selectedTables.value[0].name;
                item.tableName = selectedTables.value[0].id;
            }
        } else if (item.id === 'targetTable') {
            item.tableName = formDataRef.value.targetTable;
            item.database = formDataRef.value.targetDatabase;
            const targetTable = targetTableList.value.find(option => option.id === formDataRef.value.targetTable);
            item.value = targetTable ? targetTable.name : '';
            item.tableName = targetTable ? targetTable.id : '';
        } else if (item.id === 'collectType') {
            item.value = formDataRef.value.collectType;
        } else if (item.id === 'incrementType') {
            item.value = formDataRef.value.incrementType;
            item.isShow = formDataRef.value.incrementTypeIsShow;
        } else if (item.id === 'sourceIncrementField') {
            item.value = formDataRef.value.sourceIncrementField;
            item.isShow = formDataRef.value.sourceIncrementFieldIsShow;
        } else if (item.id === 'pendingMark') {
            item.value = formDataRef.value.pendingMark;
            item.isShow = formDataRef.value.pendingMarkIsShow;
        } else if (item.id === 'completedMark') {
            item.value = formDataRef.value.completedMark;
            item.isShow = formDataRef.value.completedMarkIsShow;
        }
    });

    // 将更新后的数据传递给父组件
    props.confirmCallback(updatedFormData);
    hideRightPanel();
};

// 获取源表列表
function getColumnList() {
    const sourceDatabase = sourceDatabaseList.value.find(option => option.id === formDataRef.value.sourceDatabase);
    getColumns(sourceDatabase.id, formDataRef.value.sourceTable).then(res => {
        const result = res.data;
        if (result.success) {
            result.data.forEach(val => {
                sourceTableColumn.value.push({
                    name: val.name,
                    comment: val.comment,
                });
            });
        }
    });
}

// 修改汇聚方式变更处理函数，不再直接更新editedFormData
const changeSwapType = val => {
    formDataRef.value.collectType = val;

    // 根据汇聚方式更新表单数据显示状态
    if (val === '2') {
        formDataRef.value.incrementTypeIsShow = true;
        if (formDataRef.value.incrementType === '2') {
            formDataRef.value.sourceIncrementFieldIsShow = true;
            formDataRef.value.pendingMarkIsShow = true;
            formDataRef.value.completedMarkIsShow = true;
            isShowAggMark.value = true;
        } else {
            formDataRef.value.sourceIncrementFieldIsShow = true;
            formDataRef.value.pendingMarkIsShow = false;
            formDataRef.value.completedMarkIsShow = false;
            isShowAggMark.value = false;
        }
    } else {
        formDataRef.value.incrementTypeIsShow = false;
        formDataRef.value.sourceIncrementFieldIsShow = false;
        formDataRef.value.pendingMarkIsShow = false;
        formDataRef.value.completedMarkIsShow = false;
        isShowAggMark.value = false;
    }
};

// 修改增量类型变更处理函数，不再直接更新editedFormData
const changeIncrementType = val => {
    formDataRef.value.incrementType = val;
    formDataRef.value.sourceIncrementField = '';

    // 根据增量类型更新显示状态
    if (val === '2') {
        formDataRef.value.sourceIncrementFieldIsShow = true;
        formDataRef.value.pendingMarkIsShow = true;
        formDataRef.value.completedMarkIsShow = true;
        isShowAggMark.value = true;
    } else {
        formDataRef.value.sourceIncrementFieldIsShow = true;
        formDataRef.value.pendingMarkIsShow = false;
        formDataRef.value.completedMarkIsShow = false;
        isShowAggMark.value = false;
    }
};

// 修改源表字段变更处理函数，不再直接更新editedFormData
const changeSourceIncrementField = val => {
    formDataRef.value.sourceIncrementField = val;
    //如果增量机制为按时间增量，则不用展示待汇聚标识和已汇聚标识字段
    if (formDataRef.value.incrementType === '2') {
        isShowAggMark.value = true;
    } else {
        isShowAggMark.value = false;
    }
};
</script>

<style scoped lang="scss">
.edit-task-container {
    padding: 20px;
    background-color: #f5f7fa;

    .task-form {
        width: 100%;

        :deep(.el-form-item) {
            margin-bottom: 18px;

            .el-form-item__label {
                color: #606266;
                font-weight: normal;

                &::before {
                    color: #ef6820;
                }
            }

            .el-form-item__content {
                position: relative;

                .icon-edit {
                    position: absolute;
                    right: 0;
                    top: 0;
                    height: 32px;
                    color: #909399;
                }

                .el-select,
                .el-date-picker {
                    width: 100%;
                }
            }
        }

        .full-width-input {
            width: 100%;
        }

        .input-with-select {
            width: calc(100% - 120px);
        }
    }

    .section-block {
        background-color: $bg;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

        .section-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-weight: bold;

            .add-button {
                padding: 5px 10px;
                font-size: 12px;
            }
        }

        .table-search {
            margin-bottom: 15px;

            .el-input {
                width: 100%;
            }
        }

        .tables-list {
            .table-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 10px 0;
                border-bottom: 1px solid #ebeef5;

                .table-name {
                    display: flex;
                    align-items: center;
                    color: #1570ef;

                    .doc-icon {
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        margin-right: 6px;
                        background-color: #e6f1fc;
                        border-radius: 3px;
                        position: relative;

                        &:before {
                            content: '';
                            position: absolute;
                            width: 10px;
                            height: 2px;
                            background-color: #1570ef;
                            top: 7px;
                            left: 3px;
                        }
                    }
                }

                .table-actions {
                    display: flex;
                    .action-btn {
                        margin-left: 10px;
                        padding: 0;

                        &.config {
                            color: #16b364;
                        }

                        &.preview {
                            color: #1570ef;
                        }

                        &.delete {
                            color: #ef6820;
                        }
                    }
                }
            }
        }

        .table-summary {
            text-align: right;
            color: #909399;
            font-size: 12px;
            margin-top: 10px;
        }

        .radio-group {
            display: flex;
            align-items: center;

            .radio-item {
                margin-right: 20px;
            }
        }
    }

    .form-actions {
        display: flex;
        justify-content: center;
        margin-top: 30px;

        .el-button {
            padding: 10px 20px;
            min-width: 100px;
        }
    }
}

.dialog-content {
    display: flex;
    align-items: stretch;

    .source-tables,
    .selected-tables {
        flex: 1;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        padding: 10px;
        margin: 0 10px;

        .table-section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;

            .count {
                color: #909399;
                font-size: 12px;
            }
        }

        .search-box {
            margin-bottom: 10px;
        }

        .table-list {
            height: 280px;
            overflow-y: auto;
            border: 1px solid #ebeef5;
            padding: 10px;
            display: flex;
            flex-direction: column;

            .el-checkbox {
                margin-bottom: 10px;
            }
        }

        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 10px;
        }
    }

    .transfer-actions {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0 10px;

        .el-button {
            margin: 5px 0;
        }
    }
}
</style>

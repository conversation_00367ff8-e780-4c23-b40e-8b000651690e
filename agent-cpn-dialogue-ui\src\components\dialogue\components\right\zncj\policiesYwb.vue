<template>
    <div class="task_dialog">
        <div class="task_dialog_content">
            <el-table
                :data="tableDataRef"
                :max-height="800"
                border
                stripe
                style="width: 100%"
                :header-cell-style="{ background: '#f4f9fe', color: '#1d2939', fontSize: '12px' }"
                :cell-style="{ fontSize: '12px', color: '#1d2939' }"
                header-row-class-name="header-table"
            >
                <!-- 序号列 -->
                <el-table-column type="index" label="序号" width="60" align="center" :index="indexMethod" />
                <el-table-column
                    v-for="(itemField, index) in tableRowRef"
                    :key="index"
                    :prop="itemField.name"
                    :label="itemField.comment"
                    :min-width="92"
                    show-overflow-tooltip
                    header-row-class-name="table-header"
                >
                    <template #default="scope">
                        {{ scope.row[itemField.name] }}
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup name="policiesYwb">
/**
 * 政策原文表组件
 * 展示贴源层-政策原文表数据
 */
import { ref, watch } from 'vue';

// 定义emit函数
const emit = defineEmits(['handleHide']);

const props = defineProps({
    rowData: {
        type: Object,
        default: () => {},
    },
});
const tableRowRef = ref([]);
const tableDataRef = ref([]);

watch(
    () => props.rowData,
    () => {
        // 在组件挂载时处理props.data
        tableRowRef.value = props.rowData?.tableRow;
        tableDataRef.value = props.rowData?.tableData;
    },
    {
        deep: true,
        immediate: true,
    }
);
/**
 * 表格数据
 */
const tableData = ref([
    {
        id: 1,
        companyName: '智创未来科技有限公司',
        establishDate: '2013年8月15日',
        legalPerson: '李明',
        registeredCapital: '5000万元',
        registeredAddress: 'XX 市 XX 区科技大道88 号创新大厦 10 层',
        phone: '010-12345678',
        email: '<EMAIL>',
        website: 'www.smartfuture.com',
    },
    {
        id: 2,
        companyName: '慧视智能科技有限公司',
        establishDate: '2016年3月20日',
        legalPerson: '王强',
        registeredCapital: '8000万元',
        registeredAddress: 'XX 市 XX 区高新技术产业园5 号楼',
        phone: '021-87654321',
        email: '<EMAIL>',
        website: 'www.visionsmart.com',
    },
    {
        id: 3,
        companyName: '灵犀智能科技股份有限公司',
        establishDate: '2014年11月1日',
        legalPerson: '张悦',
        registeredCapital: '6000万元',
        registeredAddress: 'XX 市 XX 区智慧产业园区 8 号楼',
        phone: '0755-66668888',
        email: '<EMAIL>',
        website: 'www.lingxi.ai',
    },
    {
        id: 4,
        companyName: '智创未来科技有限公司',
        establishDate: '2013年8月15日',
        legalPerson: '李明',
        registeredCapital: '5000万元',
        registeredAddress: 'XX 市 XX 区科技大道88 号创新大厦 10 层',
        phone: '010-12345678',
        email: '<EMAIL>',
        website: 'www.smartfuture.com',
    },
    {
        id: 5,
        companyName: '慧视智能科技有限公司',
        establishDate: '2016年3月20日',
        legalPerson: '王强',
        registeredCapital: '8000万元',
        registeredAddress: 'XX 市 XX 区高新技术产业园5 号楼',
        phone: '021-87654321',
        email: '<EMAIL>',
        website: 'www.visionsmart.com',
    },
    {
        id: 6,
        companyName: '灵犀智能科技股份有限公司',
        establishDate: '2014年11月1日',
        legalPerson: '张悦',
        registeredCapital: '6000万元',
        registeredAddress: 'XX 市 XX 区智慧产业园区 8 号楼',
        phone: '0755-66668888',
        email: '<EMAIL>',
        website: 'www.lingxi.ai',
    },
]);

/**
 * 关闭对话框
 */
const handleClose = () => {
    emit('handleHide', false);
};

/**
 * 自定义序号显示方法
 * @param {number} index - 当前行索引
 * @returns {number} 显示的序号
 */
const indexMethod = index => {
    return index + 1;
};
</script>

<style lang="scss" scoped>
.task_dialog {
    .task_dialog_content {
        width: 100%;
        overflow: hidden;
    }
}

:deep(.el-table) {
    border: 1px solid #eaecf0;

    .el-table__header-wrapper {
        .el-table__header {
            th {
                background-color: #f4f9fe;
                color: #1d2939;
                font-size: 12px;
                font-weight: bold;
                border-bottom: 1px solid #eaecf0;
            }
        }
    }

    .el-table__body-wrapper {
        .el-table__body {
            td {
                font-size: 12px;
                color: #1d2939;
                border-bottom: 1px solid #eaecf0;
                padding: 10px;
                line-height: 16px;
                min-height: 40px;
            }

            tr:hover {
                background-color: #f9f9f9;
            }
        }
    }

    .el-scrollbar__wrap {
        overflow-x: auto;
        overflow-y: auto;
    }

    .el-table__body-wrapper {
        overflow-x: auto;
    }
}

@media (max-width: 768px) {
    :deep(.el-table) {
        .el-table__body-wrapper {
            overflow-x: auto;
        }
    }
}
.header-table {
    height: 20px;
}
</style>

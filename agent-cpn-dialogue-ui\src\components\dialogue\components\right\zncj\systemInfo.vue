<template>
    <div class="task_dialog">
        <div class="task_dialog_content">
            <div class="task_dialog_bigtitle">政策发布数据采集任务</div>

            <h3 class="task_dialog_block_title">
                <span>意图识别与分析</span>
                <i><img :src="publicPath + `/static/dialogue/fangda.png`" title="" /></i>
            </h3>
            <div class="sysinfocon">
                <h4>1、解析采集任务数据源</h4>
                <p>
                    （1）解析参数
                    <span>"来源数据源"</span>
                    为
                    <small>"工信局"</small>
                </p>
                <p>
                    （2）解析参数
                    <span>"目标数据源"</span>
                    为
                    <small>"科创局"</small>
                </p>
                <h4>2、解析到采集任务源表及目标表</h4>
                <p>
                    （1）解析参数
                    <span>"待采集数据表"</span>
                    <small>"政策原文件"</small>
                </p>
                <p>
                    （2）解析参数
                    <span>"目标数据表"</span>
                    为
                    <small>"贴源层-政策原文件"</small>
                </p>
            </div>

            <h3 class="task_dialog_block_title">
                <span>采集信息查询工具</span>
                <i><img :src="publicPath + `/static/dialogue/fangda.png`" title="" /></i>
            </h3>
            <div class="sysinfocon">
                <h4>1、调用数据源查询工具，查询"工信局"数据源信息</h4>
                <h4>2、调用数据源查询工具，查询"科创局"数据源信息</h4>
                <h4>
                    3、调用数据表查询工具，
                    <b>无法查询"政策原文表"数据表信息</b>
                </h4>
                <h4>4、调用数据表查询工具，查询"贴源层-政策原文表"数据表信息</h4>
            </div>

            <h3 class="task_dialog_block_title">
                <span>采集任务生成工具</span>
                <i><img :src="publicPath + `/static/dialogue/fangda.png`" title="" /></i>
            </h3>
            <div class="sysinfocon">
                <h4>
                    1、调用
                    <span>数据采集任务生成</span>
                    工具，填充采集信息
                </h4>
                <h4>2、生成数据采集任务</h4>
            </div>
        </div>
    </div>
</template>

<script setup name="systemInfo">
import { publicPath } from '@/components/dialogue/store/main.js';

/**
 * 系统信息组件
 * 展示政策发布数据采集任务的系统后台信息
 */

// 定义emit函数
const emit = defineEmits(['handleHide']);

/**
 * 关闭对话框
 */
const handleClose = () => {
    emit('handleHide', false);
};
</script>

<style lang="scss" scoped></style>

<template>
    <div class="task_dialog">
        <div class="task_dialog_title">
            <span>执行过程</span>
            <p>
                <img :src="publicPath + `/static/dialogue/dialogicon1.png`" title="复制" />
                <img :src="publicPath + `/static/dialogue/dialogicon2.png`" title="分享" />
                <i>|</i>
                <img
                    :src="publicPath + publicPath + `/static/dialogue/dialogicon3.png`"
                    title="关闭"
                    @click="handleClose"
                />
            </p>
        </div>
        <div class="task_dialog_content">
            <el-form :model="form" ref="formRef" class="my_form_edit" label-width="130px">
                <!-- ---------------------- 执行过程--------------------- -->

                <div class="task_dialog_bigtitle">政策原文表数据汇聚任务</div>

                <h3 class="task_dialog_block_title">
                    <span>解析资源目录基本信息</span>
                    <i><img :src="publicPath + publicPath + `/static/dialogue/fangda.png`" title="" /></i>
                </h3>
                <div class="sysinfocon">
                    <h4>
                        1、解析参数
                        <span>“资源标题”</span>
                        为
                        <small>“2025年人口基本信息统计演示数据”</small>
                    </h4>
                    <h4>
                        2、解析参数
                        <span>“更新周期”</span>
                        为
                        <small>“每日”</small>
                    </h4>
                    <h4>
                        3、解析参数
                        <span>“属性分类”</span>
                        为
                        <small>“基础资源信息”</small>
                    </h4>
                    <h4>
                        4、解析参数
                        <span>“基础分类”</span>
                        为
                        <small>“社会信用”</small>
                    </h4>
                    <h4>
                        5、解析参数
                        <span>“主题分类”</span>
                        为
                        <small>“综合政务”</small>
                    </h4>
                    <h4>
                        6、解析参数
                        <span>“部门”</span>
                        为
                        <small>“公安局”</small>
                    </h4>
                    <h4>
                        7、解析参数
                        <span>“共享类型”</span>
                        为
                        <small>“有条件共享”</small>
                    </h4>
                    <h4>
                        8、解析参数
                        <span>“开放类型”</span>
                        为
                        <small>“依申请开放”</small>
                    </h4>
                </div>

                <h3 class="task_dialog_block_title">
                    <span>目录基本信息配置工具</span>
                    <i><img :src="publicPath + publicPath + `/static/dialogue/fangda.png`" title="" /></i>
                </h3>
                <div class="sysinfocon">
                    <h4>
                        1、解析参数
                        <span>“资源标题”</span>
                        为
                        <small>“2025年人口基本信息统计演示数据”</small>
                    </h4>
                    <h4>
                        2、解析参数
                        <span>“更新周期”</span>
                        为
                        <small>“每日”</small>
                    </h4>
                    <h4>
                        3、解析参数
                        <span>“属性分类”</span>
                        为
                        <small>“基础资源信息”</small>
                    </h4>
                    <h4>
                        4、解析参数
                        <span>“基础分类”</span>
                        为
                        <small>“社会信用”</small>
                    </h4>
                    <h4>
                        5、解析参数
                        <span>“主题分类”</span>
                        为
                        <small>“综合政务”</small>
                    </h4>
                    <h4>
                        6、解析参数
                        <span>“部门”</span>
                        为
                        <small>“公安局”</small>
                    </h4>
                    <h4>
                        7、解析参数
                        <span>“共享类型”</span>
                        为
                        <small>“有条件共享”</small>
                    </h4>
                    <h4>
                        8、解析参数
                        <span>“开放类型”</span>
                        为
                        <small>“依申请开放”</small>
                    </h4>
                </div>

                <h3 class="task_dialog_block_title">
                    <span>解析资源类别信息</span>
                    <i><img :src="publicPath + publicPath + `/static/dialogue/fangda.png`" title="" /></i>
                </h3>
                <div class="sysinfocon">
                    <h4>
                        1、解析编目任务参数
                        <span>“资源类别”</span>
                        为
                        <small>“结构化数据”</small>
                    </h4>
                    <h4>
                        2、解析结构化数据参数
                        <span>“资源系统”</span>
                        为
                        <small>“公安局演示系统”</small>
                    </h4>
                    <h4>
                        3、解析结构化数据参数
                        <span>“资源数据库”</span>
                        为
                        <small>“公安局数据库”</small>
                    </h4>
                    <h4>
                        4、解析结构化数据参数
                        <span>“表名”</span>
                        为
                        <small>“人口信息演示表”</small>
                    </h4>
                    <h4>
                        5、解析结构化数据指标参数
                        <span>“指标信息”</span>
                        为
                        <small>“省、市、区、街道、社区、人口数量、统计时间”</small>
                    </h4>
                </div>

                <h3 class="task_dialog_block_title">
                    <span>资源类别配置工具</span>
                    <i><img :src="publicPath + publicPath + `/static/dialogue/fangda.png`" title="" /></i>
                </h3>
                <div class="sysinfocon">
                    <h4>
                        1、调用
                        <span>“应用系统”</span>
                        查询工具，查询
                        <small>“公安局演示系统”</small>
                        信息
                    </h4>
                    <h4>
                        2、调用
                        <span>“数据源”</span>
                        查询工具，查询
                        <small>“公安局数据库”</small>
                        信息
                    </h4>
                    <h4>
                        3、调用
                        <span>“数据表”</span>
                        查询工具，查询
                        <small>“人口信息演示表”</small>
                        信息
                    </h4>
                    <h4>
                        4、调用
                        <span>“数据字段”</span>
                        查询工具，查询
                        <small>“省、市、区、街道、社区、人口数量、统计时间”</small>
                        字段信息
                    </h4>
                </div>

                <h3 class="task_dialog_block_title">
                    <span>资源目录生成工具</span>
                    <i><img :src="publicPath + publicPath + `/static/dialogue/fangda.png`" title="" /></i>
                </h3>
                <div class="sysinfocon">
                    <h4>
                        1、调用
                        <span>资源目录生成</span>
                        工具，填充生成”2025年人口基本信息统计演示数据“目录
                    </h4>
                    <h4>2、提交目录审核</h4>
                </div>
            </el-form>
        </div>
    </div>
</template>

<script>
import { publicPath } from '@/components/dialogue/store/main.js';

export default {
    name: 'backInfo',
    components: {},
    data() {
        return {
            showTaskDialog: true, // 显示窗口
            form: {
                relatedDemand: '',
                taskName: '',
                executionMethod: '',
                priority: '',
                dataSource: '',
                cycle: '',
                targetDatabase: '',
                hjfs: '',
                mbb: '',
                searchText: '',
            },
            formRef: null,

            tableData: [
                {
                    id: 1,
                    data1: '姓名',
                    data2: '-',
                    data3: 'name',
                    data4: '255',
                    data5: '-',
                    data6: '整型值',
                    data7: '-',
                    data8: '-',
                    data9: '-',
                    data10: '-',
                },
                {
                    id: 2,
                    data1: '姓名',
                    data2: '-',
                    data3: 'name',
                    data4: '255',
                    data5: '-',
                    data6: '整型值',
                    data7: '-',
                    data8: '-',
                    data9: '-',
                    data10: '-',
                },
                {
                    id: 3,
                    data1: '姓名',
                    data2: '-',
                    data3: 'name',
                    data4: '255',
                    data5: '-',
                    data6: '整型值',
                    data7: '-',
                    data8: '-',
                    data9: '-',
                    data10: '-',
                },
                {
                    id: 4,
                    data1: '姓名',
                    data2: '-',
                    data3: 'name',
                    data4: '255',
                    data5: '-',
                    data6: '整型值',
                    data7: '-',
                    data8: '-',
                    data9: '-',
                    data10: '-',
                },
            ],
        };
    },
    methods: {
        //确认
        onSubmit() {
            if (this.$refs.formRef) {
                this.$refs.formRef.validate(function (valid) {
                    if (valid) {
                        alert('提交成功');
                    } else {
                        alert('表单验证失败');
                        return false;
                    }
                });
            }
        },
        //取消
        resetForm() {
            if (this.$refs.formRef) {
                this.$refs.formRef.resetFields();
            }
            this.$emit('handleHide', false);
        },
        //关闭
        handleClose() {
            this.$emit('handleHide', false);
        },
    },
};
</script>

<style lang="scss" scoped></style>

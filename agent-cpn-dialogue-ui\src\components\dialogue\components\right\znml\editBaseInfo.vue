<template>
    <div class="edit-base-info">
        <el-form :model="form" ref="formRef" :rules="rules" label-width="100px" class="edit-form">
            <el-form-item label="资源标题：" prop="name">
                <el-input v-model="form.name" placeholder="请输入资源标题"></el-input>
            </el-form-item>

            <el-form-item label="更新周期：" prop="updateCycle" class="update-date-item">
                <el-radio-group v-model="form.updateCycle">
                    <el-radio
                        v-for="item in dictData.updateCycle"
                        :value="item.dictKey"
                        :label="item.dictKey"
                        :key="item.dictKey"
                    >
                        {{ item.dictValue }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item label="属性分类：" prop="resType" class="attribute-type-item">
                <el-radio-group v-model="form.resType">
                    <el-radio
                        v-for="item in dictData.resType"
                        :value="item.dictKey"
                        :label="item.dictKey"
                        :key="item.dictKey"
                    >
                        {{ item.dictValue }}
                    </el-radio>
                </el-radio-group>
            </el-form-item>

            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="基础分类：" prop="baseType">
                        <el-tree-select
                            v-model="form.baseType"
                            :data="dictData.baseCategory"
                            :render-after-expand="false"
                        />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="主题分类：" prop="subjectType">
                        <el-tree-select
                            v-model="form.subjectType"
                            :data="dictData.subjectType"
                            :render-after-expand="false"
                        />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="12">
                    <el-form-item label="部门：" prop="deptId">
                        <el-tree-select v-model="form.deptId" :data="dictData.deptId" :render-after-expand="false" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="共享类型：" prop="shareType">
                        <el-select v-model="form.shareType" placeholder="请选择共享类型">
                            <el-option
                                :value="item.dictKey"
                                :label="item.dictValue"
                                v-for="(item, index) in dictData.shareType"
                                :key="index"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-form-item label="共享条件：" prop="shareCondition" v-if="form.shareType === 1">
                <el-input v-model="form.shareCondition" placeholder="请输入共享条件"></el-input>
            </el-form-item>

            <el-row :gutter="20" v-if="form.shareType !== 3">
                <el-col :span="12">
                    <el-form-item label="共享方式：" prop="shareWay">
                        <el-select v-model="form.shareWay" placeholder="请选择共享方式">
                            <el-option
                                :value="item.dictKey"
                                :label="item.dictValue"
                                v-for="(item, index) in dictData.shareWay"
                                :key="index"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="共享范围：" prop="shareScope">
                        <el-select v-model="form.shareScope" placeholder="请选择共享范围">
                            <el-option
                                :value="item.dictKey"
                                :label="item.dictValue"
                                v-for="(item, index) in dictData.shareScope"
                                :key="index"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="12" v-if="form.shareType === 3">
                    <el-form-item label="不共享理由：" prop="shareReason">
                        <el-input v-model="form.shareReason" placeholder="请输入不共享理由"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12" v-if="form.shareType === 1">
                    <el-form-item label="共享依据：" prop="shareBasis">
                        <el-input v-model="form.shareBasis" placeholder="请输入共享依据"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="开放类型：" prop="openType">
                        <el-select v-model="form.openType" placeholder="请选择标签">
                            <el-option
                                :value="item.dictKey"
                                :label="item.dictValue"
                                v-for="(item, index) in dictData.openType"
                                :key="index"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>

            <!-- 操作按钮区域 -->
            <div class="form-footer">
                <el-button @click="handleCancel" class="btn-cancel">取消</el-button>
                <el-button type="primary" @click="handleSubmit" class="btn-confirm">确定</el-button>
            </div>
        </el-form>
    </div>
</template>

<script setup name="editBaseInfo">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getDictionary } from '@/api/system/dict';
import { selectClassification } from '@/api/dialogue/data-catalog-util-api';
import { getDeptTree } from '@/api/system/dept';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';

// 获取右侧面板控制函数
const { setRightPanel, hideRightPanel } = useRightPanelStore();
const emit = defineEmits(['handleHide', 'confirm']);

const props = defineProps({
    formValue: {
        type: Object,
        default: () => ({}),
    },
});
onMounted(() => {
    getDictionary({ code: 'update_cycle' }).then(res => {
        let data = res.data.data;
        dictData.updateCycle = formatDictData(data);
    });

    getDictionary({ code: 'res_type' }).then(res => {
        let data = res.data.data;
        dictData.resType = formatDictData(data);
    });
    /**
     * 基础分类
     */
    selectClassification({ type: '4' }).then(res => {
        let data = res.data.data;
        data.forEach(item => {
            item.label = item.title;
            item.value = item.title;
        });
        dictData.baseCategory = formatTreeData(data);
    });

    /**
     * 主题分类
     */
    selectClassification({ type: '1' }).then(res => {
        let data = res.data.data;
        dictData.subjectType = formatTreeDataNumber(data);
    });
    /**
     * 部门
     */
    getDeptTree().then(res => {
        let data = res.data.data;
        dictData.deptId = formatTreeData(data);
    });
    /**
     * 开放类型
     */
    getDictionary({ code: 'open_type' }).then(res => {
        let data = res.data.data;
        dictData.openType = formatDictData(data);
    });
    /**
     * 共享类型
     */
    getDictionary({ code: 'share_type' }).then(res => {
        let data = res.data.data;
        dictData.shareType = formatDictData(data);
    });
    /**
     * 共享方式
     */
    getDictionary({ code: 'share_way' }).then(res => {
        let data = res.data.data;
        dictData.shareWay = formatDictData(data);
    });
    /**
     * 共享范围
     */
    getDictionary({ code: 'share_scope' }).then(res => {
        let data = res.data.data;
        dictData.shareScope = formatDictData(data);
    });

    form.name = props.formValue.name;
    form.shareBasis = props.formValue.shareBasis;
    form.updateCycle = props.formValue.updateCycle;
    form.resType = props.formValue.resType;
    form.baseType = props.formValue.baseType;
    form.subjectType = props.formValue.subjectType;
    form.deptId = props.formValue.deptId;
    form.shareType = props.formValue.shareType;
    form.shareCondition = props.formValue.shareCondition;
    form.shareWay = props.formValue.shareWay;
    form.shareScope = props.formValue.shareScope;
    form.shareBase = props.formValue.shareBase;
    form.openType = props.formValue.openType;
});

/**
 * 遍历格式化树形结构内容，且保持树形结构
 */
function formatTreeData(data) {
    data.forEach(item => {
        item.label = item.title;
        item.value = item.id;
        if (item.children) {
            formatTreeData(item.children);
        }
    });
    return data;
}
function formatTreeDataNumber(data) {
    data.forEach(item => {
        item.label = item.title;
        item.value = Number(item.id);
        if (item.children) {
            formatTreeData(item.children);
        }
    });
    return data;
}
function formatDictData(data) {
    data.forEach(item => {
        item.dictKey = Number(item.dictKey);
    });
    return data;
}

const dictData = reactive({
    updateCycle: [],
    resType: [],
    baseCategory: [],
    subjectType: [],
    deptId: [],
    openType: [],
    shareType: [],
    shareWay: [],
    shareScope: [],
});
/**
 * 表单数据
 * @type {Object}
 */
const form = reactive({
    name: '',
    updateCycle: '',
    resType: '',
    baseType: '',
    subjectType: '',
    deptId: '',
    shareType: '',
    shareCondition: '',
    shareWay: '',
    shareRange: '',
    shareBasis: '',
    openType: '',
});

/**
 * 表单引用
 * @type {Object}
 */
const formRef = ref(null);

/**
 * 表单验证规则
 * @type {Object}
 */
const rules = reactive({
    name: [{ required: true, message: '请输入资源标题', trigger: 'blur' }],
    updateCycle: [{ required: true, message: '请选择更新日期', trigger: 'change' }],
    resType: [{ required: true, message: '请选择属性分类', trigger: 'change' }],
    deptId: [{ required: true, message: '请选择部门', trigger: 'change' }],
    shareType: [{ required: true, message: '请选择共享类型', trigger: 'change' }],
});

/**
 * 提交表单
 */
const handleSubmit = () => {
    if (formRef.value) {
        formRef.value.validate(valid => {
            if (valid) {
                // 调用父组件方法或发送请求
                emit('confirm', form);
                // triggerComponentEvent('confirm', form);
                hideRightPanel();
                ElMessage({
                    message: '提交成功',
                    type: 'success',
                });
            } else {
                console.log('表单验证失败');
                return false;
            }
        });
    }
};

/**
 * 取消操作
 */
const handleCancel = () => {
    // 重置表单或关闭弹窗
    formRef.value?.resetFields();
    hideRightPanel();
};
</script>

<style lang="scss" scoped>
@use '@/styles/dialogue/colors.scss' as *;

.edit-base-info {
    padding: 20px 10px;
    font-size: 14px;
    font-family: Source Han Sans CN;
}

.edit-form {
    width: 100%;
}

/* 资源标题输入框背景颜色 */
:deep(.el-input__wrapper) {
    background: $form-input-bg;
    border-radius: 6px;
    border: 1px solid $form-input-border;
}

/* 单选按钮样式 */
:deep(.el-radio) {
    margin-right: 10px;
    margin-bottom: 5px;
}

/* 强制单选按钮组在小屏幕上换行 */
.update-date-item :deep(.el-radio-group),
.attribute-type-item :deep(.el-radio-group) {
    display: flex;
    flex-wrap: wrap;
}

/* 下拉选择框背景颜色 */
:deep(.el-select .el-input__wrapper) {
    background: $form-input-bg;
}

/* 表单项间距 */
:deep(.el-form-item) {
    margin-bottom: 20px;
}

/* 表单项标签颜色 */
:deep(.el-form-item__label) {
    color: $form-label-color;
}

/* 按钮区域样式 */
.form-footer {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    padding-bottom: 10px;
}

/* 取消按钮 */
.btn-cancel {
    border: 1px solid $border-base;
    background-color: $button-cancel-bg;
    margin-right: 15px;
    min-width: 80px;
}

/* 确定按钮 */
.btn-confirm {
    min-width: 80px;
    background-color: $primary-color;
}
</style>

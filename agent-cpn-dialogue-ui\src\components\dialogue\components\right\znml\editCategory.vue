<template>
    <div class="edit-category-container">
        <div class="form-container">
            <el-form
                :model="formData"
                label-position="right"
                :label-width="config.labelWidth || '91px'"
                class="custom-form"
            >
                <div class="form-row" v-for="(row, rowIndex) in formLayout" :key="rowIndex">
                    <template v-for="(field, fieldIndex) in row" :key="field.key">
                        <el-form-item
                            v-if="!field.hidden"
                            :label="field.label || getDefaultLabel(field.key)"
                            :required="field.required !== false"
                            :class="{ 'empty-item': field.empty }"
                        >
                            <!-- 选择框 -->
                            <el-select
                                v-if="field.type === 'select'"
                                v-model="formData[field.key]"
                                :placeholder="field.placeholder || `请选择${field.label || getDefaultLabel(field.key)}`"
                                class="custom-select"
                            >
                                <el-option
                                    v-for="option in getOptions(field.key)"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value"
                                ></el-option>
                            </el-select>

                            <!-- 输入框 -->
                            <el-input
                                v-else-if="field.type === 'input'"
                                v-model="formData[field.key]"
                                :disabled="field.disabled"
                                :placeholder="field.placeholder || `请输入${field.label || getDefaultLabel(field.key)}`"
                                class="custom-input"
                            ></el-input>

                            <!-- 日期范围 -->
                            <div v-else-if="field.type === 'dateRange'" class="time-range-container">
                                <el-date-picker
                                    v-model="formData.startTime"
                                    type="date"
                                    :placeholder="field.startPlaceholder || '开始日期'"
                                    class="custom-date-picker"
                                    value-format="YYYY-MM-DD"
                                ></el-date-picker>
                                <span class="time-separator">-</span>
                                <el-date-picker
                                    v-model="formData.endTime"
                                    type="date"
                                    :placeholder="field.endPlaceholder || '结束日期'"
                                    class="custom-date-picker"
                                    value-format="YYYY-MM-DD"
                                ></el-date-picker>
                            </div>

                            <!-- 空白项 -->
                            <div v-else-if="field.empty"></div>
                        </el-form-item>
                    </template>
                </div>
            </el-form>

            <div class="form-actions">
                <el-button class="cancel-btn" @click="handleCancel">{{ config.cancelText || '取消' }}</el-button>
                <el-button type="primary" class="confirm-btn" @click="handleConfirm">
                    {{ config.confirmText || '确定' }}
                </el-button>
            </div>
        </div>
    </div>
</template>

<script setup name="editCategory">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
import { getDictionary } from '@/api/system/dict';
import {
    listTableBySource,
    selectAppSystem,
    selectDatabaseApi,
    selectDatabaseDetail,
    selectFileSystemList,
} from '@/api/dialogue/data-catalog-util-api';
import { selectServiceList } from '@/api/dialogue/data-service-util-api';

const emit = defineEmits(['handleHide', 'confirm']);

const props = defineProps({
    /**
     * 资源数据
     */
    formData: {
        type: Object,
        default: () => ({}),
    },
    /**
     * 自定义配置
     */
    config: {
        type: Object,
        default: () => ({
            title: '修改资源类别信息',
            labelWidth: '91px',
            cancelText: '取消',
            confirmText: '确定',
            options: {},
        }),
    },
    /**
     * 字段布局配置
     */
    formConfig: {
        type: Array,
        default: null,
    },
});

// 表单引用
const formRef = ref(null);

// 获取右侧面板状态
const { updatePanelTitle, triggerComponentEvent } = useRightPanelStore();
const makeFormData = () => {
    let entity = props.formData;
    formData.category = entity.catalogType;
    formData.appSystem = entity.sourceSystemId;
    formData.sourceDatabase = entity.sourceDatabaseId;
    formData.sourceFileServer = entity.sourceFileServerId;
    formData.sourceFolderPath = entity.sourceFolderPath;
    formData.service = entity.serviceId;
    formData.schemaName = entity.sourceSchema;
    formData.tableName = entity.sourceTableName;
    formData.dataScope = entity.dataRange;
    formData.dataLevel = entity.dataLevel;
    formData.dateRange = entity.timeRange;
    formData.startTime = entity.timeRange[0];
    formData.endTime = entity.timeRange[1];
};

// 表单数
const formData = reactive({});
watch(formData, newVal => {
    // 数据库相关链路监听
    let db = newVal.sourceDatabase;
    if (db && db != '' && db != '数据库名称') {
        // 模式名称配置
        selectDatabaseDetail({ id: db }).then(res => {
            if (res.data.data) {
                let schemaName = '';
                if (res.data.data.type === 0) {
                    formData.schemaName = '无';
                    schemaName = res.data.data.dbName;
                    defaultFormLayout.value[1][1].disabled = true;
                } else {
                    formData.schemaName = res.data.data.username;
                    schemaName = res.data.data.username;
                    defaultFormLayout.value[1][1].disabled = true;
                }
                //表名筛选配置
                let paramTables = {
                    id: db,
                    schemaName: schemaName,
                };
                listTableBySource(paramTables).then(res => {
                    if (res.data.data) {
                        defaultOptions.tableName = res.data.data.map(item => ({
                            label: item.tableComment,
                            value: item.tableName,
                        }));
                    }
                });
            }
        });
    }

    // 目录类别监听
    let catalogType = newVal.category;
    if (catalogType === 2) {
        defaultFormLayout.value = [
            [
                { key: 'category', label: '目录分类', type: 'select', required: true, isShow: true },
                { key: 'appSystem', label: '应用系统', type: 'select', required: true, isShow: true },
            ],
            [
                { key: 'sourceFileServer', label: '文件服务器', type: 'select', required: true, isShow: true },
                { key: 'sourceFolderPath', label: '文件夹路径', type: 'input', required: false, isShow: true },
            ],
        ];
    } else if (catalogType === 3) {
        defaultFormLayout.value = [
            [
                { key: 'category', label: '目录分类', type: 'select', required: true, isShow: true },
                { key: 'appSystem', label: '应用系统', type: 'select', required: true, isShow: true },
            ],
            [{ key: 'service', label: '接口服务', type: 'select', required: true, isShow: true }],
        ];
    } else {
        defaultFormLayout.value = [
            [
                { key: 'category', label: '目录分类', type: 'select', required: true, isShow: true },
                { key: 'appSystem', label: '应用系统', type: 'select', required: true, isShow: true },
            ],
            [
                { key: 'sourceDatabase', label: '来源数据库', type: 'select', required: true, isShow: true },
                { key: 'schemaName', label: '模式名称', type: 'input', required: false, isShow: true },
            ],
            [
                { key: 'tableName', label: '表名', type: 'select', required: true, isShow: true },
                { key: 'dataScope', label: '数据范围', type: 'select', required: true, isShow: true },
            ],
            [
                { key: 'dataLevel', label: '数据分级', type: 'select', required: true, isShow: true },
                { key: 'dateRange', label: '时间范围', type: 'dateRange', required: true, isShow: true },
            ],
        ];
    }
});
// 初始化标题
onMounted(() => {
    updatePanelTitle(props.config.title || '修改资源类别信息');
    getDictionary({ code: 'catalog_type' }).then(res => {
        let data = res.data.data;
        defaultOptions.category = formatDictData(data);
    });
    getDictionary({ code: 'data_level' }).then(res => {
        let data = res.data.data;
        defaultOptions.dataLevel = formatDictData(data);
    });
    getDictionary({ code: 'data_range' }).then(res => {
        let data = res.data.data;
        defaultOptions.dataScope = formatDictData(data);
    });
    selectAppSystem().then(res => {
        let data = res.data.data;
        defaultOptions.appSystem = formatSystem(data);
    });
    selectDatabaseApi().then(res => {
        let data = res.data.data;
        defaultOptions.sourceDatabase = formatSystem(data);
    });
    selectFileSystemList().then(res => {
        let data = res.data.data;
        defaultOptions.sourceFileServer = formatSystem(data);
    });
    selectServiceList().then(res => {
        let data = res.data.data;
        defaultOptions.service = formatSystem(data);
    });
});

function formatDictData(data) {
    data.forEach(item => {
        item.value = Number(item.dictKey);
        item.label = item.dictValue;
    });
    return data;
}

function formatSystem(data) {
    data.forEach(item => {
        item.value = item.id;
        item.label = item.name;
    });
    return data;
}

// 默认表单布局配置
const defaultFormLayout = ref([
    [
        { key: 'category', label: '目录分类', type: 'select', required: true, isShow: true },
        { key: 'appSystem', label: '应用系统', type: 'select', required: true, isShow: true },
    ],
    [
        { key: 'sourceDatabase', label: '来源数据库', type: 'select', required: true, isShow: true },
        { key: 'schemaName', label: '模式名称', type: 'input', required: false, isShow: true },
    ],
    [
        { key: 'tableName', label: '表名', type: 'select', required: true, isShow: true },
        { key: 'dataScope', label: '数据范围', type: 'select', required: false, isShow: true },
    ],
    [
        { key: 'dataLevel', label: '数据分级', type: 'select', required: false, isShow: true },
        { key: 'dateRange', label: '时间范围', type: 'dateRange', required: false, isShow: true },
    ],
]);

// 当前使用的表单布局
const formLayout = computed(() => props.formConfig || defaultFormLayout.value);

// 默认选项配置
const defaultOptions = reactive({
    category: [],
    appSystem: [],
    sourceDatabase: [],
    sourceFileServer: [],
    tableName: [],
    dataScope: [],
    dataLevel: [],
    service: [],
});

// 获取字段选项
const getOptions = key => {
    return props.config.options && props.config.options[key] ? props.config.options[key] : defaultOptions[key] || [];
};

// 获取默认标签
const getDefaultLabel = key => {
    const labelMap = {
        category: '目录分类',
        appSystem: '应用系统',
        sourceDatabase: '来源数据库',
        schemaName: '模式名称',
        tableName: '表名',
        dataScope: '数据范围',
        dataLevel: '数据分级',
        dateRange: '时间范围',
    };
    return labelMap[key] || key;
};

// 初始化表单数据
onMounted(() => {
    makeFormData();
    // 如果有默认值配置，使用默认值
    if (props.config && props.config.defaultValues) {
        Object.entries(props.config.defaultValues).forEach(([key, value]) => {
            if (!formData[key]) {
                formData[key] = value;
            }
        });
    }

    // 初始化日期范围（如果有）
    if (formData.dateRange) {
        const dateRangeParts = formData.dateRange.split('—');
        if (dateRangeParts.length === 2) {
            formData.startTime = dateRangeParts[0];
            formData.endTime = dateRangeParts[1];
        }
    }
});

/**
 * 处理表单提交
 */
const handleConfirm = () => {
    // 构建提交的数据对象
    const submitData = { ...formData };

    // 如果有开始和结束时间，合并为日期范围
    if (formData.startTime && formData.endTime) {
        submitData.dateRange = [formData.startTime, formData.endTime];
    }

    // 根据表单字段映射转换数据
    if (props.config.fieldMapping) {
        Object.entries(props.config.fieldMapping).forEach(([sourceKey, targetKey]) => {
            if (submitData[sourceKey] !== undefined) {
                submitData[targetKey] = submitData[sourceKey];
            }
        });
    }

    // 提交数据
    emit('confirm', submitData);
    console.log('formData', submitData);

    // 尝试触发面板组件事件
    const eventTriggered = triggerComponentEvent('confirm', submitData);

    // ElMessage.success(props.config.successMessage || '资源类别信息修改成功');
    emit('handleHide');
};

/**
 * 取消操作
 */
const handleCancel = () => {
    emit('handleHide');
    triggerComponentEvent('cancel');
};
</script>

<style lang="scss" scoped>
:deep(.el-input__wrapper) {
    border-radius: 6px;
    background: #f4f9fe;
}

.edit-category-container {
    padding: 20px;
    height: 100%;
    overflow: auto;

    .form-container {
        max-width: 900px;
        margin: 0 auto;

        .custom-form {
            .form-row {
                display: flex;
                margin-bottom: 5px;
                gap: 30px;

                .el-form-item {
                    flex: 1;
                    margin-bottom: 15px;
                    min-width: 0; /* 防止内容溢出 */
                }

                .empty-item {
                    // 空的表单项，用于保持两列布局
                }
            }

            :deep(.el-form-item) {
                .el-form-item__label {
                    color: #606266;
                    font-size: 14px;
                    text-align: right;
                    margin-right: 8px;
                    padding: 0;
                    white-space: nowrap; /* 防止标签换行 */

                    &::before {
                        color: #f56c6c;
                        margin-right: 4px;
                    }
                }

                .el-form-item__content {
                    width: 100%;
                }
            }

            :deep(.custom-select),
            :deep(.custom-input) {
                width: 100%;
                border-radius: 4px;

                .el-input__wrapper {
                    box-shadow: 0 0 0 1px #dcdfe6 inset;
                }

                // 下拉箭头样式
                .el-input__suffix {
                    color: #909399;
                }
            }

            .time-range-container {
                display: flex;
                align-items: center;

                .time-separator {
                    padding: 0 5px;
                    color: #606266;
                }

                :deep(.custom-date-picker) {
                    flex: 1;
                    width: calc(50% - 10px);
                    border-radius: 4px;

                    .el-input__wrapper {
                        box-shadow: 0 0 0 1px #dcdfe6 inset;
                    }
                }
            }
        }

        .form-actions {
            margin-top: 30px;
            text-align: center;
            display: flex;
            justify-content: center;
            gap: 20px;

            .cancel-btn,
            .confirm-btn {
                min-width: 80px;
                padding: 8px 20px;
                font-size: 14px;
                border-radius: 4px;
            }

            .cancel-btn {
                background: $bg;
                border-color: #dcdfe6;
                color: #606266;
            }

            .confirm-btn {
                background-color: #1890ff;
                border-color: #1890ff;
            }
        }
    }
}
</style>

<template>
    <div class="task_dialog">
        <div class="task_dialog_title">
            <span>详细配置目录信息</span>
            <p>
                <img :src="publicPath + `/static/dialogue/dialogicon1.png`" title="复制" />
                <img :src="publicPath + `/static/dialogue/dialogicon2.png`" title="分享" />
                <i>|</i>
                <img :src="publicPath + `/static/dialogue/dialogicon3.png`" title="关闭" @click="handleClose" />
            </p>
        </div>
        <div class="task_dialog_content">
            <el-form :model="form" ref="formRef" class="my_form_edit" label-width="100px">
                <el-form-item label="关联需求:" prop="relatedDemand">
                    <el-input v-model="form.relatedDemand" placeholder="为企业查找可申报的政策表"></el-input>
                </el-form-item>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="任务名称:" prop="taskName">
                            <el-input v-model="form.taskName" placeholder="政策发布数据采集任务"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="优先级:" prop="priority">
                            <el-select v-model="form.priority" placeholder="高" style="width: 100%">
                                <el-option label="高" value="high"></el-option>
                                <el-option label="中" value="medium"></el-option>
                                <el-option label="低" value="low"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="执行方式:" prop="executionMethod">
                            <el-select v-model="form.executionMethod" placeholder="定时执行" style="width: 100%">
                                <el-option label="定时执行" value="scheduled"></el-option>
                                <el-option label="手动执行" value="manual"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="周期选择:" prop="cycle">
                            <el-input v-model="form.cycle" placeholder="0201-02562"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="数据源:" prop="dataSource">
                            <el-select v-model="form.dataSource" placeholder="政企服务平台" style="width: 100%">
                                <el-option label="政企服务平台" value="government_platform"></el-option>
                                <el-option label="其他来源" value="other"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="目标数据库:" prop="targetDatabase">
                            <el-select
                                v-model="form.targetDatabase"
                                placeholder="政策推演数仓ods层"
                                style="width: 100%"
                            >
                                <el-option label="政策推演数仓ods层" value="policy_ods"></el-option>
                                <el-option label="其他数据库" value="other_db"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item label="属性分类:" prop="targetDatabase">
                    <el-radio-group v-model="form.resource">
                        <el-radio label="基础信息资源" />
                        <el-radio label="主题信息资源" />
                        <el-radio label="部门信息资源" />
                    </el-radio-group>
                </el-form-item>

                <el-form-item label="摘要:" prop="targetDatabase">
                    <el-input type="textarea" v-model="form.cycle" placeholder="请输入摘要" rows="6"></el-input>
                </el-form-item>

                <!-- 操作按钮区域 -->
                <div class="task_thinking_btn">
                    <a class="btn0" @click="resetForm">取消</a>
                    <!-- <a class="btn1">修改</a> -->
                    <a class="btn2" @click.once="onSubmit">确认</a>
                </div>
            </el-form>
        </div>
    </div>
</template>

<script>
import { publicPath } from '@/components/dialogue/store/main.js';

export default {
    name: 'resourceCatalog',
    components: {},
    data() {
        return {
            showTaskDialog: true, // 显示窗口
            form: {
                relatedDemand: '为企业查找可申报的政策表',
                taskName: '政策发布数据采集任务',
                executionMethod: '定时执行',
                priority: '高',
                dataSource: '政企服务平台',
                cycle: '内容',
                targetDatabase: '政策推演数仓ods层',
                hjfs: '全量',
                mbb: '贴源层-政策原文表(sq_policy)',
                searchText: '',
            },
            formRef: null,
        };
    },
    methods: {
        //确认
        onSubmit() {
            if (this.$refs.formRef) {
                this.$refs.formRef.validate(function (valid) {
                    if (valid) {
                        alert('提交成功');
                    } else {
                        alert('表单验证失败');
                        return false;
                    }
                });
            }
        },
        //取消
        resetForm() {
            if (this.$refs.formRef) {
                this.$refs.formRef.resetFields();
            }
            this.$emit('handleHide', false);
        },
        //关闭
        handleClose() {
            this.$emit('handleHide', false);
        },
    },
};
</script>

<style lang="scss" scoped></style>

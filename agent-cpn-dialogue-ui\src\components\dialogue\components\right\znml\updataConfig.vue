<template>
    <div class="task_dialog">
        <!--        <div class="task_dialog_title">-->
        <!--            <span>更新配置</span>-->
        <!--            <p>-->
        <!--                <img src="/dialogue/dialogicon1.png" title="复制" />-->
        <!--                <img src="/dialogue/dialogicon2.png" title="分享" />-->
        <!--                <i>|</i>-->
        <!--                <img src="/dialogue/dialogicon3.png" title="关闭" @click="handleClose" />-->
        <!--            </p>-->
        <!--        </div>-->
        <div class="task_dialog_content" style="margin-top: 30px">
            <el-form :model="form" ref="formRef" class="my_form_edit" label-width="160px">
                <!-- ------------------------更新配置-------------------------- -->

                <el-form-item label="大数据抽取方式：" prop="dsjcqfs">
                    <el-select v-model="form.dsjcqfs" placeholder="请选择" style="width: 100%">
                        <el-option
                            v-for="item in option.dsjcqfsOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        />
                    </el-select>
                </el-form-item>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="业务更新时间字段：" prop="ywgxsjzd">
                            <el-select v-model="form.ywgxsjzd" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in option.fieldOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="增量抽取字段：" prop="zlcqzd">
                            <el-select v-model="form.zlcqzd" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in option.fieldOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <h3 class="task_dialog_block_title"><span>信息项数据来源</span></h3>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="是否为主数据：" prop="sfwzsj">
                            <el-select v-model="form.sfwzsj" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in option.trueFalse"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否本系统产生数据：" prop="sfbxtcssj">
                            <el-select v-model="form.sfbxtcssj" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in option.trueFalse"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="是否外部订阅数据：" prop="sfwbdysj">
                            <el-select v-model="form.sfwbdysj" placeholder="请选择" style="width: 100%">
                                <el-option
                                    v-for="item in option.trueFalse"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="订阅来源单位：" prop="dylydy">
                            <el-input v-model="form.dylydy" placeholder="请输入关键字"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="订阅来源系统：" prop="dylyxt">
                            <el-input v-model="form.dylyxt" placeholder="请输入关键字"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="订阅来源表名称：" prop="dylybmc">
                            <el-input v-model="form.dylybmc" placeholder="请输入关键字"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <h3 class="task_dialog_block_title">
                    <span>指标信息</span>
                    <!--                    <a>+新增</a>-->
                </h3>

                <el-table :data="tableData" style="width: calc(100% - 20px)" max-height="300">
                    <el-table-column prop="name" label="名称" width="150px" />
                    <el-table-column prop="itemEngName" label="英文名称" width="120px" />
                    <el-table-column prop="dataFormat" label="数据格式" />
                    <el-table-column prop="dataLength" label="长度" />
                    <el-table-column prop="isPk" label="是否主键" />
                    <el-table-column prop="isNull" label="是否为空" />
                    <!--                <el-table-column fixed="right" label="操作">-->
                    <!--                  <template #default="scope">-->
                    <!--                    <el-button-->
                    <!--                        link-->
                    <!--                        type="primary"-->
                    <!--                        size="small"-->
                    <!--                        @click.prevent="editRow(scope.row)"-->
                    <!--                    >-->
                    <!--                      编辑-->
                    <!--                    </el-button>-->
                    <!--                    <el-button-->
                    <!--                        link-->
                    <!--                        type="primary"-->
                    <!--                        size="small"-->
                    <!--                        @click.prevent="deleteRow(scope.$index)"-->
                    <!--                    >-->
                    <!--                      删除-->
                    <!--                    </el-button>-->
                    <!--                  </template>-->
                    <!--                </el-table-column>-->
                </el-table>
                <!--              <el-button class="mt-4" style="width:  calc(100% - 20px);margin-top: 30px" @click="onAddItem">-->
                <!--                新增指标-->
                <!--              </el-button>-->

                <!-- 操作按钮区域 -->
                <div class="task_thinking_btn">
                    <a class="btn0" @click="resetForm">取消</a>
                    <a class="btn2" @click.once="onSubmit">确认</a>
                </div>
            </el-form>
        </div>
    </div>
</template>

<script setup name="updataConfig">
import { ref, computed, onMounted, reactive } from 'vue';
import { getDictionary } from '@/api/system/dict';

const props = defineProps({
    /**
     * 组件标题
     */
    title: {
        type: String,
        default: '详细配置信息',
    },
    /**
     * 资源数据
     */
    resourceData: {
        type: Object,
        default: () => ({}),
    },

    formData: {
        type: Object,
        default: () => ({}),
    },
});

function formatDictData(data) {
    data.forEach(item => {
        item.value = Number(item.dictKey);
        item.label = item.dictValue;
    });
    return data;
}
function formatFieldDictData(data) {
    let dict = [];
    data.forEach(item => {
        item.value = item.itemEngName;
        item.label = item.name + '[' + item.itemEngName + ']';
        dict.push(item);
    });
    option.fieldOptions = dict;
}
function makeItems() {
    let items = props.formData.resItems;
    tableData.value = [];
    items.forEach(item => {
        tableData.value.push({
            name: item.name,
            itemEngName: item.itemEngName,
            dataFormat: item.dataFormat,
            dataLength: item.dataLength,
            isPk: item.isPk === 1 ? '是' : '否',
            isNull: item.isNull === 1 ? '是' : '否',
        });
    });
}

const emit = defineEmits(['handleHide', 'confirm']);
onMounted(() => {
    getDictionary({ code: 'get_way' }).then(res => {
        let data = res.data.data;
        option.dsjcqfsOptions = formatDictData(data);
    });
    formatFieldDictData(props.formData.resItems);
    makeItems();
});

// 目录基本信息配置工具
const option = reactive({
    dsjcqfsOptions: [],
    fieldOptions: [],
    trueFalse: [
        { value: 1, label: '是' },
        { value: 0, label: '否' },
    ],
});

const handleSubmit = async () => {
    console.log('submit');
};
const resetForm = () => {
    emit('handleHide');
};

const onSubmit = () => {
    emit('confirm', form);
    emit('handleHide');
};

const tableData = ref([]);
const now = new Date();
const deleteRow = index => {
    tableData.value.splice(index, 1);
};
const editRow = index => {};

const onAddItem = () => {};

const form = reactive({
    dsjcqfs: 1, // 大数据抽取方式
    ywgxsjzd: '', // 业务更新时间字段
    zlcqzd: '', // 增量抽取字段
    sfwzsj: 0, // 是否为主数据
    sfbxtcssj: 1, // 是否本系统产生数据
    sfwbdysj: 0, // 是否外部订阅数据
    dylydy: '', // 订阅来源单位
    dylyxt: '', // 订阅来源系统
    dylybmc: '', // 订阅来源表名称
});
</script>

<style lang="scss" scoped></style>

<script setup>
const props = defineProps({
    /**
     * 思考内容
     */
    thinkingContent: {
        type: [String, Object],
        default: '',
    },
    /**
     * 思考过程状态：0-正在思考, 1-已完成思考
     */
    processState: {
        type: [Number, Object],
        default: 0,
    },
});
const statusValue = computed(() => {
    return props.processState === 0 ? 'thinking' : 'end';
});
const senderValue = ref(false);
</script>

<template>
    <Thinking
        :status="statusValue"
        v-model="senderValue"
        :content="thinkingContent"
        button-width="250px"
        max-width="100%"
    >
        <template #status-icon="{ status }">
            <Icon name="rock" v-if="status === 'end'" />
            <div v-if="status === 'thinking'" class="thinking-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </template>

        <template #label="{ status }">
            <span v-if="status === 'thinking'">思考中</span>
            <span v-if="status === 'end'">已完成思考</span>
        </template>

        <template #arrow>
            <el-icon><ArrowDown /></el-icon>
        </template>
    </Thinking>
</template>

<style scoped lang="scss">
.thinking-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;

    span {
        display: inline-block;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: #409eff;
        animation: thinking 1.4s infinite ease-in-out both;

        &:nth-child(1) {
            animation-delay: -0.32s;
        }

        &:nth-child(2) {
            animation-delay: -0.16s;
        }
    }
}

@keyframes thinking {
    0%,
    80%,
    100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}
</style>

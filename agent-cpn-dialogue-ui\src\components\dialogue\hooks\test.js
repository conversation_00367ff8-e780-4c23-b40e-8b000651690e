import { stepProps } from 'element-plus';

// 延时函数，返回Promise
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

const cjTestData = async () => {
    addSystemCom({
        container: 'cj',
        components: [
            {
                name: 'thinkingProcess',
                props: {
                    processState: 1,
                    thinkingContent: `
### （二）针对学生个体
1. 对于成绩较低的学生，如王五，制定个性化的学习计划，加强基础知识辅导和学习方法指导。
2. 对于成绩较高的学生，如刘七，鼓励其参加学科竞赛和拓展学习，进一步提升成绩。
3. 对于成绩较为均衡的学生，如张三和赵六，鼓励其在保持优势的同时，加强薄弱环节的学习，争取更优异的成绩。

你可以将上述内容复制到Markdown编辑器中进行排版，或者根据自己的需求进行修改和调整。希望这个示例对你有所帮助。`,
                },
            },
        ],
    });

    await delay(1000);

    addSystemCom({
        container: 'cj',
        components: [
            {
                name: 'thinkingResult',
                props: {
                    data: {
                        message: '解析采集任务数据源完成',
                        stepName: 'systemInfo',
                    },
                },
            },
        ],
    });

    await delay(1000);

    addSystemCom({
        container: 'cj',
        components: [
            {
                name: 'thinkingResult',
                props: {
                    data: {
                        message: '解析到采集任务数据表完成',
                        stepName: 'systemInfo',
                    },
                },
            },
        ],
    });

    await delay(1000);

    addSystemCom({
        container: 'cj',
        // 采集信息查询工具
        components: [
            {
                name: 'createWorkFlow',
            },
        ],
    });

    await delay(1000);

    addSystemCom({
        container: 'cj',
        components: [
            {
                name: 'thinkingResult',
                props: {
                    data: {
                        message: '调用数据表查询工具',
                        stepName: 'systemInfo',
                    },
                },
            },
        ],
    });

    await delay(1000);

    addSystemCom({
        container: 'cj',
        // 采集信息查询工具
        components: [
            {
                name: 'cjInfoTool',
            },
        ],
    });

    await delay(1000);

    addSystemCom({
        container: 'cj',
        components: [
            {
                name: 'thinkingResult',
                props: {
                    data: {
                        message: '生成数据采集任务完成',
                        stepName: 'systemInfo',
                    },
                },
            },
        ],
    });

    await delay(1000);

    addSystemCom({
        container: 'cj',
        // 采集信息查询工具
        components: [
            {
                name: 'cjGenerateTool',
            },
        ],
    });
};
const TestData = async () => {
    // await delay(1000);
    addSystemCom({
        container: 'sbsjsc',
        components: [
            {
                name: 'thinkingResult',
                props: {
                    data: {
                        message: '解析数据指标查询信息完成',
                    },
                },
            },
        ],
    });

    addSystemCom({
        container: 'sbsjsc',
        components: [
            {
                name: 'IndicatorKanban',
            },
        ],
        answerProps: {
            showProblemRecommend: true,
            showFeedback: true,
        },
    });
};
const mbwsTest = async () => {
    const firstID = 'zbcx';

    // addSystemCom({
    //     container: firstID,
    //     components: [
    //         {
    //             name: 'thinkingProcess',
    //             props: {
    //                 processState: 1,
    //                 thinkingContent: '啊实打实大萨达花见花开卡萨达',
    //             },
    //         },
    //     ],
    // });

    // addSystemCom({
    //     container: firstID,
    //     components: [
    //         {
    //             name: 'createWorkFlow',
    //             props: {
    //                 data: {
    //                     intent: '数据查询',
    //                     target: '指标查询工具',
    //                     source: '数据来源',
    //                     flow: ['指标信息工具查询', '指标数据查询结果', '问题推荐'],
    //                 },
    //             },
    //         },
    //     ],
    // });
    // addSystemCom({
    //     container: firstID,
    //     components: [
    //         {
    //             name: 'thinkingResult',
    //             props: {
    //                 data: {
    //                     message: '解析数据指标查询信息完成',
    //                 },
    //             },
    //         },
    //     ],
    // });
    addSystemCom({
        container: firstID,
        components: [
            {
                name: 'IndicatorSearch',
            },
        ],
    });
    addSystemCom({
        container: firstID,
        components: [
            {
                name: 'IndicatorData',
            },
        ],
    });
};
const zbcxTest = async () => {
    const firstID = 'zbcx';
    addSystemCom({
        container: firstID,
        components: [
            {
                name: 'thinkingProcess',
                props: {
                    processState: 1,
                    thinkingContent: '啊实打实大萨达花见花开卡萨达',
                },
            },
        ],
    });
    addSystemCom({
        container: firstID,
        components: [
            {
                name: 'createWorkFlow',
                props: {
                    data: {
                        intent: '数据查询',
                        target: '指标查询工具',
                        source: '数据来源',
                        flow: ['指标信息工具查询', '指标数据查询结果', '问题推荐'],
                    },
                },
            },
        ],
    });
    addSystemCom({
        container: firstID,
        components: [
            {
                name: 'thinkingResult',
                props: {
                    data: {
                        message: '解析数据指标查询基本信息完成',
                        stepName: 'systemInfo',
                    },
                },
            },
        ],
    });
    addSystemCom({
        container: firstID,
        components: [
            {
                name: 'IndicatorQuery',
            },
        ],
    });
};

const bgdsTest = async () => {
    addSystemCom({
        container: 'test',
        components: [
            {
                name: 'createWorkFlow',
                props: {
                    data: {
                        intent: '数据查询',
                        target: '表格导数工具',
                        flow: ['表格指标信息查询', '表格问数查询结果', '问题推荐'],
                    },
                },
            },
        ],
    });
    addSystemCom({
        container: 'test',
        components: [
            {
                name: 'thinkingResult',
            },
        ],
    });
    addSystemCom({
        container: 'test',
        components: [
            {
                name: 'FilesExport',
                props: {
                    isShowPreview: true,
                },
            },
        ],
        answerProps: {
            showProblemRecommend: true,
            showFeedback: true,
        },
    });
    // addSystemCom({
    //     container: 'test',
    //     components: [
    //         {
    //             name: 'TablePreview',
    //         },
    //     ],
    // });
};
const bgdwsest = async () => {
    addSystemCom({
        container: 'test',
        components: [
            {
                name: 'thinkingContentStep',
                props: {
                    stepTitle: '报告问0数结果',
                    extraText: '啊啊啊啊 ',
                },
            },
        ],
    });

    addSystemCom({
        container: 'test',
        components: [
            {
                name: 'thinkingContentStep',
                props: {
                    stepTitle: '报告问数2结果',
                    extraText: '啊啊啊啊 ',
                    data: {
                        stepTitle: '报告问数结果',
                        extraText: '阿斯顿撒旦撒 ',
                    },
                },
            },
        ],
    });
    addSystemCom({
        container: 'test',
        components: [
            {
                name: 'FilesExport',
                props: {
                    stepTitle: '报告问数结果',
                    lastStep: true,
                },
            },
        ],
    });
};
export { TestData, cjTestData, zbcxTest, mbwsTest, bgdsTest, bgdwsest };

import {
    senderValue,
    showHeaderFlog,
    isDeepThink,
    isShowSimpleTree,
    isShowFileList,
    hideSelectModle,
    defaultWord,
    currentBusinessType,
    content,
    currentBusinessTypeName,
    form,
    senderRef,
    DEFAULT_PROMPT,
    DEFAULT_BUSINESS_TYPE,
    headerType,
    files,
    isDisabled,
} from '@/components/dialogue/store/input';
import { defineStore } from 'pinia';
import { computed, watch } from 'vue';

export const useDialogInput = defineStore('dialogInput', () => {
    /**
     * @description 获取字段的安全值，避免显示 undefined
     * @param {any} value - 字段值
     * @param {string} defaultValue - 默认值
     * @returns {string} 安全的值
     */
    const getSafeValue = (value, defaultValue = '') => {
        if (value === undefined || value === null || value === '') {
            return defaultValue;
        }
        return value;
    };

    /**
     * @description 根据当前业务类型构建提示词内容（全量模板化）
     * @returns {string} 构建好的提示词内容
     */
    const templates = {
        bm1: `帮我生成一个【{resourceTitle}】的资源目录，更新周期为【{updateFrequency}】，属性分类为【{resourceCategory}】{baseCategory}{topicCategory}，部门为【{department}】，开放类型为【{openType}】，共享类型为【{shareType}】{shareCondition}{shareBasis}{shareMethod}{shareScope}{shareReason}。`,
        bm2: `帮我将【{fileName}】这个数据一本帐文件生成一个【{resourceTitle}】的资源目录。`,
        bm3: `帮我将【{fileName}】这个非结构化数据文件生成一个【{resourceTitle}】的资源目录，更新周期为【{updateFrequency}】，属性分类为【{resourceCategory}】，基础分类为【{baseCategory}】，主题分类为【{topicCategory}】，部门为【{department}】，开放类型为【{openType}】，共享类型为【{shareType}】。`,
        cj1: `帮我将【{sourceDatabase}】【{sourceTable}】的数据以{aggregationText}，汇聚到【{targetDatabase}】【{targetTable}】`,
        cj2: `帮我将【{fileName}】这个数据表采集到【{targetDataSource}】【{targetDatabase}】【{targetTable}】表里。`,
        cj3: `帮我生成一个【{resourceTitle}】的目录，更新周期为【{updateFrequency}】，属性分类为【{resourceCategory}】，基础分类为【{baseCategory}】，主题分类为【{topicCategory}】，部门为【{department}】，开放类型为【{openType}】，共享类型为【{shareType}】，共享条件为【{shareCondition}】，共享依据为【{shareBasis}】，共享方式为【{shareMethod}】，共享范围为【{shareScope}】。`,
        数据共享: `帮我根据需求生成接口，提供数据共享服务。`,
        数据画像: `帮我生成数据画像，查看企业总体情况。`,
        数据打标: `帮我生成标签，为想要的数据打标。`,
        数据治理: `帮我处理与加工数据，提高数据质量。`,
    };

    function buildPromptMessage() {
        const type = currentBusinessType.value;
        const t = templates[type];
        if (!t) return senderValue.value;

        // 特殊字段拼接
        let aggregationText = '';
        if (type === 'cj1') {
            if (form.aggregationType === '增量') {
                aggregationText = `【${getSafeValue(form.aggregationType, '增量')}】的汇聚方式`;
                if (form.incrementMechanism) {
                    aggregationText += `，【${form.incrementMechanism}】的增量机制`;
                    if (form.sourceIdentifier) {
                        aggregationText += `，【${form.sourceIdentifier}】的源表标识字段`;
                    }
                    if (form.incrementMechanism === '按标识增量' && form.pendingIdentifier) {
                        aggregationText += `，【${form.pendingIdentifier}】的待汇聚标识，【${form.aggregatedIdentifier}】的已汇聚标识`;
                    }
                }
            } else {
                aggregationText = `【${getSafeValue(form.aggregationType, '全量')}】的汇聚方式`;
            }
        }

        // 组装可选字段
        const data = {
            ...form,
            baseCategory: form.baseCategory ? `，基础分类为【${form.baseCategory}】` : '',
            topicCategory: form.topicCategory ? `，主题分类为【${form.topicCategory}】` : '',
            shareCondition:
                form.shareType === '有条件共享' && form.shareCondition ? `，共享条件为【${form.shareCondition}】` : '',
            shareBasis: form.shareType === '有条件共享' && form.shareBasis ? `，共享依据为【${form.shareBasis}】` : '',
            shareMethod:
                ['有条件共享', '无条件共享'].includes(form.shareType) && form.shareMethod
                    ? `，共享方式为【${form.shareMethod}】`
                    : '',
            shareScope:
                ['有条件共享', '无条件共享'].includes(form.shareType) && form.shareScope
                    ? `，共享范围为【${form.shareScope}】`
                    : '',
            shareReason: form.shareType === '不共享' && form.shareReason ? `，不共享理由为【${form.shareReason}】` : '',
            aggregationText,
            sourceDatabase: getSafeValue(form.sourceDatabase, '数据源'),
            sourceTable: getSafeValue(form.sourceTable, '数据表'),
            targetDatabase: getSafeValue(form.targetDatabase, '目标数据源'),
            targetTable: getSafeValue(form.targetTable, '目标数据表'),
            fileName: getSafeValue(form.fileName, '/上传文件'),
            targetDataSource: getSafeValue(form.targetDataSource, '目标数据源'),
            resourceTitle: getSafeValue(form.resourceTitle, '资源标题'),
            updateFrequency: getSafeValue(form.updateFrequency, '周期'),
            resourceCategory: getSafeValue(form.resourceCategory, '类别名'),
            department: getSafeValue(form.department, '部门名'),
            openType: getSafeValue(form.openType, '类型名'),
            shareType: getSafeValue(form.shareType, '有条件共享'),
        };

        const message = t.replace(/\{(\w+)\}/g, (_, key) => data[key] ?? '');
        senderRef.value.focus('end');
        // 移除强制设置isDisabled为false的逻辑，让禁用状态由调用方控制
        // isDisabled.value = false;
        return message;
    }

    /**
     * @description 响应式的提示词内容，会根据表单数据变化自动更新
     */
    const promptMessage = computed(() => {
        return buildPromptMessage();
    });

    watch(
        () => form,
        newFormData => {
            // 当表单数据变化时，更新发送框的内容
            if (currentBusinessType.value && currentBusinessType.value !== DEFAULT_BUSINESS_TYPE) {
                senderValue.value = buildPromptMessage();
            }
        },
        { deep: true }
    );

    /**
     * @description 更新对话框内容
     */
    const updateDialogContent = () => {
        if (currentBusinessType.value && currentBusinessType.value !== DEFAULT_BUSINESS_TYPE) {
            senderValue.value = buildPromptMessage();
        }
    };

    /**
     * @description 更新表单数据
     * @param {Object} newFormData - 新的表单数据
     */
    const updateFormData = newFormData => {
        // 将表单数据同步到全局 store
        Object.assign(form, newFormData);
    };

    /**
     * @description 关闭头部
     */
    function closeHeader() {
        showHeaderFlog.value = false;
        Object.keys(form).forEach(key => {
            form[key] = '';
        });
        files.value = [];
        hideSelectModle.value = false;
        defaultWord.value = DEFAULT_PROMPT;
        currentBusinessType.value = DEFAULT_BUSINESS_TYPE;
        isShowSimpleTree.value = false;
        isShowFileList.value = false;
        senderRef.value.clear();
    }
    function toggleHeader() {
        showHeaderFlog.value = !showHeaderFlog.value;
    }
    /**
     * @description 关闭头部
     */
    function openHeader() {
        showHeaderFlog.value = true;
        senderRef.openHeader();
    }
    /**
     * @description 在头部区域打开或关闭表单
     */
    function openCloseHeader(type = null) {
        // 如果指定了类型，则设置headerType
        if (type) {
            headerType.value = type;
        }

        showHeaderFlog.value = !showHeaderFlog.value;
    }

    /**
     * @description 初始化输入框内容
     */
    const initInputContent = () => {
        senderRef.value.clear();
        Object.keys(form).forEach(key => {
            form[key] = '';
        });
        files.value = [];
        hideSelectModle.value = false;
        defaultWord.value = DEFAULT_PROMPT;
        currentBusinessType.value = DEFAULT_BUSINESS_TYPE;
        isShowSimpleTree.value = false;
        isShowFileList.value = false;

        // 确保头部也关闭
        if (showHeaderFlog.value) {
            closeHeader();
        }
    };

    /**
     * @description 关闭选择模式
     */
    const handleClose = () => {
        senderValue.value = '';
        content.value = { parent: {}, child: {} };
        hideSelectModle.value = false;
        isShowSimpleTree.value = false;
        isShowFileList.value = false;
        defaultWord.value = DEFAULT_PROMPT;
        currentBusinessType.value = DEFAULT_BUSINESS_TYPE;

        // 确保头部也关闭
        if (showHeaderFlog.value) {
            closeHeader();
        }
    };
    /**
     * @description 处理选择数据
     */
    const getSelectData = data => {
        console.log('🚀 ~ getSelectData ~ data:', data);

        // 关闭所有弹窗
        isShowSimpleTree.value = false;
        isShowFileList.value = false;
        headerType.value = 'template';

        if (!data?.value) {
            console.log('🚀 ~ getSelectData ~ data.value is empty:', data?.value);
            // 如果没有有效数据，重置状态并返回
            senderRef.value.clear();

            hideSelectModle.value = true;

            return;
        }

        // 更新内容状态
        content.value.parent = data.parent || {};
        content.value.child = data.child || {};
        currentBusinessType.value = data.value;
        // 更新业务类型名称
        currentBusinessTypeName.value = `${content.value.parent.name}-${content.value.child.childName || ''}`;
        console.log('🚀 ~ dialogInput.vue:106 ~ currentBusinessType.value:', currentBusinessType.value);
        // 处理从selectOption选择的情况
        if (data.fromSelectOption) {
            // 添加特殊处理逻辑
        }

        // 为所有业务类型生成提示词，而不仅仅是数据采集和数据编目类型
        senderValue.value = buildPromptMessage();

        // 如果是智能问数问模板业务类型，清空输入框
        if (currentBusinessType.value.includes('ws2')) {
            senderRef.value.clear();
            senderValue.value = '/';
            isShowFileList.value = true;
        }
        if (currentBusinessType.value.includes('ws1')) {
            senderRef.value.clear();
            isShowFileList.value = true;
            defaultWord.value = '请输入您想要问的指标';
        }

        // 使用nextTick确保DOM更新后再聚焦
        nextTick(() => {
            senderRef.value.focus('end');
        });

        hideSelectModle.value = true;
        console.log('hideSelectModle', hideSelectModle.value, 'showHeaderFlog', showHeaderFlog.value);

        // 根据业务类型设置禁用状态
        // 对于需要表单验证的业务类型，初始状态应该是禁用
        if (['cj1', 'cj2', 'bm1', 'bm2', 'bm3'].includes(currentBusinessType.value)) {
            isDisabled.value = true;
        } else {
            isDisabled.value = false;
        }
    };
    const router = useRouter();

    const selectChildItem = (parentItem, parentIndex, childItem, childIndex) => {
        const data = {
            parent: parentItem,
            child: childItem,
            parentIndex: parentIndex,
            childIndex: childIndex,
            value: childItem.value, // 业务类型标识，用于动态加载组件
        };

        if (childItem.agentId) {
            router.push({
                path: '/chat',
                query: {
                    agentId: childItem.agentId,
                },
            });
            return;
        }
        nextTick(() => {
            getSelectData(data);
        });
    };
    return {
        openCloseHeader,
        closeHeader,
        openHeader,
        buildPromptMessage,
        promptMessage,
        updateDialogContent,
        updateFormData,
        handleClose,
        initInputContent,
        getSelectData,
        selectChildItem,
        toggleHeader,
    };
});

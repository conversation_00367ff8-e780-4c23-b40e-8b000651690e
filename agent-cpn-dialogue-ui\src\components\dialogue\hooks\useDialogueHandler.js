import { connect, disconnect, isWebSocketConnected } from '@/utils/websocket';
import { getToken } from '@/utils/auth';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { DialogueService } from '@/components/dialogue/hooks/useSystemResponse';
import { generateDialogueMsg, reply, replySync } from '@/api/dialogue';
import { bgdwsest, mbwsTest } from './test';
// 判断是否是开发环境
import { isFontTest } from '@/components/dialogue/store/main';
import { fileListUrl, isDeepThink, isLoading, hasActiveConnections } from '@/components/dialogue/store/input';
import { useSessionStore } from '@/components/dialogue/store/modules/session';
import { defineStore } from 'pinia';
import { useUserStore } from '@/components/dialogue/store/modules/user';

// 移除全局作用域的sessionStore
// const sessionStore = useSessionStore();

const wsUrl = !isFontTest ? window.location.origin + '/api/data-component-dialogue/ws' : '/';
const replyUrl = '/api/data-component-dialogue/noauth/dialogue/reply';

/**
 * 对话处理钩子
 * 用于处理发送消息和接收响应
 * @param {Object} options 配置选项
 * @param {Object} options.dialogueList ref类型的对话列表
 * @returns {Object} 对话处理相关方法和状态
 */
export const useDialogueHandler = defineStore('dialogueHandler', () => {
    // 在函数内部获取sessionStore，避免循环依赖
    const sessionStore = useSessionStore();

    // 思考过程状态：0-正在思考, 1-已完成思考，以messageId为键的映射
    const processState = ref({});
    // SSE返回的思考过程数据映射，以 messageId 为键
    const sseData = ref({});

    // 存储每个messageId对应的控制器，用于取消请求
    const controllerMap = ref({});
    const userStore = useUserStore();
    const token = computed(() => userStore.token);
    /**
     * 添加文件URL到消息内容
     */
    const appendFileUrlToMessage = option => {
        if (!fileListUrl.value) return option;

        return {
            ...option,
            msg: option.msg + '，附件地址：' + fileListUrl.value,
        };
    };

    /**
     * 发送消息处理函数
     * @param {string} message 用户输入的消息内容
     */
    const sendMessage = async message => {
        if (!token.value) return;
        // 设置加载状态
        isLoading.value = true;

        if (!isFontTest) {
            // 添加用户消息到对话列表
            DialogueService.addUserQuestion(message);
            const v = localStorage.getItem('chatContent');
            // 如果本地有发送消息不在二次请求接口获取对话id
            if (!v) {
                await sessionStore.setDialogIdMessageId(message);
            }

            console.log('userInfo:', userStore.userInfo);
            let userInfo = userStore.userInfo;
            // 准备消息内容
            let option = {
                dialogueId: sessionStore.dialogueId,
                messageId: sessionStore.messageId,
                userId: userInfo.user_id,
                isDeepThinking: 1,
                msg: message,
            };
            // 添加文件URL（如果有）
            option = appendFileUrlToMessage(option);

            // 处理思考过程和WebSocket连接
            handleSSEThinking(option);
            handleWebSocket(option);
            // 清空文件URL
            fileListUrl.value = null;
            // 更新历史记录
            sessionStore.requestSessionList();

            // 设置活跃连接状态
            hasActiveConnections.value = true;
        } else {
            // sessionStore.messageId(121221);
            // sessionStore.dialogueId(1);
            DialogueService.addUserQuestion(message);
            // bgdwsest();
            // bgdsTest();
            mbwsTest();
        }
    };
    const setProcessError = messageId => {
        processState.value = { ...processState.value, [messageId]: 2 };
    };
    /**
     * 处理WebSocket连接
     * @param {string} message 消息内容
     * @param {string} containerId 容器ID
     */
    const handleWebSocket = async option => {
        connect(wsUrl, option, wsMsg => {
            if (wsMsg.includes('工具执行完毕')) {
                disconnectAllConnections();
                return;
            }

            try {
                const wsData = typeof wsMsg === 'string' ? JSON.parse(wsMsg) : wsMsg;
                console.log('🚀 ~ useDialogueHandler.js:106 ~ useDialogueHandler ~ wsData:', wsData);

                // 如果是call则关闭连接
                if (wsData && wsData.result && wsData.result.end === 'call') {
                    disconnectAllConnections();
                    return;
                }

                const { messageId, stepName, showProblemRecommend, showFeedback } = wsData;

                addSystemCom({
                    container: messageId,
                    components: [
                        {
                            name: stepName,
                            props: { data: wsData },
                        },
                    ],
                    answerProps: {
                        showProblemRecommend: !!showProblemRecommend,
                        showFeedback: !!showFeedback,
                    },
                });
            } catch (error) {
                console.error('解析WebSocket消息失败:', error, wsMsg);
            }
        });
    };

    /**
     * @description 优化后的SSE流式思考过程处理函数，支持中断、状态管理与组件复用
     * @param {object} option 用户消息内容
     */
    const handleSSEThinking = async option => {
        const messageId = option.messageId;
        if (!messageId) {
            console.log('未找到对话ID，不建立SSE连接');
            return;
        }

        // 终止已有的SSE请求
        const prevController = controllerMap.value[messageId];
        if (prevController) {
            prevController.abort();
        }

        // 初始化思考状态与内容
        processState.value = { ...processState.value, [messageId]: 0 };
        sseData.value = { ...sseData.value, [messageId]: '' };

        // 添加思考过程组件
        addSystemCom({
            container: messageId,
            components: [
                {
                    name: 'thinkingProcess',
                    props: {
                        processState: computed(() => processState.value[messageId]),
                        thinkingContent: computed(() => sseData.value[messageId]),
                        isDeepThink: isDeepThink.value,
                    },
                },
            ],
        });

        // 创建新的AbortController
        const controller = new AbortController();
        controllerMap.value[messageId] = controller;

        try {
            await fetchEventSource(replyUrl, {
                method: 'post',
                headers: {
                    'Ark-Auth': `bearer ${getToken()}`,
                    Accept: 'text/event-stream;charset=UTF-8',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(option),
                openWhenHidden: true,
                withCredentials: true,
                signal: controller.signal,
                onopen: async response => {
                    if (!response.ok || !response.headers.get('content-type')?.includes('text/event-stream')) {
                        console.log('SSE错误:', response.status, response.statusText);
                    }
                },
                onmessage: event => {
                    // 只处理当前控制器对应的消息
                    if (controllerMap.value[messageId] !== controller) return;

                    // 保持思考状态为0（思考中）
                    processState.value = { ...processState.value, [messageId]: 0 };

                    // 累积思考过程数据
                    sseData.value = {
                        ...sseData.value,
                        [messageId]: (sseData.value[messageId] || '') + event.data,
                    };
                },
                onclose: () => {
                    if (controllerMap.value[messageId] !== controller) return;

                    // 更新状态为1（思考完成）
                    processState.value = { ...processState.value, [messageId]: 1 };

                    // 清理控制器
                    delete controllerMap.value[messageId];

                    // 添加结果组件
                    addSystemCom({
                        container: messageId,
                        components: [
                            {
                                name: 'catalogStructuredData',
                                props: {
                                    title: '基于您的需求生成的目录',
                                },
                            },
                        ],
                    });

                    // 检查是否所有连接都已关闭
                    checkConnections();
                },
                onerror: err => {
                    console.error('SSE 错误:', err);

                    // 清理控制器
                    if (controllerMap.value[messageId] === controller) {
                        delete controllerMap.value[messageId];
                    }
                    controller.abort();

                    // 检查是否所有连接都已关闭
                    checkConnections();
                },
            });
        } catch (error) {
            console.error('SSE连接异常:', error);
            if (controllerMap.value[messageId] === controller) {
                delete controllerMap.value[messageId];
            }
            controller.abort();

            // 检查是否所有连接都已关闭
            checkConnections();
        }
    };

    /**
     * 检查是否还有活跃连接
     */
    const checkConnections = () => {
        // 检查是否有活跃的SSE连接
        const hasActiveSSE = Object.keys(controllerMap.value).length > 0;
        // 检查是否有活跃的WebSocket连接
        const hasActiveWS = isWebSocketConnected();

        // 更新状态
        hasActiveConnections.value = hasActiveSSE || hasActiveWS;

        // 如果没有活跃连接了，清除加载状态
        if (!hasActiveConnections.value) {
            isLoading.value = false;
        }
    };

    /**
     * 断开所有连接
     */
    const disconnectAllConnections = () => {
        // 断开WebSocket连接
        disconnect();
        setProcessError(sessionStore.messageId);
        // 中止所有SSE请求
        Object.values(controllerMap.value).forEach(controller => {
            try {
                controller.abort();
            } catch (error) {
                console.error('中止SSE请求失败:', error);
            }
        });
        // processState 设为 2（已停止思考）
        // processState.value[sessionStore.messageId] = 2;
        // 清空控制器映射
        controllerMap.value = {};

        // 重置状态
        isLoading.value = false;
        hasActiveConnections.value = false;
    };

    /**
     * 处理动作回复
     * @param {string} message 用户消息
     * @param {string} messageId 消息容器ID
     */
    const handleActionReply = option => {
        replySendWs(option);
        generateDialogueMsg(option).then(res => {
            if (res.data.code !== 200) return;

            // 更新消息内容
            option.dialogueId = res.data.data.dialogueId;
            option.messageId = res.data.data.messageId;

            // 添加用户消息到对话列表
            if (option.msg) {
                DialogueService.addUserQuestion(option.msg);
            }

            // 处理第一条消息
            if (option.firstMessage) {
                option.msg = option.firstMessage;
            }

            // 添加文件URL（如果有）
            option = appendFileUrlToMessage(option);
            // 发送同步回复
            replySync(option).then(res => {
                if (res.data.code === 200) {
                    if (option.callBack) {
                        option.callBack(res.data);
                    }
                }
            });

            // 清空文件URL
            fileListUrl.value = null;
        });
    };

    /**
     * 处理动作回复-有返回
     * @param {string} message 用户消息
     * @param {string} messageId 消息容器ID
     */
    const handleActionReplyAsync = async option => {
        // 添加文件URL（如果有）
        option = appendFileUrlToMessage(option);

        const { data: res } = await reply(option);
        return res;
    };

    /**
     * 发送回复并建立WebSocket连接
     * @returns {Promise} 请求响应
     * @param option
     */
    const replySendWs = option => {
        // 先断开之前的WebSocket连接
        disconnectAllConnections();

        // 建立WebSocket连接
        handleWebSocket(option);
    };

    return {
        sendMessage,
        replySendWs,
        addSystemCom,
        handleActionReply,
        handleActionReplyAsync,
        handleWebSocket,
        disconnectAllConnections,
        checkConnections,
        setProcessError,
    };
});

export function addSystemCom(options) {
    DialogueService.addSystemResponse(options);
}

export function addUserQuestion(options) {
    DialogueService.addUserQuestion(options);
}

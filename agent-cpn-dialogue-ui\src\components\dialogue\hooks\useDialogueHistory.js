/**
 * @description 对话持久化存储钩子，用于保存和恢复对话内容
 */
import { setStore, getStore, removeStore } from '@/utils/store';
import eventBus from '@/utils/bus.js';

// 对话内容存储键名前缀
const DIALOGUE_CONTENT_PREFIX = 'dialogue_content_';
// 历史记录存储键名
const DIALOGUE_HISTORY_KEY = 'dialogue_history';
// 收藏对话存储键名
const DIALOGUE_FAVORITES_KEY = 'dialogue_favorites';
// 会话标签存储键名
const DIALOGUE_TAGS_KEY = 'dialogue_tags';
// 最大历史记录数量
const MAX_HISTORY_COUNT = 100;

/**
 * 对话持久化存储钩子
 * @returns {Object} 对话持久化相关方法
 */
export function useDialogueHistory() {
    /**
     * 保存对话内容
     * @param {string} dialogueID 对话ID
     * @param {Array} messageList 对话内容列表
     */
    const saveDialogueContent = (dialogueID, messageList) => {
        if (!dialogueID || !messageList) return;

        try {
            // 使用项目通用存储工具保存对话内容
            setStore({
                name: `${DIALOGUE_CONTENT_PREFIX}${dialogueID}`,
                content: messageList,
            });

            // 更新对话消息计数
            eventBus.emit('update-message-count', dialogueID, messageList.length);
        } catch (error) {
            console.error('保存对话内容失败:', error);
        }
    };

    /**
     * 加载对话内容
     * @param {string} dialogueID 对话ID
     * @returns {Array|null} 对话内容列表
     */
    const loadDialogueContent = dialogueID => {
        if (!dialogueID) return null;
    };

    /**
     * 删除对话内容
     * @param {string} dialogueID 对话ID
     */
    const deleteDialogueContent = dialogueID => {
        if (!dialogueID) return;

        try {
            // 删除指定对话内容
            removeStore({
                name: `${DIALOGUE_CONTENT_PREFIX}${dialogueID}`,
            });
            console.log(`对话内容 [${dialogueID}] 已删除`);
        } catch (error) {
            console.error('删除对话内容失败:', error);
        }
    };

    /**
     * 从本地存储中加载历史记录列表
     * @returns {Array} 历史记录列表
     */
    const loadHistoryList = () => {
        try {
            // 从localStorage中获取历史记录
            const storedHistory = localStorage.getItem(DIALOGUE_HISTORY_KEY);
            if (storedHistory) {
                const parsedHistory = JSON.parse(storedHistory);
                // 确保解析的数据是数组
                if (Array.isArray(parsedHistory) && parsedHistory.length > 0) {
                    return parsedHistory;
                }
            }
            // 如果没有找到或格式不正确，返回空数组
            console.log('本地存储中的历史记录为空或格式不正确');
            return [];
        } catch (error) {
            console.error('加载历史记录失败:', error);
            return [];
        }
    };

    /**
     * 保存历史记录列表到本地存储
     * @param {Array} historyList 历史记录列表
     */
    const saveHistoryList = historyList => {
        if (!historyList) return;

        try {
            // 保存至localStorage
            localStorage.setItem(DIALOGUE_HISTORY_KEY, JSON.stringify(historyList));
        } catch (error) {
            console.error('保存历史记录失败:', error);
        }
    };

    /**
     * 添加或更新历史记录项
     * @param {Object} historyItem 历史记录项
     * @param {Array} historyList 历史记录列表
     * @returns {Array} 更新后的历史记录列表
     */
    const addOrUpdateHistoryItem = (historyItem, historyList = []) => {
        if (!historyItem || !historyItem.dialogueID) return historyList;

        // 克隆列表防止直接修改原列表
        const newList = [...historyList];

        // 查找是否已存在相同ID的记录
        const existingIndex = newList.findIndex(item => item.dialogueID === historyItem.dialogueID);

        if (existingIndex !== -1) {
            // 如果已存在，更新时间戳并移动到最前面
            const existingItem = newList[existingIndex];
            newList.splice(existingIndex, 1);
            newList.unshift({
                ...existingItem,
                ...historyItem,
                timestamp: Date.now(),
            });
        } else {
            // 如果不存在，添加新记录到最前面
            newList.unshift({
                ...historyItem,
                timestamp: Date.now(),
            });

            // 如果超出最大记录数，删除最旧的记录
            if (newList.length > MAX_HISTORY_COUNT) {
                newList.pop();
            }
        }

        // 保存更新后的列表
        saveHistoryList(newList);

        return newList;
    };

    /**
     * 删除历史记录项
     * @param {string} dialogueID 对话ID
     * @param {Array} historyList 历史记录列表
     * @returns {Array} 更新后的历史记录列表
     */
    const deleteHistoryItem = (dialogueID, historyList = []) => {
        if (!dialogueID) return historyList;

        // 克隆列表防止直接修改原列表
        const newList = [...historyList];

        // 查找并删除记录
        const existingIndex = newList.findIndex(item => item.dialogueID === dialogueID);
        if (existingIndex !== -1) {
            newList.splice(existingIndex, 1);

            // 同时删除对应的对话内容
            deleteDialogueContent(dialogueID);

            // 保存更新后的列表
            saveHistoryList(newList);
        }

        return newList;
    };

    /**
     * 导出对话内容
     * @param {string} dialogueID 对话ID
     * @returns {Object|null} 导出的对话数据
     */
    const exportDialogue = dialogueID => {
        if (!dialogueID) return null;

        try {
            // 获取对话内容
            const content = loadDialogueContent(dialogueID);
            if (!content) return null;

            // 获取对话信息
            const historyList = loadHistoryList();
            const dialogueInfo = historyList.find(item => item.dialogueID === dialogueID);
            if (!dialogueInfo) return null;

            // 构建导出数据
            const exportData = {
                dialogueInfo: {
                    name: dialogueInfo.name,
                    timestamp: dialogueInfo.timestamp,
                    liked: dialogueInfo.liked || false,
                    messageCount: content.length,
                },
                messages: content,
                exportTime: Date.now(),
            };

            return exportData;
        } catch (error) {
            console.error('导出对话失败:', error);
            return null;
        }
    };

    /**
     * 导入对话内容
     * @param {Object} importData 导入的对话数据
     * @returns {string|null} 导入后的对话ID
     */
    const importDialogue = importData => {
        if (!importData || !importData.dialogueInfo || !importData.messages) return null;

        try {
            const { dialogueInfo, messages } = importData;

            // 生成新的对话ID
            const newDialogueID = `import_${Date.now()}`;

            // 创建新的对话记录
            const newDialogueItem = {
                dialogueID: newDialogueID,
                name: dialogueInfo.name || '导入的对话',
                timestamp: Date.now(),
                liked: dialogueInfo.liked || false,
                messageCount: messages.length,
                imported: true,
            };

            // 保存对话内容
            saveDialogueContent(newDialogueID, messages);

            // 添加到历史记录
            const historyList = loadHistoryList();
            addOrUpdateHistoryItem(newDialogueItem, historyList);

            return newDialogueID;
        } catch (error) {
            console.error('导入对话失败:', error);
            return null;
        }
    };

    /**
     * 分享对话内容
     * @param {string} dialogueID 对话ID
     * @returns {Object|null} 分享的对话数据
     */
    const shareDialogue = dialogueID => {
        // 导出对话内容作为分享数据
        return exportDialogue(dialogueID);
    };

    /**
     * 加载分享的对话
     * @param {Object} shareData 分享的对话数据
     * @returns {string|null} 加载后的对话ID
     */
    const loadSharedDialogue = shareData => {
        // 导入分享的对话数据
        return importDialogue(shareData);
    };

    /**
     * 创建对话标签
     * @param {string} tag 标签名称
     * @returns {Array} 更新后的标签列表
     */
    const createTag = tag => {
        if (!tag || tag.trim() === '') return [];

        try {
            // 加载现有标签
            const tags =
                getStore({
                    name: DIALOGUE_TAGS_KEY,
                }) || [];

            // 如果标签已存在，直接返回
            if (tags.includes(tag.trim())) return tags;

            // 添加新标签
            const newTags = [...tags, tag.trim()];

            // 保存标签列表
            setStore({
                name: DIALOGUE_TAGS_KEY,
                content: newTags,
            });

            return newTags;
        } catch (error) {
            console.error('创建标签失败:', error);
            return [];
        }
    };

    /**
     * 获取所有标签
     * @returns {Array} 标签列表
     */
    const getAllTags = () => {
        try {
            return (
                getStore({
                    name: DIALOGUE_TAGS_KEY,
                }) || []
            );
        } catch (error) {
            console.error('获取标签失败:', error);
            return [];
        }
    };

    /**
     * 为对话添加标签
     * @param {string} dialogueID 对话ID
     * @param {string} tag 标签名称
     */
    const addTagToDialogue = (dialogueID, tag) => {
        if (!dialogueID || !tag) return;

        try {
            // 先确保标签存在
            createTag(tag);

            // 获取对话历史记录
            const historyList = loadHistoryList();
            const index = historyList.findIndex(item => item.dialogueID === dialogueID);

            if (index !== -1) {
                // 添加或更新标签
                const item = historyList[index];

                if (!item.tags) {
                    item.tags = [tag];
                } else if (!item.tags.includes(tag)) {
                    item.tags.push(tag);
                }

                // 保存更新后的历史记录
                saveHistoryList(historyList);
            }
        } catch (error) {
            console.error('为对话添加标签失败:', error);
        }
    };

    /**
     * 从对话中移除标签
     * @param {string} dialogueID 对话ID
     * @param {string} tag 标签名称
     */
    const removeTagFromDialogue = (dialogueID, tag) => {
        if (!dialogueID || !tag) return;

        try {
            // 获取对话历史记录
            const historyList = loadHistoryList();
            const index = historyList.findIndex(item => item.dialogueID === dialogueID);

            if (index !== -1 && historyList[index].tags) {
                // 移除标签
                historyList[index].tags = historyList[index].tags.filter(t => t !== tag);

                // 保存更新后的历史记录
                saveHistoryList(historyList);
            }
        } catch (error) {
            console.error('从对话移除标签失败:', error);
        }
    };

    /**
     * 收藏对话
     * @param {string} dialogueID 对话ID
     */
    const favoriteDialogue = dialogueID => {
        if (!dialogueID) return;

        try {
            // 获取对话历史记录
            const historyList = loadHistoryList();
            const index = historyList.findIndex(item => item.dialogueID === dialogueID);

            if (index !== -1) {
                // 标记为收藏
                historyList[index].liked = true;

                // 保存更新后的历史记录
                saveHistoryList(historyList);

                // 同时更新收藏列表
                const favorites =
                    getStore({
                        name: DIALOGUE_FAVORITES_KEY,
                    }) || [];

                if (!favorites.includes(dialogueID)) {
                    favorites.push(dialogueID);
                    setStore({
                        name: DIALOGUE_FAVORITES_KEY,
                        content: favorites,
                    });
                }
            }
        } catch (error) {
            console.error('收藏对话失败:', error);
        }
    };

    /**
     * 取消收藏对话
     * @param {string} dialogueID 对话ID
     */
    const unfavoriteDialogue = dialogueID => {
        if (!dialogueID) return;

        try {
            // 获取对话历史记录
            const historyList = loadHistoryList();
            const index = historyList.findIndex(item => item.dialogueID === dialogueID);

            if (index !== -1) {
                // 取消收藏标记
                historyList[index].liked = false;

                // 保存更新后的历史记录
                saveHistoryList(historyList);

                // 同时更新收藏列表
                const favorites =
                    getStore({
                        name: DIALOGUE_FAVORITES_KEY,
                    }) || [];

                const newFavorites = favorites.filter(id => id !== dialogueID);
                setStore({
                    name: DIALOGUE_FAVORITES_KEY,
                    content: newFavorites,
                });
            }
        } catch (error) {
            console.error('取消收藏对话失败:', error);
        }
    };

    /**
     * 获取所有收藏的对话
     * @returns {Array} 收藏的对话列表
     */
    const getFavoriteDialogues = () => {
        try {
            const favorites =
                getStore({
                    name: DIALOGUE_FAVORITES_KEY,
                }) || [];

            const historyList = loadHistoryList();

            return historyList.filter(item => favorites.includes(item.dialogueID));
        } catch (error) {
            console.error('获取收藏对话失败:', error);
            return [];
        }
    };

    return {
        saveDialogueContent,
        loadDialogueContent,
        deleteDialogueContent,
        loadHistoryList,
        saveHistoryList,
        addOrUpdateHistoryItem,
        deleteHistoryItem,
        exportDialogue,
        importDialogue,
        shareDialogue,
        loadSharedDialogue,
        createTag,
        getAllTags,
        addTagToDialogue,
        removeTagFromDialogue,
        favoriteDialogue,
        unfavoriteDialogue,
        getFavoriteDialogues,
    };
}

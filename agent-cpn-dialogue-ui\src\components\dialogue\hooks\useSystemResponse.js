/**
 * @typedef {Object} DialogueComponent
 * @property {string} name 组件名
 * @property {Object} [props] 组件属性
 * @property {Object} [events] 组件事件对象，key为事件名，value为事件处理函数
 */
import { ref, watch } from 'vue';
import eventBus from '@/utils/bus.js';
import { useSessionStore } from '@/components/dialogue/store/modules/session';
import { files as filesList } from '@/components/dialogue/store/input.js';

/**
 * 系统回答管理钩子
 * @returns {Object} 系统回答相关方法和状态
 */
export function useSystemResponse() {
    const currentDialogueID = ref('');
    const sessionStore = useSessionStore();

    /**
     * 添加系统回答到对话列表
     * @param {Object} options 配置项
     * @returns {string} 对话ID
     */
    const addSystemResponse = (options = {}) => {
        const messageId = options.id || sessionStore.messageId || `session-${Date.now()}`;
        console.log(messageId, '--------------------------');

        const dialogueItem = {
            id: messageId,
            dialogueId: sessionStore.dialogueId,
            container: options.container,
            isUser: false,
            components: options.components || [],
            answerProps: options.answerProps || {},
            timestamp: Date.now(),
        };

        let existingIndex = -1;
        if (options.container) {
            existingIndex = sessionStore.messageList.findIndex(
                item => item.container === options.container && !item.isUser
            );
        } else {
            existingIndex = sessionStore.messageList.findIndex(item => item.id === messageId);
        }

        if (existingIndex >= 0) {
            const existingItem = sessionStore.messageList[existingIndex];
            sessionStore.messageList[existingIndex] = {
                ...existingItem,
                ...dialogueItem,
                container: options.container || existingItem.container,
                components: options.components
                    ? [...existingItem.components, ...options.components]
                    : existingItem.components,
                answerProps: {
                    ...existingItem.answerProps,
                    ...(options.answerProps || {}),
                },
            };
        } else {
            sessionStore.messageList.push(dialogueItem);
        }

        if (sessionStore.messageList.length === 1 && !currentDialogueID.value) {
            setCurrentDialogueID(options.dialogueID || `session-${Date.now()}`);
        }

        return messageId;
    };

    /**
     * 添加用户问题到对话列表
     * @param {string} question 用户问题
     * @param {Array} files 用户上传的文件列表
     * @returns {string} 对话ID
     */
    const addUserQuestion = (question, files) => {
        const messageId = sessionStore.messageId || `user-${Date.now()}`;
        sessionStore.messageList.push({
            id: messageId,
            isUser: true,
            question,
            files: files || filesList,
            timestamp: Date.now(),
        });

        return messageId;
    };

    /**
     * 清空对话列表
     */
    const clearDialogue = () => {
        sessionStore.messageList = [];
        setCurrentDialogueID('');
    };

    /**
     * 设置当前对话ID
     * @param {string} dialogueID 对话ID
     */
    const setCurrentDialogueID = dialogueID => {
        if (!dialogueID) return;
        currentDialogueID.value = dialogueID;
    };

    /**
     * 根据ID或容器标识更新对话项的属性
     * @param {string|Object} idOrParams 对话ID或包含id/container的对象
     * @param {Object} [updates] 要更新的属性
     */
    const updateDialogue = (idOrParams, updates = {}) => {
        let id = null;
        let container = null;
        let updateData = updates;

        if (typeof idOrParams === 'object') {
            id = idOrParams.id;
            container = idOrParams.container;
            updateData = idOrParams.updates || updates;
        } else {
            id = idOrParams;
        }

        let index = -1;
        if (id) {
            index = sessionStore.messageList.findIndex(item => item.id === id);
        }
        if (index < 0 && container) {
            index = sessionStore.messageList.findIndex(item => item.container === container && !item.isUser);
        }

        if (index >= 0) {
            const existingItem = sessionStore.messageList[index];
            sessionStore.messageList[index] = {
                ...existingItem,
                ...updateData,
                components: updateData.components
                    ? updateData.mergeComponents
                        ? [...existingItem.components, ...updateData.components]
                        : updateData.components.map(comp => {
                              const existingComp = existingItem.components.find(ec => ec.name === comp.name);
                              if (existingComp && existingComp.events && comp.events) {
                                  return {
                                      ...comp,
                                      events: { ...existingComp.events, ...comp.events },
                                  };
                              }
                              return comp;
                          })
                    : existingItem.components,
                answerProps: updateData.answerProps
                    ? { ...existingItem.answerProps, ...updateData.answerProps }
                    : existingItem.answerProps,
            };
        }
    };

    // 注册全局事件处理函数
    const setupGlobalEvents = () => {
        eventBus.on('add-system-response', options => addSystemResponse(options));
        eventBus.on('add-user-question', (question, files) => addUserQuestion(question, files));
        eventBus.on('update-dialogue', params => updateDialogue(params));
        eventBus.on('clear-dialogue', () => clearDialogue());
        eventBus.on('set-current-dialogue-id', dialogueID => setCurrentDialogueID(dialogueID));
    };

    const cleanupGlobalEvents = () => {
        eventBus.off('add-system-response');
        eventBus.off('add-user-question');
        eventBus.off('update-dialogue');
        eventBus.off('clear-dialogue');
        eventBus.off('load-dialogue');
        eventBus.off('set-current-dialogue-id');
    };

    setupGlobalEvents();

    return {
        currentDialogueID,
        addSystemResponse,
        addUserQuestion,
        updateDialogue,
        clearDialogue,
        setCurrentDialogueID,
        cleanupGlobalEvents,
    };
}

/**
 * 全局对话操作工具对象
 */
export const DialogueService = {
    addSystemResponse: options => {
        eventBus.emit('add-system-response', options);
        return options.id || `dialogue-${Date.now()}`;
    },

    addUserQuestion: (question, files) => {
        eventBus.emit('add-user-question', question, files);
        return `user-${Date.now()}`;
    },

    updateDialogue: (id, updates) => {
        eventBus.emit('update-dialogue', { id, updates });
    },

    updateDialogueByContainer: (container, updates) => {
        eventBus.emit('update-dialogue', { container, updates });
    },

    clearDialogue: () => {
        eventBus.emit('clear-dialogue');
    },

    loadDialogue: dialogueID => {
        let result = false;
        eventBus.emit('load-dialogue', dialogueID, success => {
            result = success;
        });
        return result;
    },

    setCurrentDialogueID: dialogueID => {
        eventBus.emit('set-current-dialogue-id', dialogueID);
    },
};

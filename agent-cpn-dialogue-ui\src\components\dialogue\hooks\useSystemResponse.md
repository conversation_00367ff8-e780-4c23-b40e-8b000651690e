# useSystemResponse 使用文档

## 安装和导入

```javascript
import { useSystemResponse } from '@/components/dialogue/hooks/useSystemResponse';
```

## API 参考

### useSystemResponse 钩子函数

#### 返回值

| 名称                | 类型                          | 描述                 |
| ------------------- | ----------------------------- | -------------------- |
| dialogueList        | Ref\<Array\>                  | 对话列表响应式数组   |
| addSystemResponse   | Function(options)             | 添加系统回答方法     |
| addUserQuestion     | Function(question)            | 添加用户问题方法     |
| updateDialogue      | Function(idOrParams, updates) | 更新对话方法         |
| clearDialogue       | Function()                    | 清空对话列表方法     |
| cleanupGlobalEvents | Function()                    | 清理全局事件监听方法 |

### DialogueService 全局服务

| 方法名                                        | 描述                   |
| --------------------------------------------- | ---------------------- |
| addSystemResponse(options)                    | 添加系统回答到对话列表 |
| addUserQuestion(question)                     | 添加用户问题到对话列表 |
| updateDialogue(id, updates)                   | 通过ID更新对话         |
| updateDialogueByContainer(container, updates) | 通过容器标识更新对话   |
| clearDialogue()                               | 清空对话列表           |

## 详细说明

### addSystemResponse(options)

添加系统回答到对话列表。

**参数**:

- `options`: 配置对象
    - `id`: 对话唯一标识，用于更新已存在的对话
    - `container`: 容器标识，用于判断是否添加到已有对话
    - `components`: 组件配置数组，每个对象包含name、props和events
    - `message`: 纯文本消息内容
    - `answerProps`: answer回答容器的属性

**返回值**: 对话ID (字符串)

**示例**:

```javascript
const dialogueId = addSystemResponse({
    container: 'data-collection',
    components: [
        {
            name: 'CollectionTask',
            props: {
                taskData: {
                    sourceInfo: {
                        dataSource: 'mysql',
                        database: 'policies_db',
                        table: 'policies',
                    },
                    method: 'full',
                    targetInfo: {
                        dataSource: 'postgresql',
                        database: 'integrated_db',
                        table: 'target_policies',
                    },
                },
            },
            events: {
                'task-complete': result => {
                    console.log('任务完成:', result);
                },
            },
        },
    ],
});
```

### addUserQuestion(question)

添加用户问题到对话列表。

**参数**:

- `question`: 用户问题文本 (字符串)

**返回值**: 对话ID (字符串)

**示例**:

```javascript
const userDialogueId = addUserQuestion('帮我将【MySQL】数据源的数据迁移到【PostgreSQL】');
```

### updateDialogue(idOrParams, updates)

根据ID或容器标识更新对话项的属性。

**参数**:

- `idOrParams`: 对话ID (字符串) 或包含id/container的对象
- `updates`: 要更新的属性 (对象)

**示例**:

```javascript
// 通过ID更新
updateDialogue('dialogue-123456789', {
    message: '更新后的消息内容',
});

// 或者使用对象参数
updateDialogue({
    id: 'dialogue-123456789',
    updates: {
        message: '更新后的消息内容',
    },
});

// 通过container更新
updateDialogue({
    container: 'data-collection',
    updates: {
        components: [
            {
                name: 'TaskStatus',
                props: { status: 'completed' },
            },
        ],
        mergeComponents: true, // 合并而非替换组件
    },
});
```

### clearDialogue()

清空对话列表。

**示例**:

```javascript
clearDialogue();
```

## 组件类型定义

```typescript
/**
 * @typedef {Object} DialogueComponent
 * @property {string} name 组件名
 * @property {Object} [props] 组件属性
 * @property {Object} [events] 组件事件对象，key为事件名，value为事件处理函数
 */
```

## 应用场景示例

### 1. 在Vue组件中使用钩子函数

```vue
<template>
    <div class="dialogue-container">
        <!-- 显示对话列表 -->
        <div v-for="item in dialogueList" :key="item.id" class="dialogue-item">
            <div v-if="item.isUser" class="user-question">
                {{ item.question }}
            </div>
            <div v-else class="system-response">
                <template v-if="item.message">{{ item.message }}</template>
                <component
                    v-for="(comp, index) in item.components"
                    :key="`${comp.name}-${index}`"
                    :is="resolveComponent(comp.name)"
                    v-bind="{ ...item.answerProps, ...comp.props }"
                    v-on="comp.events || {}"
                />
            </div>
        </div>

        <!-- 用户输入 -->
        <div class="input-area">
            <input v-model="userInput" type="text" placeholder="请输入您的问题..." />
            <button @click="handleSendQuestion">发送</button>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { useSystemResponse } from '@/components/dialogue/hooks/useSystemResponse';

const userInput = ref('');
const { dialogueList, addUserQuestion, addSystemResponse, clearDialogue } = useSystemResponse();

// 发送用户问题
const handleSendQuestion = () => {
    if (!userInput.value.trim()) return;

    // 添加用户问题
    addUserQuestion(userInput.value);

    // 模拟系统回答
    setTimeout(() => {
        addSystemResponse({
            container: 'response-container',
            message: `您好，我已收到您的问题: "${userInput.value}"`,
            components: [
                {
                    name: 'ResponseComponent',
                    props: { content: '这是回答内容' },
                },
            ],
        });
    }, 1000);

    userInput.value = '';
};

// 解析组件
const resolveComponent = name => {
    // 这里可以根据name动态导入组件或使用已注册的组件
    return name;
};
</script>
```

### 2. 使用全局服务在非组件环境中管理对话

```javascript
import { DialogueService } from '@/components/dialogue/hooks/useSystemResponse';

// 在任何JavaScript文件中使用
function handleUserInput(userQuestion) {
    // 添加用户问题
    DialogueService.addUserQuestion(userQuestion);

    // 处理用户问题并生成回答
    processUserQuestion(userQuestion).then(response => {
        // 添加系统回答
        DialogueService.addSystemResponse({
            message: response.textContent,
            components: response.components,
        });
    });
}

// 使用示例: 处理数据采集任务
function createDataCollectionTask(params) {
    // 构建采集任务描述
    const taskDescription =
        `帮我将【${params.sourceType}】数据源，` +
        `【${params.sourceDb}】数据库，` +
        `【${params.sourceTable}】数据表的数据按` +
        `【${params.method}】的方式定量到` +
        `【${params.targetType}】数据源，` +
        `【${params.targetDb}】数据库，` +
        `【${params.targetTable}】数据表`;

    // 添加用户问题
    DialogueService.addUserQuestion(taskDescription);

    // 创建任务并添加系统回答
    const taskId = `task_${Date.now()}`;
    DialogueService.addSystemResponse({
        container: 'data-collection-task',
        components: [
            {
                name: 'CollectionTaskResult',
                props: {
                    collectionTask: {
                        sourceInfo: {
                            dataSource: params.sourceType,
                            database: params.sourceDb,
                            table: params.sourceTable,
                        },
                        method: params.method,
                        targetInfo: {
                            dataSource: params.targetType,
                            database: params.targetDb,
                            table: params.targetTable,
                        },
                        taskId: taskId,
                        createdAt: new Date().toISOString(),
                        status: 'pending',
                    },
                },
                events: {
                    'status-change': newStatus => {
                        // 更新任务状态
                        updateTaskStatus(taskId, newStatus);
                    },
                },
            },
        ],
    });

    return taskId;
}

// 更新任务状态
function updateTaskStatus(taskId, status) {
    DialogueService.updateDialogueByContainer('data-collection-task', {
        components: [
            {
                name: 'CollectionTaskResult',
                props: { status },
            },
        ],
    });
}
```

// 导入组件
import { markRaw } from 'vue';
import dialogueInput from '@/components/dialogue/main/dialogInput.vue';
import SelectionToolbar from '@/components/dialogue/components/SelectionToolbar.vue';
import agentTree from '@/components/dialogue/components/AgentLeft/agent-tree.vue';

// import { Ws } from "@/utils/ws";
// 右侧容器组件
import pageRightContainer from '@/components/dialogue/components/pageRightContainer.vue';
import agentHeader from '@/components/dialogue/main/agent-header.vue';
// 智能回答界面组件
// 默认回答
import pageAgentAnswerDefault from '@/components/dialogue/components/pageAgentAnswerDefault.vue';

//? 1、数据采集====================左侧页面
// 模糊查询生成内容
import cjmhCreate from '@/components/dialogue/components/DataCollection/cjmhCreate.vue';
// 采集信息查询工具
import cjInfoTool from '@/components/dialogue/components/DataCollection/cjInfoTool.vue';
// 采集任务生成工具
import cjGenerateTool from '@/components/dialogue/components/DataCollection/cjGenerateTool.vue';
// 采集结果
import taskGenerateResult from '@/components/dialogue/components/DataCollection/taskGenerateResult.vue';

// 资源类别配置工具
import zymlConfig from '@/components/dialogue/components/DataCatalog/zymlConfig.vue';

import CreateTask from '@/components/dialogue/common/CreateTask.vue';
// 目录生成结果
import mlscResult from '@/components/dialogue/components/DataCatalog/mlscResult.vue';
// 任务推荐
import cjTaskRecommend from '@/components/dialogue/components/DataCollection/cjTaskRecommend.vue';
import examples1 from '@/components/dialogue/examples/1.vue';

// ?1、数据采集====================右侧页面
// 编辑采集任务
import editCjrw from '@/components/dialogue/components/right/zncj/editCjrw.vue';
//执行过程
import systemInfo from '@/components/dialogue/components/right/zncj/systemInfo.vue';
// 贴源层-政策原文表
import policiesYwb from '@/components/dialogue/components/right/zncj/policiesYwb.vue';

// ?2、智能目录====================右侧页面
// 系统信息资源生成目录 —— 详细配置目录信息
// 基本信息配置
import resourceCatalog from '@/components/dialogue/components/right/znml/resourceCatalog.vue';
import dialogueInfoPzTool from '@/components/dialogue/components/DataCatalog/dialogueInfoPzTool.vue';
// 基本信息配置工具
import dialogueBaseInfoPzTool from '@/components/dialogue/components/DataCatalog/dialogueBaseInfoPzTool.vue';
// 编辑目录基本信息
import editBaseInfo from '@/components/dialogue/components/right/znml/editBaseInfo.vue';

// 更新配置
import updataConfig from '@/components/dialogue/components/right/znml/updataConfig.vue';
// 修改资源类别信息
import editCategory from '@/components/dialogue/components/right/znml/editCategory.vue';

// 执行过程
import backInfo from '@/components/dialogue/components/right/znml/backInfo.vue';
import thinkingProcess from '@/components/dialogue/common/thinkingProcess.vue';
// 导入新创建的组件
import UserQuestion from '@/components/dialogue/components/UserQuestion.vue';
import SystemAnswer from '@/components/dialogue/components/SystemAnswer.vue';
import MessageWrapper from '@/components/dialogue/components/MessageWrapper.vue';
// 思考结果
import thinkingResult from '@/components/dialogue/common/thinkingResult.vue';
import thinkingContentStep from '@/components/dialogue/common/thinkingContentStep.vue';
import stepError from '@/components/dialogue/common/Error.vue';
import MarkDown from '@/components/dialogue/common/MarkDown.vue';
// ?3、指标问数====================
import IndicatorData from '@/components/dialogue/components/DataAsk/IndicatorData.vue';
import IndicatorKanban from '@/components/dialogue/components/DataAsk/IndicatorKanban.vue';
import IndicatorSearch from '@/components/dialogue/components/DataAsk/IndicatorSearch.vue';
import ImportResult from '@/components/dialogue/components/DataAsk/ImportResult.vue';
import IndicatorQuery from '@/components/dialogue/components/DataAsk/IndicatorQuery.vue';
import FilesExport from '@/components/dialogue/common/FilesExport.vue';
import RightAskDatabaogao from '@/components/dialogue/components/DataAsk/RightAskDatabaogao/index.vue';
// 指标问数右侧组件
import RightIndicatorInfo from '@/components/dialogue/components/DataAsk/RightIndicatorInfo.vue';
import RightEditTablePreview from '@/components/dialogue/common/RightEditTablePreview.vue';
import RightHistory from '@/components/dialogue/components/AgentLeft/RightHistory.vue';
// ! 公共组件
import TablePreview from '@/components/dialogue/common/TablePreview.vue';
// 意图识别与分析
import createWorkFlow from '@/components/dialogue/common/createWorkFlow.vue';
// 超音数配置
// 创建组件映射表
const componentMap = {
    // 左侧数据采集组件

    cjmhCreate: markRaw(cjmhCreate),
    cjGenerateTool: markRaw(cjGenerateTool),
    taskGenerateResult: markRaw(taskGenerateResult),

    // 意图识别与分析
    createWorkFlow: markRaw(createWorkFlow),
    // 目录基本信息配置工具
    dialogueInfoPzTool: markRaw(dialogueInfoPzTool),
    dialogueBaseInfoPzTool: markRaw(dialogueBaseInfoPzTool),

    zymlConfig: markRaw(zymlConfig),
    mlscResult: markRaw(mlscResult),
    // 任务推荐
    cjTaskRecommend: markRaw(cjTaskRecommend),

    thinkingProcess: markRaw(thinkingProcess),

    // 右侧采集组件
    editCjrw: markRaw(editCjrw),
    systemInfo: markRaw(systemInfo),
    policiesYwb: markRaw(policiesYwb),

    // 右侧目录组件
    resourceCatalog: markRaw(resourceCatalog),
    editBaseInfo: markRaw(editBaseInfo),
    CreateTask: markRaw(CreateTask),
    updataConfig: markRaw(updataConfig),
    backInfo: markRaw(backInfo),
    editCategory: markRaw(editCategory),
    // --------------------------------

    // 思考结构
    thinkingResult: markRaw(thinkingResult),
    thinkingContentStep: markRaw(thinkingContentStep),
    stepError: markRaw(stepError),
    cjInfoTool: markRaw(cjInfoTool),
    // --------------------------------

    // 指标问数
    IndicatorData: markRaw(IndicatorData),
    IndicatorKanban: markRaw(IndicatorKanban),
    IndicatorSearch: markRaw(IndicatorSearch),
    ImportResult: markRaw(ImportResult),
    RightIndicatorInfo: markRaw(RightIndicatorInfo),
    RightEditTablePreview: markRaw(RightEditTablePreview),
    IndicatorQuery: markRaw(IndicatorQuery),
    FilesExport: markRaw(FilesExport),
    // --------------------------------
    examples1: markRaw(examples1),
    TablePreview: markRaw(TablePreview),
    RightAskDatabaogao: markRaw(RightAskDatabaogao),
    RightHistory: markRaw(RightHistory),
    // 确保Markdown组件名称正确
    MarkDown: markRaw(MarkDown),
};
export {
    componentMap,
    dialogueInput,
    agentTree,
    pageRightContainer,
    agentHeader,
    pageAgentAnswerDefault,
    UserQuestion,
    SystemAnswer,
    MessageWrapper,
    SelectionToolbar,
};

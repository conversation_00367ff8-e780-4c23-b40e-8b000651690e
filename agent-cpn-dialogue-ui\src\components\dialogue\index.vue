<template>
    <el-container class="agent-container">
        <el-header class="header">
            <div class="logo">
                <img :src="publicPath + `/static/dialogue/indexlogo.png`" alt="logo" />
                <span>AI数据治理智能体平台</span>
                <div class="ml-2">
                    <Icon
                        :name="!isShowAside ? 'tree-zhankai' : 'tree-shousuo'"
                        @click="isShowAside = !isShowAside"
                        size="22"
                    />
                </div>
            </div>
            <div class="container_agent_header">
                <div class="header_user" v-if="token">
                    <img class="top-bar__img" :src="userStore.avatar" @click="userStore.toCeshi" />
                    <el-dropdown>
                        <span class="el-dropdown-link">
                            {{ userStore.userInfo.real_name }}
                            <el-icon class="el-icon--right">
                                <arrow-down />
                            </el-icon>
                        </span>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item>
                                    <router-link to="/index">{{ $t('navbar.dashboard') }}</router-link>
                                </el-dropdown-item>
                                <el-dropdown-item>
                                    <router-link to="/info/index">{{ $t('navbar.userinfo') }}</router-link>
                                </el-dropdown-item>
                                <el-dropdown-item>
                                    <router-link to="/vform/index">{{ '后台管理' }}</router-link>
                                </el-dropdown-item>
                                <el-dropdown-item @click="userStore.logout" divided>
                                    {{ $t('navbar.logOut') }}
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
                <div class="user-info" v-else @click="userStore.login">
                    <span>立即登录</span>
                    <img class="avatar" :src="deFaultAvatar" alt="avatar" />
                </div>
            </div>
        </el-header>
        <el-container class="content">
            <!-- 顶部导航 -->
            <transition name="slide">
                <el-aside width="auto" style="overflow: visible" v-if="isShowAside">
                    <agentTree :showHeader="false"></agentTree>
                </el-aside>
            </transition>
            <div class="box">
                <!-- 欢迎区域 -->
                <div class="welcome-section">
                    <h1 class="welcome-title">
                        您好！欢迎来到
                        <span class="highlight">AI数据治理</span>
                        小助手
                    </h1>
                    <p class="welcome-subtitle">我可以为您提供数据治理的相关服务哦~</p>
                </div>

                <!-- 输入框区域 -->
                <div class="mt-14">
                    <dialogueInput @sendMessage="SendIndexMessage" ref="dialogInputRef" />
                </div>
                <!-- 功能卡片区域 -->
                <div class="feature-grid" :class="{ collapsed: !isExpanded }">
                    <div
                        class="feature-card"
                        v-for="(item, index) in featureCards"
                        :key="index"
                        @mouseenter="handleCardMouseEnter(index)"
                        @mouseleave="handleCardMouseLeave(index)"
                    >
                        <div class="flex">
                            <div :class="`item-${index + 1}`" class="w-10 h-8 mt-2 mr-2"></div>
                            <div class="right">
                                <div class="card-icon">{{ item.name }}</div>
                                <div class="card-desc">{{ item.info }}</div>
                            </div>
                        </div>

                        <!-- 子项菜单 -->
                        <div
                            class="child-menu"
                            v-if="item.children && item.children.length && activeCardIndex === index"
                            @mouseenter="handleChildMenuMouseEnter"
                            @mouseleave="handleChildMenuMouseLeave(index)"
                        >
                            <div class="child-menu-content">
                                <div
                                    class="child-menu-item"
                                    v-for="(child, childIndex) in item.children"
                                    :key="childIndex"
                                    @click.stop="selectChildItem(item, index, child, childIndex)"
                                >
                                    <Icon :name="child.icon" size="20" class="child-icon mt-1" v-if="child.icon"></Icon>
                                    <div class="child-info">
                                        <h4>{{ child.childName }}</h4>
                                        <p>{{ child.childInfo }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <p class="collapse-btn" @click="toggleExpand">
                    {{ isExpanded ? '收起' : '更多' }}
                    <img :class="{ rotate: isExpanded }" :src="publicPath + `/static/dialogue/shouqi.png`" />
                </p> -->
            </div>
        </el-container>
    </el-container>
</template>

<script setup>
import { ref, useTemplateRef } from 'vue';
import { agentTree } from '@/components/dialogue/index.js';
import dialogueInput from '@/components/dialogue/main/dialogInput.vue';
import { useSessionStore } from '@/components/dialogue/store/modules/session.js';
import { useUserStore } from '@/components/dialogue/store/modules/user';
const { selectChildItem } = useDialogInput();
import deFaultAvatar from '/static/dialogue/indexpeople.png';
import { publicPath } from '@/components/dialogue/store/main.js';
const isExpanded = ref(true);
const isShowAside = ref(false);
const sessionStore = useSessionStore();
const userStore = useUserStore();
const token = computed(() => userStore.token);
const dialogInputRef = useTemplateRef('dialogInputRef');
// 添加当前激活的卡片索引
const activeCardIndex = ref(null);
// 添加计时器引用
let menuCloseTimer = null;

// 鼠标进入卡片事件处理
const handleCardMouseEnter = index => {
    // 清除之前的计时器
    if (menuCloseTimer) {
        clearTimeout(menuCloseTimer);
        menuCloseTimer = null;
    }
    activeCardIndex.value = index;
};

// 鼠标离开卡片事件处理
const handleCardMouseLeave = index => {
    // 使用计时器延迟关闭菜单
    menuCloseTimer = setTimeout(() => {
        if (activeCardIndex.value === index) {
            activeCardIndex.value = null;
        }
    }, 300);
};

// 鼠标进入子菜单事件处理
const handleChildMenuMouseEnter = () => {
    // 清除关闭计时器
    if (menuCloseTimer) {
        clearTimeout(menuCloseTimer);
        menuCloseTimer = null;
    }
};

// 鼠标离开子菜单事件处理
const handleChildMenuMouseLeave = () => {
    activeCardIndex.value = null;
};

const featureCards = ref([
    // {
    //     name: '工作台',
    //     info: '查看您工作任务和任务进展情况',
    //     children: [],
    // },
    {
        name: '我要数据采集',
        info: '根据需求采集数据，查看采集情况',
        children: [
            {
                childName: '采集数据到数据表',
                childInfo: '将某库表里的数据采集到系统的数据表里',
                value: 'cj1',
                icon: 'cj1',
            },
            {
                childName: '上传数据采集到系统库表',
                childInfo: '点击右侧按钮上传数据采集到系统数据表里',
                value: 'cj2',
                icon: 'cj2',
            },
        ],
    },
    {
        name: '我要智能问数',
        info: '查询数据，生成可视化数据图表',
        children: [
            // {
            //     childName: '问指标',
            //     childInfo: '帮助您用自然语言查询指标',
            //     value: 'ws1',
            //     icon: 'wzb',
            // },
            {
                childName: '模板问数',
                childInfo: '帮助您将数据导入您所需文本/表格文档模板',
                value: 'ws2',
                icon: 'mbws',
            },
            // {
            //     childName: '问大屏看板',
            //     childInfo: '帮助您用自然语言查询指标大屏',
            //     value: 'ws3',
            //     icon: 'wdp',
            // },
        ],
    },
    {
        name: '我要智能目录',
        info: '将数据生成目录，提供目录订阅',
        children: [
            {
                childName: '系统信息资源生成目录',
                childInfo: '将某库表里的数据生成资源目录',
                value: 'bm1',
                icon: 'ml1',
            },
            {
                childName: '上传数据一本帐文件生成资源目录',
                childInfo: '上传数据表到对话页面自动生成资源目录',
                value: 'bm2',
                icon: 'ml2',
            },
            {
                childName: '上传非结构化数据文件生成资源目录',
                childInfo: '上传非结构化数据文件到对话页面自动生成资源目录',
                value: 'bm3',
                icon: 'ml3',
            },
        ],
    },
    // {
    //     name: '我要智能打标',
    //     info: '生成标签，为增强的数据打标',
    //     children: [],
    // },
    // {
    //     name: '我要智能治理',
    //     info: '处理与加工数据，提高数据质量',
    //     children: [],
    // },
    // {
    //     name: '我要智能画像',
    //     info: '生成数据画像，查看企业总体情况',
    //     children: [],
    // },
    // {
    //     name: '我要智能共享',
    //     info: '根据需求生成接口，提供共享服务',
    //     children: [],
    // },
]);

const SendIndexMessage = async message => {
    localStorage.setItem('chatContent', message);
    try {
        sessionStore.setCurrentSession({
            title: message,
        });
        await sessionStore.createSessionList(message);
    } catch (error) {
        console.error('发送消息失败:', error);
    }
};

const toggleExpand = () => {
    isExpanded.value = !isExpanded.value;
};
</script>

<style lang="scss" scoped>
/* 滑入滑出动画 */
.slide-enter-active,
.slide-leave-active {
    transition: transform 0.3s ease-in-out;
}
.slide-enter {
    transform: translateX(100%); /* 从左侧滑入 */
}
.slide-leave-to {
    transform: translateX(-100%); /* 向右侧滑出 */
}
.agent-container {
    width: 100%;
    height: 100%;
    background: url('/static/dialogue/index_bj.png') no-repeat center top / 100% 100%;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 40px;
        .logo {
            display: flex;
            align-items: center;
            gap: 8px;

            span {
                font-size: 24px;
                font-weight: bold;
                color: #1d2939;
            }
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;

            span {
                font-size: 14px;
                color: #1570ef;
            }

            .avatar {
                width: 24px;
                height: 24px;
                border-radius: 50%;
            }
        }
    }

    .content {
        flex: 1;
        overflow: auto;

        .box {
            width: 100%;
            max-width: 1080px;
            margin: 0 auto;
        }
    }

    .welcome-section {
        text-align: center;
        //margin-bottom: 60px;
        margin-top: 10rem;
        .welcome-title {
            font-size: 42px;
            font-weight: bold;
            color: #000;
            margin-bottom: 40px;

            .highlight {
                color: #0f50f5;
                background: linear-gradient(90deg, #00a6ff 3.25%, #0d4cf8 46.28%, #cc65f9 98.88%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
        }

        .welcome-subtitle {
            font-size: 28px;
            color: #525252;
        }
    }

    .input-section {
        max-width: 800px;
        margin: 0 auto 40px;

        .input-wrapper {
            background: $bg;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 16px;

            input {
                width: 100%;
                border: none;
                outline: none;
                font-size: 14px;
                margin-bottom: 12px;

                &::placeholder {
                    color: #999;
                }
            }

            .input-tools {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .deep-thought {
                    color: #666;
                    font-size: 14px;
                    background: #f5f5f5;
                    padding: 4px 8px;
                    border-radius: 4px;
                }

                .action-buttons {
                    display: flex;
                    gap: 8px;

                    .tool-btn {
                        background: none;
                        border: none;
                        cursor: pointer;
                        padding: 8px;
                    }

                    .send-btn {
                        background: #1890ff;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px 16px;
                        cursor: pointer;
                    }
                }
            }
        }
    }

    .feature-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        margin-top: 20px;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        &.collapsed {
            height: 146px;
            overflow: hidden;
        }

        .feature-card {
            position: relative;
            background: $bg;
            border-radius: 8px;
            box-shadow: 2px 2px 10px 0px rgba(94, 137, 249, 0.08);
            padding: 24px;
            cursor: pointer;
            transition: all 0.3s;

            .item-1 {
                background: url('/static/dialogue/cardimg1.png') no-repeat;
            }

            .item-2 {
                background: url('/static/dialogue/cardimg2.png') no-repeat;
            }

            .item-3 {
                background: url('/static/dialogue/cardimg3.png') no-repeat;
            }

            .item-4 {
                background: url('/static/dialogue/cardimg4.png') no-repeat;
            }

            .item-5 {
                background: url('/static/dialogue/cardimg5.png') no-repeat;
            }

            .item-6 {
                background: url('/static/dialogue/cardimg6.png') no-repeat;
            }

            .item-7 {
                background: url('/static/dialogue/cardimg7.png') no-repeat;
            }

            .item-8 {
                background: url('/static/dialogue/cardimg8.png') no-repeat;
            }

            &:hover {
                transform: translateY(-4px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .card-icon {
                font-size: 20px;
                font-weight: 500;
                color: #101828;
                margin-bottom: 20px;
            }

            .card-desc {
                font-size: 14px;
                color: #667085;
                line-height: 20px;
            }

            // 子菜单样式
            .child-menu {
                position: absolute;
                bottom: 160px;
                left: 0;
                z-index: 100;
                padding-top: 8px;
                .child-menu-content {
                    background: $bg;
                    border-radius: 8px;
                    box-shadow: 0px 0px 10px 0px rgba(222, 230, 240, 0.6);
                    padding: 8px;
                    width: 300px;

                    .child-menu-item {
                        display: flex;
                        padding: 10px;
                        border-radius: 4px;

                        .child-icon {
                            margin-right: 10px;
                        }

                        .child-info {
                            h4 {
                                font-size: 14px;
                                color: #1d2939;
                                margin: 0 0 5px 0;
                                font-weight: normal;
                            }

                            p {
                                font-size: 12px;
                                color: #475467;
                                margin: 0;
                            }
                        }

                        &:hover {
                            background-color: #eff8ff;
                            h4 {
                                color: #1570ef;
                            }
                        }
                    }
                }
            }
        }
    }

    .collapse-btn {
        height: 40px;
        display: flex;
        align-items: center;
        gap: 10px;
        font-size: 14px;
        color: #475467;
        cursor: pointer;

        img {
            transition: transform 0.3s ease;

            &.rotate {
                transform: rotate(270deg);
            }
        }

        &:hover {
            color: #1570ef;
        }
    }
}
</style>

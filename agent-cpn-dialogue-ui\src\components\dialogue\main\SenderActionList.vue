<template>
    <!-- <div
        class="prefix-self-wrap"
        :class="{ 'deep-think': true, 'deep-think-click': isDeepThink }"
        @click="toggleDeepThink"
    >
        <el-icon>
            <ElementPlus />
        </el-icon>
        <span>深度思考(R1)</span>
    </div> -->
    <div></div>
    <div class="flex-center">
        <div class="gap-2 flex-center">
            <!-- <Icon name="tp" color="#98A2B3" size="16" /> -->
            <div class="btn-list">
                <Icon name="yuyin" color="#98A2B3" size="16" v-if="!loading" :loading="loading" @click="handleStart" />

                <el-icon v-else class="animate-spin" color="#626aef" plain :z-index="99" @click="stop">
                    <Loading />
                </el-icon>
            </div>
            <Icon name="lianjie" color="#98A2B3" size="16" @click="handleFileUpload" />
        </div>
        <div class="divider-line"></div>
        <!-- <el-button v-if="isLoading" type="primary" plain circle @click="cancelConnect" class="loading">
            <el-icon>
                <Loading />
            </el-icon>
        </el-button> -->

        <template v-if="isLoading">
            <el-tooltip content="停止生成" placement="top">
                <el-button type="primary" plain circle @click="cancelConnect" class="stop-btn animate-spin">
                    <span class="stop-icon semi-icon"></span>
                </el-button>
            </el-tooltip>
        </template>
        <el-button
            v-else
            round
            class="gradient-btn"
            :disabled="!senderValue || isDisabled"
            :style="{ cursor: senderValue ? 'pointer' : 'not-allowed' }"
            @click="senderValue && sendMessage()"
        >
            <el-icon>
                <Promotion />
            </el-icon>
        </el-button>
    </div>
</template>
<script setup>
defineOptions({
    name: 'SenderActionList',
});

import { isDisabled, senderValue, senderRef, isLoading } from '@/components/dialogue/store/input.js';
import { useDialogInput } from '@/components/dialogue/hooks/useDialogInput.js';
const { openCloseHeader } = useDialogInput();
const dialogueHandler = useDialogueHandler();

import { useRecord } from 'vue-element-plus-x';
const {
    start,
    value: text,
    loading,
    stop,
} = useRecord({
    onEnd: handleEnd,
});

function handleStart() {
    start();
}

function handleEnd(res) {
    console.log('end:', res);
}

watch(
    text,
    val => {
        senderValue.value = val;
    },
    { deep: true }
);
const cancelConnect = () => {
    dialogueHandler.disconnectAllConnections();
};
// 组件事件
const emit = defineEmits(['sendMessage']);

/**
 * @description 发送消息
 */
const sendMessage = () => {
    emit('sendMessage');
};
const handleFileUpload = () => {
    openCloseHeader('file');
};
</script>
<style scoped lang="scss">
.prefix-self-wrap {
    display: flex;
    align-items: center;
    gap: 4px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 12px;
}
.deep-think {
    opacity: 0.7;
    transition: all 0.3s;
    border: 1px solid gray;
    padding: 2px 8px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}
.loading {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.deep-think-click {
    opacity: 1;
    color: #626aef;
    border: 1px solid #626aef !important;
    font-weight: 700;
    background-color: rgba(98, 106, 239, 0.05);
}
.gradient-btn {
    background: linear-gradient(90deg, #014dff 0%, #10d3ff 100%);
    border: none;
    color: white;

    &:hover {
        background: linear-gradient(90deg, #014dff 0%, #10d3ff 100%);
    }
    &:active {
        background: linear-gradient(90deg, #014dff 0%, #10d3ff 100%);
    }
    &:disabled {
        background: linear-gradient(90deg, #a5adbd 0%, #d0d5dd 100%);
        color: $bg;
    }
    &:focus {
        background: linear-gradient(90deg, #014dff 0%, #10d3ff 100%);
    }
}
.stop-btn {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border: 2px solid #222;
    box-shadow: none;
    transition: background 0.2s;
    &:hover {
        background: #f5f6fa;
    }
}
.stop-icon {
    display: block;
    width: 8px;
    height: 8px;
    background: #222;
    border-radius: 3px;
}
</style>

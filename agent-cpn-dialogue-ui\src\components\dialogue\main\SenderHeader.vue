<template>
    <div>
        <div class="header-self-wrap" v-if="headerType === 'template'">
            <div class="header-self-title">
                <span>{{ currentBusinessTypeName }}</span>
                <div class="flex items-center">
                    <Icon name="contraction" @click="toggleHeader" />
                    <div class="divider-line"></div>
                    <el-icon @click="closeHeader" :size="16">
                        <Close />
                    </el-icon>
                </div>
            </div>
            <div class="header-self-content form-container">
                <!-- 表单组件 -->
                <keep-alive>
                    <component :is="currentFormComponent" :form="form" @getFormData="getFormData"></component>
                </keep-alive>
            </div>
        </div>
        <div class="file-list-wrap w-full h-40" v-if="headerType === 'file'">
            <div class="flex flex-col gap-2">
                <div class="file-header flex justify-between items-center mb-2 mt-2">
                    <span class="text-sm">上传文件大小不能超过2MB文件或图片</span>
                    <el-icon @click="closeHeader" :size="16">
                        <Close />
                    </el-icon>
                </div>
                <common-FileUploadList :handleDeleteCard="handleDeleteCard"></common-FileUploadList>
            </div>
        </div>
    </div>
</template>
<script setup>
import { computed, markRaw } from 'vue';
import {
    showHeaderFlog,
    currentBusinessType,
    form,
    senderValue,
    currentBusinessTypeName,
    headerType,
    files,
} from '@/components/dialogue/store/input.js';
// 导入表单组件
import {
    aggregationTableForm,
    aggregationExcelForm,
    catalogStructuredDataForm,
    catalogUnstructuredDataForm,
    catalogYbzForm,
    WsHeader,
} from '@/components/dialogue/components/fromComponents/index.js';
// 组件映射表
const formComponents = {
    cj1: markRaw(aggregationTableForm),
    cj2: markRaw(aggregationExcelForm),
    bm1: markRaw(catalogStructuredDataForm),
    bm2: markRaw(catalogYbzForm),
    bm3: markRaw(catalogUnstructuredDataForm),
    ws1: markRaw(WsHeader),
    ws2: markRaw(WsHeader),
};
import { useDialogInput } from '@/components/dialogue/hooks/useDialogInput.js';
const { closeHeader, toggleHeader, buildPromptMessage } = useDialogInput();
// 计算属性
const currentFormComponent = computed(
    () => formComponents[currentBusinessType.value] || formComponents.aggregationTable
);

/**
 * @description 获取表单数据
 */
const getFormData = val => {
    // 合并数据
    Object.assign(form, {
        ...form,
        ...val,
    });

    // 更新表单字段
    Object.keys(val).forEach(key => {
        if (Object.prototype.hasOwnProperty.call(form, key)) {
            form[key] = val[key];
        }
    });

    // 只有在头部表单打开时才实时更新提示词
    if (showHeaderFlog.value) {
        const promptMessage = buildPromptMessage();
        // 设置程序更新标志
        senderValue.value = promptMessage;
    }
};

function handleDeleteCard() {
    if (files.value.length === 0) {
        closeHeader();
    }
}
</script>

<style scoped lang="scss">
.header-self-wrap {
    display: flex;
    flex-direction: column;
    padding: 16px;

    .header-self-title {
        width: 100%;
        display: flex;
        height: 30px;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 12px;
        color: #1d2939;
        border-bottom: 1px solid #eaeaea;
        margin-bottom: 12px;
        font-weight: 500;
        font-size: 14px;
        color: #1d2939;
        line-height: 18px;
    }

    .header-self-content {
        flex: 1;
    }

    .no-form-tip {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #909399;
        font-size: 14px;
    }
}

.file-header {
    padding: 0 10px;
}
</style>

<template>
    <!-- 标题 -->
    <h2 v-show="hideSelectModle && !showHeaderFlog" class="dialogue-input-title">
        <div class="business-type-selector">
            <el-dropdown v-if="hasSelectOptions" @command="handleSelectOptionChange" trigger="click" placement="top">
                <span class="el-dropdown-link">
                    {{ currentBusinessTypeName }}
                    <el-icon class="el-icon--right">
                        <ArrowDown />
                    </el-icon>
                </span>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item
                            v-for="(option, index) in currentSelectOptions"
                            :key="index"
                            :command="option.value"
                        >
                            {{ option.label }}
                        </el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
            <span v-else>{{ currentBusinessTypeName }}</span>
            <!-- 智能问数业务类型提示 -->
            <span v-if="currentBusinessType.includes('ws')" class="data-ask-tip">
                <el-icon><Paperclip /></el-icon>
                <span v-if="currentBusinessType.includes('ws2')">可使用"/"指令查看文件列表</span>
            </span>
        </div>
        <div class="flex items-center">
            <Icon name="expand" @click="openHeader" />
            <div class="divider-line"></div>
            <el-icon @click="handleClose" :size="16">
                <Close />
            </el-icon>
        </div>
    </h2>
</template>
<script setup>
import { computed } from 'vue';
import {
    hideSelectModle,
    showHeaderFlog,
    content,
    currentBusinessType,
    currentBusinessTypeName,
    updateDefaultWord,
} from '@/components/dialogue/store/input.js';
import { useDialogInput } from '@/components/dialogue/hooks/useDialogInput.js';

const { openHeader, handleClose } = useDialogInput();

// 判断当前是否有选项可选
const hasSelectOptions = computed(() => {
    return content.value.parent && content.value.parent.selectOption && content.value.parent.selectOption.length > 0;
});
/**
 * @description 处理选项变更
 * @param {String} value - 选中的选项值
 */
const handleSelectOptionChange = value => {
    if (!content.value.parent || !content.value.parent.child) return;
    const childItem = content.value.parent.child.find(child => child.value === value) || {};
    currentBusinessType.value = value;
    currentBusinessTypeName.value = `${content.value.parent.name}-${childItem.childName || ''}`;
    content.value.child = childItem;
    console.log(value);
    if (value === 'ws1') {
        updateDefaultWord('请输入信息查询指标');
    }
    if (value === 'ws2') {
        updateDefaultWord('输入/选择历史模版');
    }
    if (value === 'ws3') {
        updateDefaultWord('请输入信息查询大屏看板');
    }
    // senderValue.value = '/';
};
// 当前可选的选项列表
const currentSelectOptions = computed(() => {
    if (!content.value.parent || !content.value.parent.selectOption) {
        return [];
    }
    console.log(content.value.parent.selectOption);
    return content.value.parent.selectOption;
});
</script>
<style scoped lang="scss">
.dialogue-input-title {
    display: flex;
    margin-bottom: 10px;
    transition: opacity 0.3s;
    justify-content: space-between;
    .business-type-selector {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #475467;
        .el-dropdown-link {
            cursor: pointer;
            display: flex;
            align-items: center;
            color: #333;
            font-weight: bold;
            font-size: 14px;

            &:hover {
                color: #409eff;
            }
        }

        .data-ask-tip {
            margin-left: 10px;
            font-size: 12px;
            color: #909399;
            display: flex;
            align-items: center;
            background-color: #f0f9ff;
            padding: 2px 8px;
            border-radius: 12px;
            border: 1px solid #e1f3ff;

            .el-icon {
                margin-right: 4px;
                font-size: 12px;
            }
        }
    }
    p {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;

        i {
            margin: 0 5px;
            color: #dcdfe6;
        }

        .el-icon {
            cursor: pointer;
            font-size: 16px;
            color: #606266;

            &:hover {
                color: #409eff;
            }
        }
    }
}
</style>

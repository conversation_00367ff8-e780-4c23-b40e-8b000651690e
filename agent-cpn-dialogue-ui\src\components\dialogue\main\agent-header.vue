<template>
    <!-- 头部 -->
    <div class="content">
        <!-- 左侧控制区域 -->
        <div class="header-left">
            <!-- 展开收起 -->
            <div class="ml-2">
                <Icon :name="isTreeShow ? 'tree-zhankai' : 'tree-shousuo'" @click="toggleTree" size="22" />
            </div>
            <div
                class="title-editing-container p-4px w-fit max-w-full flex items-center justify-start cursor-pointer select-none hover:bg-[rgba(0,0,0,.04)] cursor-pointer rounded-md font-size-14px"
                @click="handleClickTitle"
                v-if="currentSession?.title"
            >
                <div class="w-100px text-overflow select-none pr-8px">
                    {{ currentSession?.title || '' }}
                </div>
                <SvgIcon name="bianji" size="14" class="flex-none c-gray-500" />
            </div>
        </div>

        <div class="container_agent_header flex-y-center">
            <div class="header_user" v-if="token">
                <el-tooltip
                    :content="isSelectionMode ? '退出选择模式' : '进入选择模式'"
                    placement="bottom"
                    v-if="currentBusinessType.includes('ws')"
                >
                    <Icon name="wdp" @click="toggleSelectionMode" class="selection-toggle-btn" size="20" />
                </el-tooltip>
                <img class="top-bar__img" :src="userStore.avatar" @click="userStore.toCeshi" />
                <el-dropdown>
                    <span class="el-dropdown-link">
                        {{ userStore.userInfo.real_name }}
                        <el-icon class="el-icon--right">
                            <arrow-down />
                        </el-icon>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item @click="userStore.toIndex">
                                {{ '首页' }}
                            </el-dropdown-item>
                            <el-dropdown-item>
                                <router-link to="/info/index">{{ $t('navbar.userinfo') }}</router-link>
                            </el-dropdown-item>
                            <el-dropdown-item>
                                <router-link to="/vform/index">{{ '后台管理' }}</router-link>
                            </el-dropdown-item>
                            <el-dropdown-item @click="userStore.logout">
                                {{ '退出登录' }}
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
            <div class="user-info" v-else @click="userStore.login">
                <span>立即登录</span>
                <img class="avatar" :src="deFaultAvatar" alt="avatar" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { ElMessageBox, ElMessage } from 'element-plus';
import { isTreeShow, toggleTree } from '@/components/dialogue/store/main';
import {
    isSelectionMode,
    toggleSelectionMode as toggleSelection,
    toggleSelectAll,
    isMessageSelected,
} from '@/components/dialogue/store/input';
import { useSessionStore } from '@/components/dialogue/store/modules/session';
import { useUserStore } from '@/components/dialogue/store/modules/user';
import SvgIcon from '@/components/dialogue/common/Icon/SvgIcon.vue';
import { publicPath } from '@/components/dialogue/store/main.js';
import { updateHistory } from '@/api/dialogue/index.js';
import deFaultAvatar from '/static/dialogue/indexpeople.png';
import { currentBusinessType } from '@/components/dialogue/store/input.js';
const sessionStore = useSessionStore();
const userStore = useUserStore();
const currentSession = computed(() => sessionStore.currentSession);
const dialogueId = computed(() => sessionStore.dialogueId);
const token = computed(() => userStore.token);

defineOptions({
    name: 'AgentHeader',
});

const router = useRouter();
const store = useStore();
// const { t } = useI18n();

// 计算是否有消息
const hasMessages = computed(() => {
    return sessionStore.messageList && sessionStore.messageList.length > 0;
});

// 计算是否全选
const isAllMessagesSelected = computed(() => {
    if (!hasMessages.value) return false;
    return sessionStore.messageList.every(message => isMessageSelected(message.id));
});

// 切换选择模式
const toggleSelectionMode = () => {
    toggleSelection();
};

// 处理全选/取消全选
const handleSelectAll = () => {
    toggleSelectAll(sessionStore.messageList);
};
const truncateText = (text, maxLength = 20) => {
    if (!text) return ''; // 处理空值
    // 计算字符长度（中文等全角字符按1个字符计算）
    return text.length > maxLength ? text.slice(0, maxLength) : text;
};
/**
 * @description 处理点击标题编辑事件
 */
function handleClickTitle() {
    // 检查是否有当前会话
    if (!currentSession.value?.title) {
        return;
    }

    // 检查是否有对话ID
    if (!dialogueId.value) {
        return;
    }

    ElMessageBox.prompt('', '编辑对话名称', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputErrorMessage: '请输入对话名称',
        confirmButtonClass: 'el-button--primary',
        cancelButtonClass: 'el-button--info',
        roundButton: true,
        inputValue: truncateText(currentSession.value?.title),

        inputValidator: value => {
            if (!value || value.trim() === '') {
                return '对话名称不能为空';
            }
            if (value.length > 21) {
                return `请将此文本缩短到20个字符或更少(目前使用了${value.length}个字符)`;
            }
            return true;
        },
        inputPlaceholder: '请输入对话名称',
    })
        .then(async ({ value }) => {
            try {
                const trimmedValue = value.trim();

                // 更新会话
                await sessionStore.updateSession(dialogueId.value, trimmedValue);
            } catch (error) {
                console.error('更新对话标题失败:', error);
            }
        })
        .catch(() => {
            // 用户取消编辑，不需要显示错误信息
        });
}
</script>

<style lang="scss" scoped>
.title-editing-container {
    transition: all 0.3s ease;
    // font-weight: 700;
    &:hover {
        .svg-icon {
            display: block;
            opacity: 1;
        }
    }
    .svg-icon {
        display: none;
        opacity: 0.5;
        transition: all 0.3s ease;
    }
}
.content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e7e7e7;
    padding: 8px 16px;
    height: 100%;
    background-color: #fff;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-center {
    display: flex;
    align-items: center;
}

.selection-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.selection-count {
    font-size: 14px;
    color: #606266;
    white-space: nowrap;
}

.selection-toggle-btn {
    cursor: pointer;
}

.header_logo {
    font-size: 38px;
    color: #0d58d2;
    font-weight: bold;
    font-style: oblique;
}
.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;

    span {
        font-size: 14px;
        color: #1570ef;
    }

    .avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
    }
}
</style>

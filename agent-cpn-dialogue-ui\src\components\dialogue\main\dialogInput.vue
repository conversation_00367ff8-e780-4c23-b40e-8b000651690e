<template>
    <!-- 命令列表 -->
    <common-TemplateFile
        v-if="isShowFileList"
        :search-keyword="senderValue.replace('/', '')"
        @select-file="handleFileSelect"
        @select-additional-content="handleAdditionalContentSelect"
    ></common-TemplateFile>

    <div class="dialogue-input-container">
        <!-- 输入框区域 -->
        <!-- 菜单快捷入口 -->
        <agentSimpleTree
            v-if="isShowSimpleTree"
            @getSelectData="getSelectData"
            class="agent-simple-tree"
        ></agentSimpleTree>

        <Sender
            ref="senderRef"
            v-model="senderValue"
            :auto-size="{ minRows: 2, maxRows: 5 }"
            allow-speech
            :placeholder="defaultWord"
            :submit-btn-disabled="isDisabled"
            :trigger-strings="['/', '@']"
            @trigger="onTrigger"
            :loading="isLoading"
            @submit="sendMessage"
            @cancel="cancelConnect"
        >
            <template #header>
                <SenderHeader />
            </template>

            <template #prefix>
                <SenderPrefix />
            </template>

            <template #action-list>
                <SenderActionList @sendMessage="sendMessage" />
            </template>
        </Sender>
        <!-- <EditorSender ref="editorRef" @change="handleChange" @submit="handleSubmit" @cancel="handleCancel">
            <template #header>
                <SenderHeader />
            </template>
            <template #footer>
                <SenderActionList @sendMessage="sendMessage" />
            </template>
        </EditorSender> -->
        <!-- <el-button @click="setMixTags" type="primary" class="w-4 h-4">cs</el-button>

        <Editor></Editor> -->
    </div>
</template>

<script setup>
const editorRef = useTemplateRef('editorRef');
function setMixTags() {
    editorRef.value?.setMixTags([
        [
            {
                type: 'gridInput',
                value: '这是第一行，请根据以下文案内容绘制一张图片：',
            },
            {
                type: 'inputTag',
                key: 'content',
                placeholder: '[文案内容]',
                value: '太阳由那扇大玻璃窗透入屋内，先是落在墙上，接着映照到桌上，最终，也照到了我那可爱的小床上来咯',
            },
            {
                type: 'gridInput',
                value: '。风格是',
            },
            {
                type: 'selectTag',
                key: 'style',
                value: '1',
            },
            {
                type: 'gridInput',
                value: '，画面内是',
            },
            {
                type: 'inputTag',
                key: 'content',
                placeholder: '[画面内容]',
                value: '光从大落地窗照进房间内，照在墙面、地板、桌子、床上',
            },
            {
                type: 'gridInput',
                value: '。画面主体要突出，画面的色彩搭配和整体氛围要贴合文案所围绕的主题。',
            },
        ],
        [
            {
                type: 'gridInput',
                value: '这是第二行。',
            },
        ],
        [
            {
                type: 'gridInput',
                value: '这是第三行。',
            },
            {
                type: 'htmlTag',
                value: '<img class="img-tag" src="https://cdn.element-plus-x.com/element-plus-x.png" alt="">',
            },
        ],
    ]);
}
/**
 * @description 对话输入组件，基于Element Plus X的Sender组件实现
 */
import { watch, nextTick, onMounted, useTemplateRef } from 'vue';
import Editor from './1.vue';
defineOptions({
    name: 'DialogInput',
});
import { ElMessage } from 'element-plus';
import agentSimpleTree from '@/components/dialogue/components/agent-simple-tree.vue';
import SenderPrefix from './SenderPrefix.vue';
import SenderHeader from './SenderHeader.vue';
import SenderActionList from './SenderActionList.vue';

import {
    senderValue,
    senderRef,
    isShowSimpleTree,
    isShowFileList,
    hideSelectModle,
    defaultWord,
    currentBusinessType,
    files,
    isLoading,
    isDisabled,
    currentFiles,
} from '@/components/dialogue/store/input.js';
import { useDialogInput } from '@/components/dialogue/hooks/useDialogInput.js';

const emit = defineEmits(['sendMessage']);

const { closeHeader, initInputContent, buildPromptMessage, getSelectData } = useDialogInput();
const dialogueHandler = useDialogueHandler();

/**
 * @description 发送消息
 */
const sendMessage = () => {
    if (!senderValue.value) return;

    let messageToSend = hideSelectModle.value ? buildPromptMessage() : senderValue.value;

    // 只过滤掉消息开头的"@"和"/"字符
    messageToSend = messageToSend.replace(/^@/, '');
    messageToSend = messageToSend.replace(/^\//, '');
    messageToSend = messageToSend.trim();

    if (!messageToSend.trim()) return;
    // 先保存文件列表，再清空
    currentFiles.value = files.value;
    initInputContent();

    emit('sendMessage', messageToSend, files.value);
};
const cancelConnect = () => {
    dialogueHandler.disconnectAllConnections();
};

/**
 * @description 处理文件选择事件
 * @param {Object} file - 选中的文件对象
 */
const handleFileSelect = file => {
    // 关闭文件列表
    isShowFileList.value = false;

    // 设置输入框内容为固定模板
    senderValue.value = `帮我调用最新指标数据填充到模版里`;

    nextTick(() => {
        senderRef.value.focus('end');
    });
};
/**
 * @description 处理附加内容选择事件
 * @param {String} content - 附加内容
 */
const handleAdditionalContentSelect = content => {
    // 关闭文件列表
    isShowFileList.value = false;

    // 设置输入框内容为附加内容
    senderValue.value = content;

    // 使用nextTick确保DOM更新后再聚焦
    nextTick(() => {
        senderRef.value.focus('end');
    });
};

/**
 * @description 处理触发事件
 */
const onTrigger = event => {
    console.log('触发事件:', event.oldValue);

    // 确保只有一种弹窗显示
    if (event.triggerString === '@' && event.isOpen) {
        isShowSimpleTree.value = true;
        isShowFileList.value = false;
    } else if (event.triggerString === '@' && !event.isOpen) {
        isShowSimpleTree.value = false;
    } else if (event.triggerString === '/' && event.isOpen) {
        // 只有在选中智能问数相关业务类型时才显示文件列表
        if (currentBusinessType.value.includes('ws')) {
            isShowFileList.value = true;
            isShowSimpleTree.value = false;
        } else if (!currentBusinessType.value.includes('ws')) {
            // 如果不是智能问数业务类型，提示用户
            ElMessage.warning('只有选择智能问数相关业务类型才能使用此功能');
            // 阻止显示文件列表
            isShowFileList.value = false;
        }
    } else if (event.triggerString === '/' && !event.isOpen) {
        // 关闭指令弹窗
        isShowFileList.value = false;
    }
};

/**
 * @description 监听输入框内容变化，动态控制文件列表和头部区域显示
 */
watch(senderValue, (val, old) => {
    const isWsType = currentBusinessType.value.includes('ws');
    // 输入框清空时处理
    if (val === '' && !isWsType) {
        closeHeader();

        return;
    }

    // 避免在程序更新时重新聚焦
    if (old !== undefined && val !== old) {
        // 使用nextTick确保DOM更新后再聚焦
        nextTick(() => {
            senderRef.value.focus('end');
        });
    }
});
onMounted(() => {
    initInputContent();
});
</script>

<style scoped lang="scss">
.dialogue-input-container {
    display: flex;
    flex-direction: column;
    position: relative;
}
.agent-simple-tree {
    position: absolute;
    bottom: 120px;
    left: 0;
    z-index: 10;
}
:deep(.el-sender) {
    .el-sender-content {
        display: flex;
        flex-direction: column;
    }
    .el-sender-prefix {
        width: 100%;
    }
    .el-sender-action-list {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}

.divider-line {
    width: 2px;
    height: 16px;
    background-color: #dcdfe6;
    margin: 0 10px;
}
</style>

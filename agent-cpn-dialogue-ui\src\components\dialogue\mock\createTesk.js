// 采集
const configCase2 = {
    texts: {
        source: '数据源',
        table: '数据表',
        range: '数据范围',
        targetSource: '方式汇聚到',
        targetTable: '数据表',
        emptyTip: '请补全信息',
    },
    fieldOrder: ['source', 'table', 'range', 'targetSource', 'targetTable'],
    sources: [{ label: '源1', value: 'source1' }],
    tables: [{ label: '表1', value: 'table1' }],
    ranges: [{ label: '全量', value: 'all' }],
    targetSources: [{ label: '目标源', value: 'target1' }],
    targetTables: [{ label: '目标表', value: 'targetTable1' }],
    placeholders: {
        source: '源名',
        table: '表名',
        range: '全量',
        targetSource: '目标源',
        targetTable: '目标表',
    },
};
const configCase1 = {
    texts: {
        category: '资源目录是',
        categorySuffix: '类别，',
        system: '应用系统',
        systemSuffix: '，',
        service: '服务',
        serviceSuffix: '。',
        emptyTip: '请选择完整信息',
    },
    fieldOrder: ['category', 'system', 'service'],
    categories: [{ label: '接口数据', value: 'api-data' }],
    systems: [{ label: '系统A', value: 'system-a' }],
    services: [{ label: '服务A', value: 'service-a' }],
    placeholders: {
        category: '类别',
        system: '系统名',
        service: '服务名',
    },
};

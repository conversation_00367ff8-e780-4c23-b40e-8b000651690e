<script setup>
import { inject } from 'vue';
import { storeToRefs } from 'pinia';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
import { pageRightContainer } from '@/components/dialogue/index.js';

/**
 * @description 右侧面板组件，负责渲染当前选中的右侧内容
 */
defineOptions({
    name: 'RightPanel',
});

// 获取右侧面板状态
const rightPanelStore = useRightPanelStore();
const { currentComponent, componentProps, componentEvents, rightPanelTitle } = storeToRefs(rightPanelStore);
const { hideRightPanel } = rightPanelStore;

// 获取组件映射表
const componentMap = inject('componentMap');
</script>

<template>
    <div class="container_agent_qa_box_r">
        <pageRightContainer :title="rightPanelTitle" :icons="componentProps.icons">
            <!-- 使用动态组件渲染右侧内容 -->
            <component
                :is="componentMap[currentComponent]"
                v-bind="componentProps"
                @handleHide="hideRightPanel"
                v-on="componentEvents"
            ></component>
        </pageRightContainer>
    </div>
</template>

<style lang="scss" scoped>
.container_agent_qa_box_r {
    height: 100%;
    display: flex;
    flex-direction: column;
}
</style>

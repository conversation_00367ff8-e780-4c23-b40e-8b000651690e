<template>
    <div class="container_agent">
        <div class="container_agent_content">
            <!-- 主区域 -->
            <div class="container_agent_main">
                <div class="chat-list">
                    <!-- ======================循环问答模块区域====================== -->

                    <div class="customer_question inner_scroll_znkf">
                        <!-- 数据编目 -->
                        <div class="agent_qa_title"></div>

                        <!-- ----------------初始默认显示--------------- -->
                        <pageAgentAnswerDefault></pageAgentAnswerDefault>

                        <!-- --------------初始默认显示 end--------------- -->
                    </div>
                    <!-- =====================输入框区域===================== -->
                    <dialogueInput @sendMessage="handleSendMessage"></dialogueInput>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// 从index.js导入组件
import { dialogueInput, pageAgentAnswerDefault } from '@/components/dialogue/index.js';

import { useDialogueHandler } from '@/components/dialogue/hooks/useDialogueHandler';
import { isFontTest } from '@/components/dialogue/store/main';
import { useSessionStore } from '@/components/dialogue/store/modules/session';

// 使用对话处理钩子
const { sendMessage } = useDialogueHandler();
const sessionStore = useSessionStore();

const router = useRouter();

/**
 * @description 处理发送消息,生成对话ID并跳转到对话页面
 */
const handleSendMessage = async message => {
    localStorage.setItem('chatContent', message);
    if (!isFontTest) {
        try {
            // 直接传递sendMessage函数作为回调
            sessionStore.setCurrentSession({
                title: message,
            });
            await sessionStore.createSessionList(message);
        } catch (error) {
            console.error('发送消息失败:', error);
        }
    } else {
        sendMessage(message);
        router.replace({
            name: 'chatWithId',
            params: { id: 123 },
        });
    }
};
onMounted(() => {
    // 重置会话内容
    sessionStore.setCurrentSession({});
    sessionStore.requestSessionList();
});
</script>

<style lang="scss" scoped>
.container_agent_main {
    display: flex;
    justify-content: center;
    transition: all 0.3s ease-in-out;
    position: relative;
    padding: 10px;
    .chat-list {
        flex: 1;
        padding: 0 320px;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .customer_question {
            flex: 1;
            height: 0;
            overflow: auto;
            overflow-x: hidden;
            margin-bottom: 15px;
            // padding-right: 12px;
        }
    }
}

// 隐藏滚动条但保留滚动功能
// .inner_scroll_box_znkf,
// .inner_scroll_znkf {
//     -ms-overflow-style: none; /* IE 和 Edge */
//     scrollbar-width: none; /* Firefox */

//     &::-webkit-scrollbar {
//         display: none; /* Chrome, Safari 和 Opera */
//     }
// }

// 确保整个容器也不显示滚动条
// .customer_question {
//     overflow-y: auto;
//     -ms-overflow-style: none;
//     scrollbar-width: none;

//     &::-webkit-scrollbar {
//         display: none;
//     }
// }
</style>

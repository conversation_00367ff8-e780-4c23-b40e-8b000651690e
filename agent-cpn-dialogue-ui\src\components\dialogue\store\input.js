import { ref, reactive, computed, watch, nextTick, useTemplateRef } from 'vue';

export const DEFAULT_PROMPT = '请输入您想提的问题，@你想要的技能，使用各种能力';
export const WS1_PROMPT = '请输入您想提的问题，帮助您查询指标';
export const DEFAULT_BUSINESS_TYPE = 'cj1';

// 状态变量
export const dialogueId = ref(null); // 对话ID
export const messageId = ref(null); // 消息ID
export const messageList = ref([]); // 消息列表
export const senderValue = ref('');
export const senderRef = ref(null);
export const dialogInputRef = ref(null);
export const agentTreeRef = ref(null);
export const showHeaderFlog = ref(false); // 是否展示对话头部
export const headerType = ref('template'); //展开头部显示的内容类型 template 模板  file 文件   image 图片
export const isDeepThink = ref(true); // 是否深度思考
export const isShowSimpleTree = ref(false); // 是否展示/简单树
export const isShowFileList = ref(false); // 是否展示/文件列表
export const hideSelectModle = ref(false); // 是否隐藏/选择模块
export const defaultWord = ref(DEFAULT_PROMPT); // 默认提示词
export const currentBusinessType = ref(DEFAULT_BUSINESS_TYPE); // 当前业务类型
export const content = ref({ parent: {}, child: {} }); // 当前业务类型内容
export const currentBusinessTypeName = ref('数据采集'); // 当前业务类型名称

export const isDisabled = ref(false);
// 上传的文件列表
export const files = ref([]);
// 当前文件列表 用于显示在消息框头部内容
export const currentFiles = ref([]);
// 输入框加载状态
export const isLoading = ref(false);
// 是否有活跃的WebSocket或SSE连接
export const hasActiveConnections = ref(false);

// 上传的文件的url  目前只支持单个文件上传
export const fileListUrl = ref(null);

// 初始化表单数据
export const form = reactive({
    // 基本数据
    sourceDataSourceName: '',
    sourceDatabase: '库名',
    sourceTable: '表名',
    targetDataSource: '源名',
    targetDatabase: '库名',
    targetTable: '表名',
    // 汇聚配置
    aggregationType: '全量',
    incrementMechanism: '按标识增量',
    sourceIdentifier: '字段',
    pendingIdentifier: '标识',
    aggregatedIdentifier: '标识',
    // 结构化数据目录字段
    resourceTitle: '企业基本信息目录对话生成',
    updateFrequency: '每日',
    resourceCategory: '部门信息资源',
    baseCategory: '社会信用',
    topicCategory: '综合政务',
    department: '大数据局',
    shareType: '不共享',
    openType: '不开放',
    shareCondition: '',
    shareReason: '',
    shareBasis: '',
    shareMethod: '',
    shareScope: '',
    // 文件相关
    fileName: '/上传文件',
    fileType: '数据一本帐',
});

/**
 * 菜单树形数据
 */
export const menuList = reactive([
    {
        name: '数据采集',
        icon: 'cj',
        child: [
            {
                childName: '采集数据到数据表',
                icon: 'cj1',
                value: 'cj1',
            },
            // 因中期验收需要，先屏蔽以下功能菜单
            // {
            //     childName: '上传数据采集到系统库表',
            //     icon: 'cj2',
            //     value: 'cj2',
            // },
        ],
    },
    {
        name: '数据编目',
        icon: 'sjbm',
        child: [
            {
                childName: '系统信息资源生成目录',
                icon: 'ml1',
                value: 'bm1',
            },
            // 因中期验收需要，先屏蔽以下功能菜单
            // {
            //     childName: '上传数据一本帐文件生成资源目录',
            //     icon: 'ml2',
            //     value: 'bm2',
            // },
            // {
            //     childName: '上传非结构化数据文件生成资源目录',
            //     icon: 'ml3',
            //     value: 'bm3',
            // },
        ],
    },
    {
        name: '智能问数',
        icon: 'sjcx',
        child: [
            {
                childName: '政务数据助手',
                agentId: 6,
                icon: 'wzb',
                value: 'zs1',
            },
            {
                childName: '固投数据助手',
                agentId: 7,
                icon: 'wzb',
                value: 'zs2',
            },
            // {
            //     childName: '问指标',
            //     icon: 'wzb',
            //     value: 'ws1',
            // },
            {
                childName: '模板问数',
                icon: 'mbws',
                value: 'ws2',
            },
            // {
            //     childName: '问大屏看板',
            //     icon: 'wdp',
            //     value: 'ws3',
            // },
        ],
    },
    //因中期验收需要，先屏蔽以下功能菜单
    // {
    //     name: '智能共享',
    //     icon: 'zngx',
    //     child: [],
    // },
    // {
    //     name: '数据画像',
    //     icon: 'sjhx',
    //     child: [],
    // },
    // {
    //     name: '数据打标',
    //     icon: 'sjdb',
    //     child: [],
    // },
    // {
    //     name: '数据治理',
    //     icon: 'sjzl',
    //     child: [],
    // },
]);
watch(showHeaderFlog, v => {
    if (v) {
        senderRef.value.openHeader();
    } else {
        senderRef.value.closeHeader();
    }
});

/**
 * 对话选择功能状态管理
 * 管理选择模式的开启/关闭以及已选择的消息列表
 */

// 选择模式是否开启
const isSelectionMode = ref(false);

// 已选择的消息ID集合
const selectedMessages = ref(new Set());

/**
 * 修改提示文字
 */
const updateDefaultWord = value => {
    defaultWord.value = value;
};
/**
 * 切换选择模式
 */
const toggleSelectionMode = () => {
    isSelectionMode.value = !isSelectionMode.value;
    // 关闭选择模式时清空已选择的消息
    if (!isSelectionMode.value) {
        selectedMessages.value.clear();
    }
};

/**
 * 开启选择模式
 */
const enableSelectionMode = () => {
    isSelectionMode.value = true;
};

/**
 * 关闭选择模式
 */
const disableSelectionMode = () => {
    isSelectionMode.value = false;
    selectedMessages.value.clear();
};

/**
 * 选择消息
 * @param {string} messageId 消息ID
 */
const selectMessage = messageId => {
    selectedMessages.value.add(messageId);
    // 触发响应式更新
    selectedMessages.value = new Set(selectedMessages.value);
};

/**
 * 取消选择消息
 * @param {string} messageId 消息ID
 */
const unselectMessage = messageId => {
    selectedMessages.value.delete(messageId);
    // 触发响应式更新
    selectedMessages.value = new Set(selectedMessages.value);
};

/**
 * 切换消息选择状态
 * @param {string} messageId 消息ID
 */
const toggleMessageSelection = messageId => {
    if (selectedMessages.value.has(messageId)) {
        unselectMessage(messageId);
    } else {
        selectMessage(messageId);
    }
};

/**
 * 检查消息是否被选中
 * @param {string} messageId 消息ID
 * @returns {boolean}
 */
const isMessageSelected = messageId => {
    return selectedMessages.value.has(messageId);
};

/**
 * 全选所有消息
 * @param {Array} messageList 消息列表
 */
const selectAllMessages = messageList => {
    messageList.forEach(message => {
        if (message.id) {
            selectedMessages.value.add(message.id);
        }
    });
    // 触发响应式更新
    selectedMessages.value = new Set(selectedMessages.value);
};

/**
 * 取消全选
 */
const unselectAllMessages = () => {
    selectedMessages.value.clear();
    // 触发响应式更新
    selectedMessages.value = new Set(selectedMessages.value);
};

/**
 * 切换全选状态
 * @param {Array} messageList 消息列表
 */
const toggleSelectAll = messageList => {
    if (isAllSelected.value) {
        unselectAllMessages();
    } else {
        selectAllMessages(messageList);
    }
};

/**
 * 获取已选择的消息数量
 */
const selectedCount = computed(() => {
    return selectedMessages.value.size;
});

/**
 * 检查是否有消息被选中
 */
const hasSelectedMessages = computed(() => {
    return selectedMessages.value.size > 0;
});

/**
 * 检查是否全选（需要传入消息列表进行比较）
 */
const isAllSelected = computed(() => {
    // 这个计算属性需要在使用时传入消息列表进行比较
    return false; // 默认返回false，实际使用时需要在组件中重新计算
});

/**
 * 获取已选择的消息ID数组
 */
const selectedMessageIds = computed(() => {
    return Array.from(selectedMessages.value);
});

/**
 * 清空选择
 */
const clearSelection = () => {
    selectedMessages.value.clear();
    // 触发响应式更新
    selectedMessages.value = new Set(selectedMessages.value);
};

/**
 * 删除选中的消息
 * @param {Array} messageList 消息列表
 * @returns {Array} 过滤后的消息列表
 */
const deleteSelectedMessages = messageList => {
    const filteredList = messageList.filter(message => !selectedMessages.value.has(message.id));
    clearSelection();
    return filteredList;
};

export {
    // 状态
    isSelectionMode,
    selectedMessages,
    // 计算属性
    selectedCount,
    hasSelectedMessages,
    isAllSelected,
    selectedMessageIds,

    // 方法
    toggleSelectionMode,
    enableSelectionMode,
    disableSelectionMode,
    selectMessage,
    unselectMessage,
    toggleMessageSelection,
    isMessageSelected,
    selectAllMessages,
    unselectAllMessages,
    toggleSelectAll,
    clearSelection,
    deleteSelectedMessages,
    updateDefaultWord,
};

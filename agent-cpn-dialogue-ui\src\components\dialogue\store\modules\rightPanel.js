import { defineStore } from 'pinia';
import { ref } from 'vue';
import { getHighlighter } from 'shikiji';

/**
 * @description 右侧面板状态管理 Store
 * 使用 Pinia 实现跨组件通信，替代原有的 provide/inject 模式
 */
export const useRightPanelStore = defineStore('rightPanel', () => {
    // 基础状态
    const isRightVisible = ref(false);
    const currentComponent = ref(null);
    const componentProps = ref({});
    const rightPanelTitle = ref('详细信息');
    const componentEvents = ref({});

    // 语法高亮相关
    const highlighter = ref(null);

    /**
     * @description 设置右侧面板状态
     * @param {String} component - 要渲染的组件名称
     * @param {Object} props - 要传递给组件的属性
     * @param {Object} [events] - 要绑定的事件处理函数
     */
    const setRightPanel = (component, props = {}, events = {}) => {
        currentComponent.value = component;
        componentProps.value = props;
        componentEvents.value = events;
        isRightVisible.value = true;

        // 尝试从不同位置获取标题
        if (props.title) {
            rightPanelTitle.value = props.title;
        } else if (props.config && props.config.title) {
            rightPanelTitle.value = props.config.title;
        }
    };

    /**
     * @description 隐藏右侧面板
     */
    const hideRightPanel = () => {
        isRightVisible.value = false;
    };

    /**
     * @description 更新右侧面板标题
     * @param {String} title - 新标题
     */
    const updatePanelTitle = title => {
        rightPanelTitle.value = title;
    };

    /**
     * @description 触发组件事件
     * @param {String} eventName - 事件名称
     * @param {...any} args - 事件参数
     * @returns {Boolean} 是否成功触发事件
     */
    const triggerComponentEvent = (eventName, ...args) => {
        if (componentEvents.value && typeof componentEvents.value[eventName] === 'function') {
            componentEvents.value[eventName](...args);
            return true;
        }
        console.log('未找到事件处理函数:', eventName);
        return false;
    };

    /**
     * @description sql高亮实例配置
     * @param {Array} themes - 主题列表
     * @param {Array} langs - 语言列表
     */
    const setHighlighter = async (themes = ['github-light'], langs = ['sql']) => {
        highlighter.value = await getHighlighter({
            themes,
            langs,
        });
    };

    /**
     * @description 将代码转换为高亮HTML
     * @param {String} code - 代码
     * @param {String} lang - 语言
     * @param {String} theme - 主题
     * @returns {String} 高亮后的HTML
     */
    const setHighligCodeToHtml = (code, lang = 'sql', theme = 'github-light') => {
        if (!highlighter.value || !code) return '';
        return highlighter.value.codeToHtml(code, {
            lang,
            theme,
        });
    };

    /**
     * @description 高亮SQL代码
     * @param {String} code - SQL代码
     * @returns {String} 高亮后的HTML
     */
    const highlightCode = code => {
        return setHighligCodeToHtml(code);
    };

    /**
     * @description 重置状态
     */
    const resetState = () => {
        isRightVisible.value = false;
        currentComponent.value = null;
        componentProps.value = {};
        rightPanelTitle.value = '详细信息';
        componentEvents.value = {};
    };

    return {
        // 状态
        isRightVisible,
        currentComponent,
        componentProps,
        rightPanelTitle,
        componentEvents,
        highlighter,

        // 方法
        setRightPanel,
        hideRightPanel,
        updatePanelTitle,
        triggerComponentEvent,
        setHighlighter,
        highlightCode,
        resetState,
    };
});

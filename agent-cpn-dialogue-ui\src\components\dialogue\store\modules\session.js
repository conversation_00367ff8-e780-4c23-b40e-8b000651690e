import { defineStore } from 'pinia';
import { useUserStore } from './user';
import { isDeepThink, senderValue, senderRef, isLoading } from '@/components/dialogue/store/input.js';
import { generateDialogueMsg } from '@/api/dialogue';
// 移除这里的导入，避免循环依赖
import { getSessionList, getHistoryList, deleteHistory, updateHistory } from '@/api/dialogue/index.js';
import { useRightPanelStore } from '@/components/dialogue/store/modules/rightPanel';
export const useSessionStore = defineStore('session', () => {
    const router = useRouter();
    const userStore = useUserStore();
    const inputStore = useDialogInput();
    const rightPanelStore = useRightPanelStore();
    //高亮的历史记录
    const active = ref('');
    // 历史记录列表
    const sessionList = ref([]);
    // 当前选中的会话ID
    const dialogueId = ref(null);
    // 当前选中的会话消息ID
    const messageId = ref(null);
    // 当前选中的会话消息列表
    const messageList = ref([]);
    // 当前选中的会话信息
    const currentSession = ref({});

    const isThinking = ref(true); // 系统是否已经拿到渲染内容

    // 设置当前会话
    const setCurrentSession = session => {
        currentSession.value = session;
    };
    const setIsThinking = session => {
        isThinking.value = session;
    };

    const setDialogueId = id => {
        dialogueId.value = id;
    };
    const setActive = id => {
        active.value = id;
    };
    const setMessageId = id => {
        messageId.value = id;
    };
    const setCurrentMessageList = list => {
        messageList.value = list;
    };

    // 会议ID对应-本地保存聊天记录 map对象
    const chatMap = ref({});
    const setChatMap = (id, data) => {
        chatMap.value[id] = data;
    };
    // 获取当前会话的聊天记录
    const requestChatList = async sessionId => {
        // 如果没有 token 则不查询聊天记录
        if (!userStore.token) return;
        console.log(sessionId, 'sessionId');
        try {
            const { content: data } = await getSessionList({ dialogueId: sessionId });
            setChatMap(sessionId, data);
            // 请求聊天记录后，赋值回显
            // setCurrentMessageList(chatMap.value[`${sessionId}`]);
            setCurrentMessageList(data);
        } catch (error) {
            console.error('getChatList:', error);
        }
    };
    // 创建新对话（按钮点击）
    const createSessionBtn = async path => {
        // 关闭右侧面板
        rightPanelStore.hideRightPanel();

        clearSession();
        inputStore.closeHeader();
        const targetRoute = path ? { name: path } : { name: 'chat' };
        router.replace(targetRoute).catch(error => {
            // 路由跳转错误处理
            console.error('createSessionBtn路由跳转错误:', error);
        });
    };
    function clearSession() {
        messageList.value = [];
        dialogueId.value = null;
        messageId.value = null;
        currentSession.value = null;
        // 初始化加载状态
        isLoading.value = false;
    }
    // 获取会话列表
    const requestSessionList = async day => {
        try {
            const params = {};
            if (!userStore.token) return;
            // 如果没有 token 则不查询聊天记录
            const { data, code } = await getHistoryList(day);
            sessionList.value = data;
            // const allSessions = new Map(sessionList.value.map(item => [item.createUser, item])); // 现有所有数据
            // res.forEach(item => allSessions.set(item.createUser, { ...item })); // 更新/添加数据
        } catch {}
        1;
    };
    const setDialogIdMessageId = async message => {
        const params = {
            dialogueId: dialogueId.value,
            msg: message,
        };

        const { data: res } = await generateDialogueMsg(params);
        if (res.code !== 200) return;

        const { dialogueId: newDialogueId, messageId: newMessageId } = res.data;
        dialogueId.value = newDialogueId;
        messageId.value = newMessageId;
    };
    // 发送消息后创建新会话
    const createSessionList = async (message, sendMessageCallback) => {
        try {
            await setDialogIdMessageId(message);
            // 跳转到对话页面
            router.replace({
                name: 'chatWithId',
                params: { id: dialogueId.value },
            });
        } catch (error) {
            console.error('createSessionList错误:', error);
        }
    };

    // 加载更多会话（供组件调用）
    const loadMoreSessions = async () => {};

    // 更新会话（供组件调用）
    const updateSession = async (id, title) => {
        try {
            await updateHistory({
                id,
                dialogueName: title,
            });

            // 刷新列表数据
            await requestSessionList();
            if (active.value === id) {
                setCurrentSession({
                    ...currentSession.value,
                    title,
                });
            }
        } catch (error) {
            console.error('updateSession错误:', error);
        }
    };

    // 删除会话（供组件调用）
    const deleteSessions = async id => {
        try {
            await deleteHistory({ dialogueId: id });
            // 刷新列表数据

            await requestSessionList();
        } catch (error) {
            console.error('deleteSessions错误:', error);
        }
    };
    return {
        // 会话ID和消息ID
        dialogueId,
        messageId,
        messageList,
        setCurrentMessageList,
        // 当前选中的会话
        currentSession,
        // 设置当前会话
        setCurrentSession,
        createSessionBtn,
        createSessionList,
        requestSessionList,
        loadMoreSessions,
        updateSession,
        deleteSessions,
        requestChatList,
        setChatMap,
        setDialogueId,
        // 暴露chatMap给组件使用
        chatMap,
        sessionList,
        clearSession,
        setDialogIdMessageId,
        setMessageId,
        setActive,
        active,
        setIsThinking,
        isThinking,
    };
});

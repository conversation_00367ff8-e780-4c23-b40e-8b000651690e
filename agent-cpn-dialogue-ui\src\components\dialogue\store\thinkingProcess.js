// src/store/thinkingProcess.js
import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useThinkingStore = defineStore('thinkingProcess', () => {
    // 思考内容
    const thinkingContent = ref('');
    // 执行结果列表
    const executeResults = ref([]);
    // SSE连接状态
    const isConnected = ref(false);

    /**
     * @description 初始化SSE连接
     * @param {string} url - SSE接口地址
     * @return {EventSource} 返回事件源对象以便在合适时机关闭
     */
    function setupSSEConnection(url) {
        const eventSource = new EventSource(url);

        eventSource.onopen = () => {
            isConnected.value = true;
        };

        eventSource.onmessage = event => {
            const json = JSON.parse(event.data);

            // 根据数据类型处理不同的更新
            if (json.type === 'thinking') {
                thinkingContent.value = json.content;
            } else if (json.type === 'execute') {
                executeResults.value.push(json.content);
            }
        };

        eventSource.onerror = () => {
            isConnected.value = false;
            closeConnection(eventSource);
        };

        return eventSource;
    }

    /**
     * @description 关闭SSE连接
     * @param {EventSource} eventSource - 要关闭的事件源
     */
    function closeConnection(eventSource) {
        if (eventSource && eventSource.readyState !== 2) {
            eventSource.close();
            isConnected.value = false;
        }
    }

    /**
     * @description 重置状态
     */
    function resetState() {
        thinkingContent.value = '';
        executeResults.value = [];
    }

    return {
        thinkingContent,
        executeResults,
        isConnected,
        setupSSEConnection,
        closeConnection,
        resetState,
    };
});

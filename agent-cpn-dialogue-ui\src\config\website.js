/**
 * 全局配置文件
 */
const title = '智能体对话模块';
export default {
    title: title,
    logo: 'I',
    key: 'Idgp', //配置主键,目前用于存储
    indexTitle: title,
    clientId: 'Idgp', // 客户端id
    clientSecret: 'idgp_a1de96da795c4e969748da35a79de787', // 客户端密钥
    tenantMode: false, // 是否开启租户模式
    tenantId: '000000', // 管理组租户编号
    captchaMode: true, // 是否开启验证码模式
    switchMode: false, // 是否开启部门切换模式
    codeLogin: true, // 是否开启使用手机号登录,使用手机号登录需要后端配置短信服务器
    lockPage: '/lock',
    tokenTime: 3000,
    tokenHeader: 'ark-Auth',
    currentTagKey: 'currentTag',
    //http的status默认放行不才用统一处理的,
    statusWhiteList: [],
    //配置首页不可关闭
    setting: {
        sidebar: 'vertical',
        tag: true,
        debug: false,
        collapse: true,
        search: true,
        lock: true,
        screenshot: true,
        fullscren: true,
        theme: false,
        menu: true,
    },
    fistPage: {
        name: '首页',
        path: '/index',
    },
    //配置菜单的属性
    menu: {
        iconDefault: 'icon-caidan',
        label: 'name',
        path: 'path',
        icon: 'source',
        children: 'children',
        query: 'query',
        href: 'path',
        meta: 'meta',
    },
    //auth配置
    auth: {
        // 使用后端工程 @org.springblade.test.Sm2KeyGenerator 获取
        publicKey:
            '04ae3795bc9ee8b74db2b8f9eb6785d29359cccb757b94575d045977cceb802a73ac8c58a07fa438274acbd99459aba8759de069791554c33d70594c4bce2e9199',
    },
    // 流程设计器类型(true->nutflow,false->flowable)
    designMode: true,
    // 流程设计器地址(flowable模式)
    designUrl: 'http://localhost:9999',
    // 第三方系统授权地址
    authUrl: 'http://localhost/blade-auth/oauth/render',
    // 报表设计器地址(cloud端口为8108,boot端口为80)
    reportUrl: 'http://localhost:8108/ureport',
    // 单点登录系统认证(blade-auth服务的地)
    ssoUrl: 'http://localhost:8100/oauth/authorize?client_id=saber&response_type=code&redirect_uri=',
    // 单点登录回调地址(Saber服务的登录界面地址)
    redirectUri: 'http://localhost:2888/login',
};

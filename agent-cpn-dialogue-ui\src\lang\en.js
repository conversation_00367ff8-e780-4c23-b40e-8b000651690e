export default {
    title: 'Saber Admin',
    logoutTip: 'Exit the system, do you want to continue?',
    submitText: 'submit',
    cancelText: 'cancel',
    search: 'Please input search content',
    menuTip: 'none menu list',
    common: {
        condition: 'condition',
        display: 'display',
        hide: 'hide',
    },
    tip: {
        select: 'Please select',
        input: 'Please input',
    },
    upload: {
        upload: 'upload',
        tip: 'Drag files here，/',
    },
    date: {
        start: 'Start date',
        end: 'End date',
        t: 'today',
        y: 'yesterday',
        n: 'nearly 7',
        a: 'whole',
    },
    form: {
        printBtn: 'print',
        mockBtn: 'mock',
        submitBtn: 'submit',
        emptyBtn: 'empty',
    },
    crud: {
        filter: {
            addBtn: 'add',
            clearBtn: 'clear',
            resetBtn: 'reset',
            cancelBtn: 'cancel',
            submitBtn: 'submit',
        },
        column: {
            name: 'name',
            hide: 'hide',
            fixed: 'fixed',
            filters: 'filters',
            sortable: 'sortable',
            index: 'index',
            width: 'width',
        },
        tipStartTitle: 'Currently selected',
        tipEndTitle: 'items',
        editTitle: 'edit',
        copyTitle: 'copy',
        addTitle: 'add',
        viewTitle: 'view',
        filterTitle: 'filter',
        showTitle: 'showTitle',
        menu: 'menu',
        addBtn: 'add',
        show: 'show',
        hide: 'hide',
        open: 'open',
        shrink: 'shrink',
        printBtn: 'print',
        excelBtn: 'excel',
        updateBtn: 'update',
        cancelBtn: 'cancel',
        searchBtn: 'search',
        emptyBtn: 'empty',
        menuBtn: 'menu',
        saveBtn: 'save',
        viewBtn: 'view',
        editBtn: 'edit',
        copyBtn: 'copy',
        delBtn: 'delete',
    },
    login: {
        title: 'Login ',
        info: 'BladeX Development Platform',
        tenantId: 'Please input tenantId',
        username: 'Please input username',
        password: 'Please input a password',
        wechat: 'Wechat',
        qq: 'QQ',
        github: 'github',
        gitee: 'gitee',
        phone: 'Please input a phone',
        code: 'Please input a code',
        submit: 'Login',
        userLogin: 'userLogin',
        phoneLogin: 'phoneLogin',
        thirdLogin: 'thirdLogin',
        ssoLogin: 'ssoLogin',
        msgText: 'send code',
        msgSuccess: 'reissued code',
    },
    navbar: {
        info: 'info',
        logOut: 'logout',
        userinfo: 'userinfo',
        switchDept: 'switch dept',
        dashboard: 'dashboard',
        management: 'management',
        lock: 'lock',
        bug: 'none bug',
        bugs: 'bug',
        screenfullF: 'exit screenfull',
        screenfull: 'screenfull',
        language: 'language',
        notice: 'notice',
        theme: 'theme',
        color: 'color',
    },
    tagsView: {
        search: 'Search',
        menu: 'menu',
        clearCache: 'Clear Cache',
        closeOthers: 'Close Others',
        closeAll: 'Close All',
    },
};

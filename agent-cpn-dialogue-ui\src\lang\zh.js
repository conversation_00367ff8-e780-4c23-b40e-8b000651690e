import website from '@/config/website';

const title = website.title;
const loginInfo = website.title;
export default {
    title: title,
    logoutTip: '退出系统, 是否继续?',
    submitText: '确定',
    cancelText: '取消',
    search: '请输入搜索内容',
    menuTip: '没有发现菜单',
    common: {
        condition: '条件',
        display: '显示',
        hide: '隐藏',
    },
    tip: {
        select: '请选择',
        input: '请输入',
    },
    upload: {
        upload: '点击上传',
        tip: '将文件拖到此处，或',
    },
    date: {
        start: '开始日期',
        end: '结束日期',
        t: '今日',
        y: '昨日',
        n: '近7天',
        a: '全部',
    },
    form: {
        printBtn: '打 印',
        mockBtn: '模 拟',
        submitBtn: '提 交',
        emptyBtn: '清 空',
    },
    crud: {
        filter: {
            addBtn: '新增条件',
            clearBtn: '清空数据',
            resetBtn: '清空条件',
            cancelBtn: '取 消',
            submitBtn: '确 定',
        },
        column: {
            name: '列名',
            hide: '隐藏',
            fixed: '冻结',
            filters: '过滤',
            sortable: '排序',
            index: '顺序',
            width: '宽度',
        },
        tipStartTitle: '当前表格已选择',
        tipEndTitle: '项',
        editTitle: '编 辑',
        copyTitle: '复 制',
        addTitle: '新 增',
        viewTitle: '查 看',
        filterTitle: '过滤条件',
        showTitle: '列显隐',
        menu: '操作',
        addBtn: '新 增',
        show: '显 示',
        hide: '隐 藏',
        open: '展 开',
        shrink: '收 缩',
        printBtn: '打 印',
        excelBtn: '导 出',
        updateBtn: '修 改',
        cancelBtn: '取 消',
        searchBtn: '搜 索',
        emptyBtn: '清 空',
        menuBtn: '功 能',
        saveBtn: '保 存',
        viewBtn: '查 看',
        editBtn: '编 辑',
        copyBtn: '复 制',
        delBtn: '删 除',
    },
    login: {
        title: '登录 ',
        info: loginInfo,
        tenantId: '请输入租户ID',
        username: '请输入账号',
        password: '请输入密码',
        wechat: '微信',
        qq: 'QQ',
        github: 'github',
        gitee: '码云',
        phone: '请输入手机号',
        code: '请输入验证码',
        submit: '登录',
        userLogin: '账号密码登录',
        phoneLogin: '手机号登录',
        thirdLogin: '第三方系统登录',
        ssoLogin: '单点系统登录',
        msgText: '发送验证码',
        msgSuccess: '秒后重发',
    },
    navbar: {
        logOut: '退出登录',
        management: '后台管理',
        userinfo: '个人信息',
        switchDept: '部门切换',
        dashboard: '首页',
        lock: '锁屏',
        bug: '没有错误日志',
        bugs: '条错误日志',
        screenfullF: '退出全屏',
        screenfull: '全屏',
        language: '中英文',
        notice: '消息通知',
        theme: '主题',
        color: '换色',
    },
    tagsView: {
        search: '搜索',
        menu: '更多',
        clearCache: '清除缓存',
        closeOthers: '关闭其它',
        closeAll: '关闭所有',
    },
};

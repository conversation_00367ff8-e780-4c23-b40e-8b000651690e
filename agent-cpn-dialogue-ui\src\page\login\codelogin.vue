<template>
    <el-form class="login-form" status-icon :rules="loginRules" ref="loginForm" :model="loginForm" label-width="0">
        <el-form-item prop="phone">
            <el-input
                @keyup.enter="handleLogin"
                v-model="loginForm.phone"
                auto-complete="off"
                :placeholder="$t('login.phone')"
            >
                <template #prefix>
                    <i class="icon-shouji" />
                </template>
            </el-input>
        </el-form-item>
        <el-form-item prop="captchaCode">
            <el-row :span="24">
                <el-col :span="16">
                    <el-input
                        @keyup.enter="handleSend"
                        v-model="loginForm.captchaCode"
                        auto-complete="off"
                        :placeholder="$t('login.code')"
                    >
                        <template #prefix>
                            <i class="icon-yanzhengma" />
                        </template>
                    </el-input>
                </el-col>
                <el-col :span="8">
                    <div class="login-code">
                        <img :src="loginForm.image" class="login-code-img" @click="refreshCode" />
                    </div>
                </el-col>
            </el-row>
        </el-form-item>
        <el-form-item prop="code">
            <el-input
                @keyup.enter="handleLogin"
                v-model="loginForm.code"
                auto-complete="off"
                placeholder="请输入短信验证码"
            >
                <template #prefix>
                    <i class="iconicon_message" style="font-family: iconfont; font-style: normal" />
                </template>
                <template #append>
                    <span @click="handleSend" class="msg-text" :class="[{ display: msgKey }]">{{ msgText }}</span>
                </template>
            </el-input>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click.prevent="handleLogin" class="login-submit">
                {{ $t('login.submit') }}
            </el-button>
        </el-form-item>
    </el-form>
</template>

<script>
import { isvalidatemobile, validatenull } from '@/utils/validate';
import { mapGetters } from 'vuex';
import { getCaptcha, getSmsCode } from '@/api/user';
import { ElMessage } from 'element-plus';
import { Message } from '@element-plus/icons-vue';
import { deepClone } from '@/utils/util';
import crypto from '@/utils/crypto';

export default {
    name: 'codelogin',
    components: { Message },
    data() {
        const validatePhone = (rule, value, callback) => {
            if (isvalidatemobile(value)[0]) {
                callback(new Error(isvalidatemobile(value)[1]));
            } else {
                callback();
            }
        };
        const validateCode = (rule, value, callback) => {
            if (value.length !== 6) {
                callback(new Error('请输入6位数的验证码'));
            } else {
                callback();
            }
        };
        return {
            msgText: '',
            msgTime: '',
            msgKey: false,
            loginForm: {
                phone: '',
                code: '',
                //租户ID
                tenantId: '000000',
                //账号类型
                type: 'account',
                //部门ID
                deptId: '',
                //角色ID
                roleId: '',
                captchaKey: '',
                captchaCode: '',
                //预加载白色背景
                image: 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7',
            },
            loginRules: {
                phone: [{ required: true, trigger: 'blur', validator: validatePhone }],
                code: [{ required: true, trigger: 'blur', validator: validateCode }],
            },
        };
    },
    created() {
        this.msgText = this.config.MSGINIT;
        this.msgTime = this.config.MSGTIME;
        this.refreshCode();
    },
    mounted() {},
    computed: {
        ...mapGetters(['tagWel']),
        config() {
            return {
                MSGINIT: this.$t('login.msgText'),
                MSGSCUCCESS: this.$t('login.msgSuccess'),
                MSGTIME: 60,
            };
        },
    },
    props: [],
    methods: {
        refreshCode() {
            getCaptcha().then(res => {
                const data = res.data;
                this.loginForm.captchaKey = data.key;
                this.loginForm.image = data.image;
            });
        },
        handleSend() {
            if (this.msgKey) return;
            let isValidate = isvalidatemobile(this.loginForm.phone);
            if (isValidate[0]) {
                ElMessage({
                    message: isValidate[1],
                    type: 'error',
                });
                return;
            }
            // 判断验证码
            if (validatenull(this.loginForm.captchaCode)) {
                ElMessage({
                    message: '请输入验证码后再发送短信验证码',
                    type: 'error',
                });
                return;
            }
            let phone = crypto.encrypt(this.loginForm.phone);
            // 发送短信
            getSmsCode(phone, this.loginForm.captchaKey, this.loginForm.captchaCode)
                .then(res => {
                    this.refreshCode();
                    const data = res.data.data;
                    ElMessage({
                        message: res.data.msg,
                        type: 'success',
                    });
                    this.msgText = this.msgTime + this.config.MSGSCUCCESS;
                    this.msgKey = true;
                    const time = setInterval(() => {
                        this.msgTime--;
                        this.msgText = this.msgTime + this.config.MSGSCUCCESS;
                        if (this.msgTime === 0) {
                            this.msgTime = this.config.MSGTIME;
                            this.msgText = this.config.MSGINIT;
                            this.msgKey = false;
                            clearInterval(time);
                        }
                    }, 1000);
                })
                .catch(err => {
                    this.refreshCode();
                });
        },
        handleLogin() {
            this.$refs.loginForm.validate(valid => {
                if (valid) {
                    let userInfo = deepClone(this.loginForm);
                    userInfo.phone = crypto.encrypt(userInfo.phone);
                    this.$store.dispatch('LoginByPhone', userInfo).then(() => {
                        this.$router.push('/index');
                    });
                }
            });
        },
    },
};
</script>

<style>
.msg-text {
    display: block;
    width: 60px;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
}

.msg-text.display {
    color: #ccc;
}
</style>

import router from './router/';
import store from './store';
import { getToken } from '@/utils/auth';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style
NProgress.configure({ showSpinner: false });
const lockPage = '/lock'; //锁屏页
import { useUserStore } from '@/components/dialogue/store/modules/user';
import { TokenManager } from '@/utils/tokenManager';

import { ROUTER_WHITE_LIST } from '@/components/dialogue/config';
router.beforeEach((to, from, next) => {
    const meta = to.meta || {};
    const userStore = useUserStore();

    const isMenu = meta.menu === undefined ? to.query.menu : meta.menu;
    store.commit('SET_IS_MENU', isMenu === undefined);

    // 白名单路由直接通过
    if (ROUTER_WHITE_LIST.includes(to.path)) return next();

    // 检查token有效性
    const hasValidToken = TokenManager.isTokenValid() && userStore.token;

    if (hasValidToken) {
        if (store.getters.isLock && to.path !== lockPage) {
            //如果系统激活锁屏，全部跳转到锁屏页
            next({ path: lockPage });
        } else if (to.path === '/login') {
            //如果登录成功访问登录页跳转到主页
            next();
        } else {
            // 检查token是否即将过期
            if (TokenManager.isTokenExpiringSoon()) {
                console.log('Token即将过期，尝试自动刷新...');
                userStore
                    .refreshToken()
                    .then(() => {
                        next();
                    })
                    .catch(() => {
                        // 刷新失败，跳转登录页
                        store.dispatch('FedLogOut').then(() => {
                            next({ path: '/login' });
                        });
                    });
            } else {
                const meta = to.meta || {};
                const query = to.query || {};
                if (meta.target) {
                    window.open(query.url.replace(/#/g, '&'));
                    return;
                } else if (meta.isTab !== false) {
                    store.commit('ADD_TAG', {
                        name: query.name || to.name,
                        path: to.path,
                        fullPath: to.fullPath,
                        params: to.params,
                        query: to.query,
                        meta: meta,
                    });
                }
                next();
            }
        }
    } else {
        //判断是否需要认证，没有登录访问去登录页
        if (meta.isAuth === false) {
            next();
        } else {
            next({
                path: '/login',
                query: { redirect: to.fullPath },
            });
        }
    }
});

router.afterEach(to => {
    NProgress.done();
    let title = router.$avueRouter.generateTitle(to, { label: 'name' });
    router.$avueRouter.setTitle(title);
    store.commit('SET_IS_SEARCH', false);
});

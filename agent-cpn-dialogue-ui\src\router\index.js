import { createRouter, createWebHistory } from 'vue-router';
import PageRouter from './page/';
import ViewsRouter from './views/';
import AvueRouter from './avue-router';
import i18n from '@/lang';
import Store from '@/store/';
//创建路由

let routes = [...PageRouter, ...ViewsRouter];
let viewRouters = import.meta.globEager('./views/*.js');
Object.keys(viewRouters).forEach(key => routes.push(...viewRouters[key].default));

// 将新路由添加到现有路由中
routes = [...routes];

const Router = createRouter({
    base: import.meta.env.VITE_APP_BASE,
    history: createWebHistory('/dialogue/'),
    routes: routes,
});

// 加载views文件夹下的所有路由配置

AvueRouter.install({
    store: Store,
    router: Router,
    i18n: i18n,
});

Router.$avueRouter.formatRoutes(Store.getters.menuAll, true);

export function resetRouter() {
    // 重置路由 比如用于身份验证失败，需要重新登录时 先清空当前的路有权限
    const newRouter = createRouter();
    Router.matcher = newRouter.matcher; // reset router
    AvueRouter.install(Vue, {
        router: Router,
        store: Store,
        i18n: i18n,
    });
}

export default Router;

import Layout from '@/page/index/index.vue';
// 当前组件的所有自定义路由写在这里
const HOME_URL = '/chat/';
export default [
    // 首页

    {
        path: '/',
        redirect: HOME_URL,
        children: [
            {
                path: HOME_URL,
                name: 'chat',
                component: () => import('@dialogue/page/chat/index.vue'),
                // component: () => import('@dialogue/main.vue'),
                meta: {
                    // title: '通用聊天页面',
                    isDefaultChat: true,
                    icon: 'HomeFilled',
                    // isHide: '1', // 是否在菜单中隐藏[0是，1否] 预留
                    // isKeepAlive: '0', // 是否缓存路由数据[0是，1否] 预留
                    // isFull: '1', // 是否全屏[0是，1否] 预留
                    // enName: "Master Station", // 英文名称 预留
                },
            },
            {
                path: '/chat/:id',
                name: 'chatWithId',
                component: () => import('@dialogue/page/chat/index.vue'),

                meta: {
                    // title: '带 ID 的聊天页面',
                    isDefaultChat: false,
                },
            },
            {
                path: '/index/',
                name: 'index',
                component: () => import('@/components/dialogue/index.vue'),
                meta: {
                    keepAlive: true,
                    isTab: false,
                    isAuth: false,
                },
            },
        ],
    },
];

import { setStore, getStore, removeStore } from 'utils/store';
import website from '@/config/website';
import user from '@/store/modules/user';

const tagWel = website.fistPage;
const navs = {
    state: {
        tagList: getStore({ name: 'tagList' }) || [],
        tag: getStore({ name: 'tag' }) || {},
        tagWel: tagWel,
    },
    mutations: {
        ADD_TAG: (state, action) => {
            if (typeof action.name == 'function') action.name = action.name(action.query);
            state.tag = action;
            setStore({ name: 'tag', content: state.tag });
            if (action.fullPath === website.fistPage.path) {
                return;
            }
            let tagd = state.tagList.find(ele => ele.fullPath == action.fullPath);
            if (tagd) {
                setStore({ name: website.currentTagKey, content: tagd, type: true });
                return;
            }
            action['topMenu'] = user.state.menuId;
            setStore({ name: website.currentTagKey, content: action, type: true });
            state.tagList.push(action);
            setStore({ name: 'tagList', content: state.tagList });
        },
        DEL_TAG: (state, action) => {
            state.tagList = state.tagList.filter(item => {
                return item.fullPath !== action.fullPath;
            });
            setStore({ name: 'tagList', content: state.tagList });
        },
        DEL_ALL_TAG: (state, tagList = []) => {
            state.tagList = tagList;
            removeStore({ name: website.currentTagKey, type: true });
            setStore({ name: 'tagList', content: state.tagList });
        },
        DEL_TAG_OTHER: state => {
            state.tagList = state.tagList.filter(item => {
                return [state.tag.fullPath, website.fistPage.path].includes(item.fullPath);
            });
            setStore({ name: 'tagList', content: state.tagList });
        },
    },
};
export default navs;

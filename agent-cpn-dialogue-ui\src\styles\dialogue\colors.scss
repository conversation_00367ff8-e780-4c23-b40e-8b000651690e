// 颜色变量定义
// 基础颜色
$primary-color: #409eff; // 主色
$success-color: #67c23a; // 成功色
$warning-color: #e6a23c; // 警告色
$danger-color: #f56c6c; // 危险色
$info-color: #909399; // 信息色
$bg: #fff;
// 文本颜色
$text-primary: #1d2939; // 主要文本
$text-secondary: #475467; // 次要文本
$text-placeholder: #c0c4cc; // 占位文本
$text-prompt: #667085; // 提示文本
// 边框颜色
$border-base: #dcdfe6; // 一级边框
$border-light: #e4e7ed; // 二级边框
$border-lighter: #e9f2fd; // 三级边框
$border-extra-light: #f2f6fc; // 四级边框

// 背景颜色
$background-white: #ffffff; // 纯白背景
$background-base: #f5f7fa; // 基础背景
$background-light: #f4f9fe; // 亮色背景

// 阴影颜色
$box-shadow-base: rgba(0, 0, 0, 0.1); // 基础阴影
$box-shadow-light: rgba(0, 0, 0, 0.05); // 轻量阴影

// 特定颜色
$form-input-bg: #f4f9fe; // 表单输入框背景色
$form-input-border: #e9f2fd; // 表单输入框边框色
$form-label-color: #98a2b3; // 表单标签文字颜色
$button-cancel-bg: #f5f7fa; // 取消按钮背景色
$card-bg: #f4f9fe; // 卡片背景色

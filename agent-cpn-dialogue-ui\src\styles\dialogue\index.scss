* {
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
}
// a {
// 	text-decoration: none; /* 去除默认的下划线 */
// 	outline: none;	/* 去除旧版浏览器的点击后的外虚线框 */
// }

li {
    list-style: none;
}
html,
body {
    font-size: 16px;
}
.container_agent {
    width: 100%;
    height: 100%;
    overflow: auto;
    position: relative;
    background: url(static/dialogue/index_bj.png) no-repeat left top/100% 100%;
}

// 头部
.container_agent_header {
    height: 40px;
    position: relative;

    .header_user {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100px;
        height: 36px;
        width: 130px;
        color: #267ef0;
        font-size: 14px;
        position: absolute;
        right: 0px;
        top: 0px;
        cursor: pointer;
        transition: 0.3s;
    }

    .header_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100px;
        border: 1px solid #267ef0;
        background: #fff;
        height: 32px;
        width: 96px;
        color: #267ef0;
        font-size: 14px;
        position: absolute;
        right: 20px;
        top: 40px;
        cursor: pointer;
        transition: 0.3s;

        img {
            margin-right: 4px;
        }

        &:hover {
            background-color: #267ef0;
            color: #fff;
            transition: 0.3s;
        }

        &.linkbtn {
            border: 1px #8ab9f5 solid;
            color: #6a92c7;
            cursor: default;

            &:hover {
                background: #fff !important;
                color: #6a92c7 !important;
            }
        }
    }

    .header_logo {
        position: absolute;
        left: 50%;
        top: 6px;
        transform: translateX(-50%);
    }

    .button-right {
        float: right;
        margin-top: 3px;
    }
}

.container_agent_content {
    height: 100%;
    display: flex;
}

// 菜单
.container_agent_left {
    width: 250px;
    position: relative;
    height: 100%;
    z-index: 10;
    // overflow: auto;

    .agent_title {
        background: url(static/dialogue/agent_title.png) no-repeat center center;
        width: 152px;
        height: 42px;
        line-height: 33px;
        font-size: 16px;
        color: #fff;
        padding-left: 35px;
        position: relative;
        top: 20px;
        left: 47px;
        cursor: pointer;
    }

    .agent_tree {
        display: flex;
        flex-direction: column;
        height: 100%;
        // overflow: auto;
        padding: 20px;

        .agent_logo {
            display: flex;
            align-items: center;
            gap: 8px;

            span {
                font-size: 18px;
                font-weight: bold;
                color: #1d2939;
            }
        }

        .agent_new {
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            border: 1px solid #1570ef;

            cursor: pointer;
            margin-bottom: 6px;

            span {
                font-size: 16px;
                color: #1570ef;
            }
            &:hover {
                background-color: #1570ef;
                .svg-icon {
                    color: #fff;
                }
            }
        }

        .agent_gzt {
            display: flex;
            align-items: center;
            height: 40px;
            line-height: 40px;
            border-radius: 8px;
            padding: 0px 16px;
            cursor: pointer;
            transition: 0.4s;
            margin-bottom: 10px;

            b {
                color: #475467;
                font-size: 16px;
            }

            &:hover,
            &.active {
                transition: 0.4s;
                background: #fff;
            }
        }

        .agent_tree_list {
            padding: 5px 0px;
            border-top: 1px solid #fff;
            border-bottom: 1px solid #fff;
            margin-bottom: 20px;

            li {
                list-style: none;
                display: flex;
                align-items: center;
                height: 40px;
                border-radius: 8px;
                padding: 0px 16px;
                margin-bottom: 6px;
                cursor: pointer;
                transition: 0.4s;
                position: relative;

                span {
                    width: 20px;
                    height: 20px;
                    margin-right: 6px;
                }

                .tip {
                    position: absolute;
                    left: -9px;
                    top: 39px;
                    // display: none;
                    opacity: 0;
                    transition: 0.3s;
                    z-index: 1;

                    small {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 20px;
                        color: #fff;
                        font-size: 12px;
                        border-radius: 3px;
                        background-color: rgba(0, 0, 0, 0.7);
                        width: 58px;
                    }

                    i {
                        position: absolute;
                        left: 25px;
                        top: -4px;
                        width: 0;
                        width: 0;
                        height: 0;
                        border-left: 4px solid transparent;
                        border-right: 4px solid transparent;
                        border-bottom: 4px solid rgba(0, 0, 0, 0.7);
                    }
                }

                > div {
                    h3 {
                        font-size: 14px;
                        color: #475467;
                        font-weight: normal;
                    }
                }

                &:hover,
                &.active {
                    transition: 0.4s;
                    background: #fff;
                }

                &:hover {
                    .tree_children_box {
                        display: block;
                    }
                }
            }
        }

        .tree_children_box {
            position: absolute;
            left: 100%;
            top: 0px;
            padding-left: 30px;
            display: none;
            z-index: 10;
            width: 320px;
        }

        .tree_children {
            display: inline-block;

            .list_tree_children {
                list-style: none;
                display: flex;
                align-items: center;
                border-radius: 8px;
                border: 1px solid #f4f3ff;
                background: #fff;
                cursor: pointer;
                margin-bottom: 10px;
                padding: 0px 10px 0px 0px;
                min-width: 220px;
                &:hover {
                    .itemChild-icon {
                        color: #1570ef;
                    }
                }
                .list_tree_children_icon {
                    width: 40px;
                    height: 40px;
                    overflow: hidden;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .list_tree_children_word {
                    > h4 {
                        color: #1d2939;
                        font-size: 14px;
                        margin-bottom: 5px;
                        font-weight: normal;
                    }
                }

                &:hover {
                    border: 1px solid #d1e9ff;

                    .list_tree_children_word {
                        > h4 {
                            color: #1570ef;
                        }
                    }
                }
            }
        }

        .agent_tree_person {
            flex: 1;
            height: 0px;

            > h2 {
                height: 48px;
                border-radius: 8px;
                border: 1px solid rgba(22, 119, 255, 0.2);
                background: linear-gradient(96deg, rgba(130, 169, 255, 0.8) 1.82%, rgba(192, 225, 255, 0.8) 100.91%);
                box-shadow: 0px 4px 4px 0px rgba(54, 126, 255, 0.1) inset;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #1677ff;
                font-size: 18px;
                margin-bottom: 12px;

                > img {
                    margin-right: 5px;
                    position: relative;
                    top: 3px;
                }
            }

            .agent_tree_box {
                height: 100%;

                h3 {
                    margin-bottom: 12px;
                    display: flex;
                    align-items: center;
                    margin-left: 16px;

                    > img {
                        margin-right: 8px;
                    }

                    span {
                        color: #475467;
                        font-size: 14px;
                        flex: 1;
                        font-weight: normal;
                    }

                    a {
                        color: #8095b4;
                        font-size: 12px;
                        font-weight: normal;
                        cursor: pointer;
                    }
                }

                ul {
                    height: calc(100% - 30px);
                    overflow: auto;

                    li {
                        display: flex;
                        height: 40px;
                        align-items: center;
                        margin-bottom: 8px;
                        cursor: pointer;
                        transition: 0.4s;
                        padding: 0px 8px;

                        &:hover,
                        &.active {
                            transition: 0.4s;
                            background: #fff;

                            span {
                                display: block;
                            }
                        }

                        small {
                            flex: 1;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            font-size: 14px;
                            color: #475467;
                        }

                        .agent_lsjl_num {
                            height: 20px;
                            line-height: 20px;
                            padding: 0px 3px;
                            border-radius: 2px;
                            background: #ef6820;
                            font-style: normal;
                            font-size: 13px;
                            color: #ffffff;
                            min-width: 20px;
                            text-align: center;
                            margin: 0px 3px;
                            border-radius: 10px;
                        }

                        > span {
                            width: 20px;
                            height: 20px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            border-radius: 2px;
                            display: none;
                        }

                        .agent_lsjl_icon1 {
                            background: url(/dialogue/agent_lsjl_icon1.png) no-repeat center center;

                            &:hover {
                                background: url(/dialogue/agent_lsjl_icon11.png) no-repeat center center #eff8ff;
                            }
                        }

                        .agent_lsjl_icon2 {
                            background: url(/dialogue/agent_lsjl_icon2.png) no-repeat center center;

                            &:hover {
                                background: url(/dialogue/agent_lsjl_icon22.png) no-repeat center center #eff8ff;
                            }
                        }

                        .agent_lsjl_icon3 {
                            background: url(/dialogue/agent_lsjl_icon3.png) no-repeat center center;

                            &:hover {
                                background: url(/dialogue/agent_lsjl_icon33.png) no-repeat center center #eff8ff;
                            }
                        }

                        .agent_lsjl_icon4 {
                            background: url(/dialogue/agent_lsjl_icon4.png) no-repeat center center;

                            &:hover {
                                background: url(/dialogue/agent_lsjl_icon44.png) no-repeat center center #eff8ff;
                            }
                        }
                    }
                }
            }
        }
    }
}
.container_agent_right {
    display: flex;
    flex-direction: column;
    height: 100%;
}
.agent_arrow_icon {
    position: absolute;
    z-index: 10;
    left: 16px;
    top: 18px;
    transform: translateY(-50%);
    width: 22px;
    height: 20px;
    cursor: pointer;
}

.agent_tree_hide {
    &.container_agent_left {
        transition: 0.2s;
    }

    .container_agent_qa {
    }

    .container_agent_qa_box {
        margin-left: -81px;
    }
}

.agent_tree_hide {
    .agent_arrow {
        right: initial;
        left: 16px;
        background: url(/dialogue/agent_arrow2.png) no-repeat center center;
    }
}

.agent_tree_hide {
    width: 60px;

    .agent_tree {
        width: 60px;
        padding: 20px 0px 20px 10px;

        .agent_logo span,
        .agent_new span,
        .agent_gzt b,
        .agent_tree_person .agent_tree_box h3 span,
        .agent_tree_person .agent_tree_box ul {
            display: none;
        }

        .agent_logo img {
            margin-left: -5px;
        }

        .agent_new,
        .agent_gzt {
            padding: 0px;
            justify-content: center;
            width: 40px;

            span {
                margin: 0px;
            }
        }

        .agent_new {
            background-position: 10px center !important;
            border-radius: 50%;
        }

        .agent_tree_list li {
            justify-content: center;
            width: 40px;
            padding: 0px;

            > span {
                margin: 0px;
            }

            > div {
                display: none;
            }

            &:hover {
                .tip {
                    transition: 0.3s;
                    // display: block;
                    opacity: 1;
                }
            }
        }

        .agent_tree_person .agent_tree_box h3 {
            margin-left: 12px;
        }
    }
}

// 主区域
.container_agent_main {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.7);
    position: relative;
}

.customer_write {
    border: 1px solid #f4f3ff;
    padding: 0px 0px 16px;
    border-radius: 14px;
    background: #fff;
    transition: 0.4s;
    margin: 0px 50px;

    &.writing {
        transition: 0.4s;
        border: 1px solid rgb(46, 144, 250);
    }

    textarea {
        border: 0px;
        background: none;
        height: 67px;
        margin-bottom: 5px;
        width: 100%;
        overflow: auto;
        font-size: 14px;
        color: #333;
        line-height: 22px;
        font-family: '微软雅黑';
        resize: none;
        padding-left: 0px;
    }

    > p.write_btn {
        padding: 0px 24px;
        display: flex;
        height: 30px;
        align-items: center;
        justify-content: space-between;

        .deep-think {
            display: flex;
            padding: 5px 8px;
            align-items: center;
            border-radius: 14px;
            //background: #f5f5f5;
            height: 30px;
            border: 1px solid #d3d3d363;
            cursor: pointer;
            color: #a6b0bd;
            //color: #267ef0;
            font-size: 12px;

            > img {
                margin-right: 8px;
                opacity: 0.5;
            }
        }

        .deep-think-click {
            display: flex;
            padding: 5px 8px;
            align-items: center;
            border-radius: 14px;
            background: #e2eeff;
            height: 30px;
            border: 1px solid #267ef0;
            cursor: pointer;
            color: #267ef0;
            //color: #267ef0;
            font-size: 12px;

            > img {
                margin-right: 8px;
            }
        }

        //a {
        //
        //}
        > span {
            display: flex;
            align-items: center;

            i {
                width: 2px;
                background-color: rgba(128, 149, 180, 0.6);
                height: 14px;
                margin-left: 15px;
            }

            > img {
                margin-left: 15px;
                cursor: pointer;
            }
        }
    }

    .customer_write_title {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px #f2f4f7 solid;
        padding: 0px 24px;

        span {
            color: #1d2939;
            font-size: 14px;
            font-weight: bold;
        }

        p {
            display: flex;
            align-items: center;

            img {
                margin: 0px 0px 0px 12px;
                cursor: pointer;
            }

            i {
                font-size: 16px;
                color: #d6d6d6;
                margin-left: 12px;
                position: relative;
                top: -2px;
            }
        }
    }

    .writeDefaultWord {
        color: #98a2b3;
        font-size: 14px;

        small {
            color: #98a2b3;
            font-size: 14px;
        }
    }

    .textareaBox {
        min-height: 67px;
        overflow: auto;
        font-size: 14px;
        padding: 10px 24px;
        line-height: 27px;
    }

    .selectWord {
        color: #1d2939;
        margin: 0;
        padding: 0;
        font-size: 14px;
        line-height: 1.6;

        a {
            color: #1570ef;
            text-decoration: none;
            cursor: pointer;
            &:hover {
                text-decoration: underline;
            }
            i {
                width: 12px;
                height: 8px;
                font-size: 7px;
                background: url(static/dialogue/hdjiantou2.png) no-repeat center center;
                margin-left: 3px;
                position: relative;
                top: 2px;
            }
        }
    }

    .customer_write_form_box {
        padding: 16px 24px 6px;
        border-bottom: #f2f4f7 solid 1px;

        .customer_write_form_box_block {
            flex: 1;

            .customer_write_form_box_block_til {
                font-size: 14px;
                color: #1d2939;
                margin-bottom: 10px;
                font-weight: bold;
                display: flex;
                align-items: center;

                .red {
                    color: #d92d20;
                    margin-right: 3px;
                }

                small {
                    flex: 1;
                    font-weight: normal;
                    font-size: 12px;
                    color: #98a2b3;
                    padding-left: 10px;
                }

                a {
                    display: flex;
                    height: 28px;
                    padding: 0px 10px;
                    justify-content: center;
                    align-items: center;
                    border-radius: 3px;
                    border: 1px solid #d1e9ff;
                    color: #1570ef;
                    font-size: 14px;
                    font-weight: normal;
                    cursor: pointer;

                    &.yl {
                        background: #edfcf2;
                        border: 1px solid #edfcf2;
                        color: #099250;
                        margin-left: 5px;
                    }
                }
            }
        }
    }

    .customer_write_form_box_inline {
        display: flex;
        justify-content: space-between;

        .customer_write_form_box_block {
            flex: initial;
            width: 48%;
        }
    }
}

// 机器人
.container_agent_robot {
    width: 290px;
    height: 405px;
    position: fixed;
    z-index: 99;
    right: 30px;
    bottom: 50px;

    .agent_robot_talk {
        right: 0px;
        top: 20px;
        position: absolute;
        display: flex;
        width: 161px;
        height: 58px;
        padding: 0px 14px;
        align-items: center;
        flex-shrink: 0;
        border-radius: 6px;
        border: 1px solid #fff;
        background: linear-gradient(175deg, rgba(222, 236, 244, 0.9) 0.23%, rgba(242, 249, 255, 0.9) 120.71%);
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.06);
        color: #677b94;
        font-size: 14px;
    }
}

.to_left {
    animation: to_left 0.4s linear 1s forwards;
    opacity: 0;
}

@keyframes to_left {
    0% {
        opacity: 0;
        transform: translateX(40px);
    }

    100% {
        opacity: 1;
        transform: translateX(0px);
    }
}

.to_top {
    animation: to_top 0.4s linear 0s;
}

@keyframes to_top {
    0% {
        opacity: 0;
        transform: translateY(100px);
    }

    100% {
        opacity: 1;
        transform: translateY(0px);
    }
}

// 智能问答交互
.container_agent_qa_box {
    display: flex;
    justify-content: center;
}

.container_agent_qa_box_r {
    background-color: #fff;
    flex: 1;
    // max-width: 800px;
    border-left: 1px solid #eaecf0;
    box-shadow: 0px 4px 10px 0px #eee;
    height: 100%;
    overflow: auto;

    // 添加对el-aside中使用的额外样式
    .el-aside & {
        border-left: none; // 因为el-aside已有边框
        box-shadow: none;
        display: flex;
        flex-direction: column;
        width: 100%;
    }
}

.container_agent_qa {
    width: 50%;
    min-width: 850px;
    height: 100%;
    position: relative;
}

.agent_qa_icon1 {
    position: absolute;
    top: 100px;
    left: 17px;
}

.agent_qa_icon2 {
    position: absolute;
    top: 126px;
    right: 18px;
}

.agent_qa_icon3 {
    position: absolute;
    bottom: 220px;
    left: 17px;
}

.agent_qa_icon4 {
    position: absolute;
    bottom: 50px;
    right: 18px;
}

.agent_qa_yuan {
    position: absolute;
    top: 10px;
    left: 20px;
}

.container_agent_qaf {
    height: 100%;
}

.container_agent_mid {
    height: 100%;
}

.customer_service {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .customer_question {
        flex: 1;
        height: 0;
        overflow: auto;
        margin-bottom: 15px;
        // padding-right: 12px;
    }
}

.hot_qus {
    li {
        padding: 16px 20px;
        background-color: #022142;
        display: flex;
        margin-bottom: 10px;
        align-items: center;
        cursor: pointer;
        transition: 0.4s;

        a {
            font-size: 18px;
            color: #d4e6ff;
            line-height: 30px;
        }

        &:hover {
            transition: 0.4s;
            background: #004187;
        }
    }
}

.agent_qa_title {
    font-size: 16px;
    font-weight: bold;
    color: #1d2939;
    text-align: center;
    padding: 0px 0px 30px;
}

.default_ask {
    display: flex;
    margin-bottom: 16px;

    .ask_icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
        margin-right: 15px;
    }

    .ask_content {
        width: calc(100% - 110px);
        // flex: 1;
        border-radius: 8px;
        background: #fff;
        padding: 8px 12px;
        width: 100%;
        box-shadow: 0px 0px 10px 0px rgba(222, 230, 240, 0.6);

        > p {
            font-size: 16px;
            color: #1d2939;
            line-height: 28px;
            margin-bottom: 5px;
            font-weight: bold;

            a {
                color: #1677ff;
                font-size: 20px;
            }
        }

        > small {
            color: #98a2b3;
            font-size: 14px;
        }
    }
}

.ask_title {
    > p {
        font-size: 16px;
        color: #1d2939;
        line-height: 28px;
        margin-bottom: 5px;
        font-weight: bold;

        a {
            color: #1677ff;
            font-size: 20px;
        }
    }

    > small {
        color: #98a2b3;
        font-size: 14px;
    }
}

.ask {
    display: flex;
    margin-bottom: 16px;
    width: 100%;

    .ask_icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
        margin-right: 15px;
    }

    .ask_content {
        width: calc(100% - 110px);
        // flex: 1;
        border-radius: 8px;
        background: #fff;
        padding: 16px 12px;
        box-shadow: 0px 0px 10px 0px rgba(222, 230, 240, 0.6);

        > p {
            font-size: 14px;
            color: #344054;
            line-height: 22px;
            margin-bottom: 5px;

            a {
                color: #1677ff;
                font-size: 20px;
            }
        }

        > small {
            color: #8095b4;
            font-size: 14px;
        }
    }
}

.ask_block {
    padding: 15px 0px 0px;

    h2 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 18px;
        color: #000;
        font-weight: normal;
        margin-bottom: 10px;

        > span {
            display: flex;
            justify-content: center;
            align-items: center;
            color: #1d2939;
            font-size: 14px;

            > img {
                margin-right: 5px;
            }
        }

        > a {
            display: flex;
            justify-content: center;
            align-items: center;
            color: #267ef0;
            font-size: 14px;
            cursor: pointer;

            > img {
                margin-right: 5px;
            }
        }
    }

    ul {
        display: flex;
        flex-wrap: wrap;

        li {
            width: 49%;
            height: 32px;
            display: flex;
            align-items: center;
            border-radius: 4px;
            background: #f0f5fc;
            margin: 0px 8px 8px 0px;
            cursor: pointer;

            a {
                display: inline-block;
                font-size: 14px;
                color: #435e81;
                padding: 0px 12px;
                height: 32px;
                line-height: 32px;
                cursor: pointer;
            }

            small {
                display: flex;
                height: 20px;
                padding: 2px 8px;
                justify-content: center;
                align-items: center;
                border-radius: 4px;
                background: #dfecff;
                color: #1677ff;
                font-size: 12px;

                &.type_11 {
                    background: #f6fff3;
                    color: #31bb02;
                }

                &.type_12 {
                    background: #fff5fa;
                    color: #e42e83;
                }

                &.type_13 {
                    background: #fffbf5;
                    color: #ffa826;
                }

                &.type_14 {
                    background: #dfecff;
                    color: #1677ff;
                }

                &.type_15 {
                    background: #dfecff;
                    color: #1ead57;
                }

                &.type_16 {
                    background: #dfecff;
                    color: #9923b4;
                }

                &.type_17 {
                    background: #dfecff;
                    color: #961f43;
                }

                &.type_18 {
                    background: #dfecff;
                    color: #99acc7;
                }

                &.type_19 {
                    background: #dfecff;
                    color: #b9ae30;
                }

                &.type_20 {
                    background: #dfecff;
                    color: #48cb9d;
                }

                &.type_21 {
                    background: #dfecff;
                    color: #510a65;
                }

                &.type_22 {
                    background: #dfecff;
                    color: #7dadce;
                }

                &.type_23 {
                    background: #dfecff;
                    color: #250646;
                }
            }

            &:hover {
                transition: 0.4s;
                background: #267ef0;

                a {
                    color: #fff;
                }
            }
        }
    }
}

.customer_list_block {
    border-radius: 8px;
    background: #fff;
    padding: 8px 12px;
    margin: 10px 0;
    width: 370px;
    box-shadow: 0px 0px 10px 0px rgba(222, 230, 240, 0.6);
    margin-bottom: 8px;

    .list-li {
        list-style: none;
        display: flex;
        align-items: center;
        height: 36px;
        border-radius: 4px;
        padding: 0px 10px 0px 0px;
        cursor: pointer;
        position: relative;
        .icon {
            margin: 0 6px 0 16px;
        }
        &:hover .item-icon {
            color: #1570ef;
        }
        span.list_block_icon {
            width: 40px;
            height: 40px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        &:nth-child(1) {
            span.list_block_icon {
                background: url(static/dialogue/agent_tree_icon1.png) no-repeat center center;
            }
        }

        &:nth-child(2) {
            span.list_block_icon {
                background: url(static/dialogue/agent_tree_icon2.png) no-repeat center center;
            }
        }

        &:nth-child(3) {
            span.list_block_icon {
                background: url(static/dialogue/agent_tree_icon3.png) no-repeat center center;
            }
        }

        &:nth-child(4) {
            span.list_block_icon {
                background: url(static/dialogue/agent_tree_icon4.png) no-repeat center center;
            }
        }

        &:nth-child(5) {
            span.list_block_icon {
                background: url(static/dialogue/agent_tree_icon5.png) no-repeat center center;
            }
        }

        &:nth-child(6) {
            span.list_block_icon {
                background: url(static/dialogue/agent_tree_icon6.png) no-repeat center center;
            }
        }

        &:nth-child(7) {
            span.list_block_icon {
                background: url(static/dialogue/agent_tree_icon7.png) no-repeat center center;
            }
        }

        > .list_block_word {
            display: flex;

            > h3 {
                color: #1d2939;
                font-size: 14px;
                margin-right: 10px;
                font-weight: normal;
            }

            > p {
                color: #475467;
                font-size: 12px;
            }
        }

        &:hover {
            background-color: #eff8ff;

            &:nth-child(1) {
                span.list_block_icon {
                    background: url(static/dialogue/agent_tree_icon11.png) no-repeat center center;
                }
            }

            &:nth-child(2) {
                span.list_block_icon {
                    background: url(static/dialogue/agent_tree_icon22.png) no-repeat center center;
                }
            }

            &:nth-child(3) {
                span.list_block_icon {
                    background: url(static/dialogue/agent_tree_icon33.png) no-repeat center center;
                }
            }

            &:nth-child(4) {
                span.list_block_icon {
                    background: url(static/dialogue/agent_tree_icon44.png) no-repeat center center;
                }
            }

            &:nth-child(5) {
                span.list_block_icon {
                    background: url(static/dialogue/agent_tree_icon55.png) no-repeat center center;
                }
            }

            &:nth-child(6) {
                span.list_block_icon {
                    background: url(static/dialogue/agent_tree_icon66.png) no-repeat center center;
                }
            }

            &:nth-child(7) {
                span.list_block_icon {
                    background: url(static/dialogue/agent_tree_icon77.png) no-repeat center center;
                }
            }

            > .list_block_word {
                > h3 {
                    color: #1570ef;
                }
            }

            .block_children_box {
                display: block;
            }
        }
    }
}

.block_children_box {
    position: absolute;
    left: 92%;
    padding-left: 40px;
    display: none;
    width: 450px;
}

.block_children {
    border-radius: 8px;
    padding: 8px 12px;
    box-shadow: 0px 0px 10px 0px rgba(222, 230, 240, 0.6);
    margin-bottom: 8px;
    display: inline-block;
    color: #475467;

    &:hover {
        .icon {
        }
    }
    .list_block_children {
        list-style: none;
        display: flex;
        align-items: center;
        border-radius: 4px;
        padding: 10px 10px 10px 0px;
        cursor: pointer;
        &:hover .itemChild-icon {
            color: #1570ef;
        }
        .list_block_children_icon {
            width: 40px;
            height: 40px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .list_block_children_word {
            > h4 {
                color: #1d2939;
                font-size: 14px;
                margin-bottom: 5px;
                font-weight: normal;
            }

            > h5 {
                color: #475467;
                font-size: 12px;
                font-weight: normal;
            }
        }

        &:hover {
            background-color: #eff8ff;

            .list_block_children_word {
                > h4 {
                    color: #1570ef;
                }
            }
        }
    }
}

.childtree1 {
    .list_block_children {
        &:nth-child(1) {
            .list_block_children_icon {
                background: url(static/dialogue/agent_tree_icon1_1.png) no-repeat center center;
            }
        }

        &:nth-child(2) {
            .list_block_children_icon {
                background: url(static/dialogue/agent_tree_icon1_2.png) no-repeat center center;
            }
        }

        &:hover {
            &:nth-child(1) {
                .list_block_children_icon {
                    background: url(static/dialogue/agent_tree_icon1_11.png) no-repeat center center;
                }
            }

            &:nth-child(2) {
                .list_block_children_icon {
                    background: url(static/dialogue/agent_tree_icon1_22.png) no-repeat center center;
                }
            }
        }
    }
}

.childtree2 {
    .list_block_children {
        &:nth-child(1) {
            .list_block_children_icon {
                background: url(static/dialogue/agent_tree_icon2_1.png) no-repeat center center;
            }
        }

        &:nth-child(2) {
            .list_block_children_icon {
                background: url(static/dialogue/agent_tree_icon2_2.png) no-repeat center center;
            }
        }

        &:nth-child(3) {
            .list_block_children_icon {
                background: url(static/dialogue/agent_tree_icon2_3.png) no-repeat center center;
            }
        }

        &:hover {
            &:nth-child(1) {
                .list_block_children_icon {
                    background: url(static/dialogue/agent_tree_icon2_11.png) no-repeat center center;
                }
            }

            &:nth-child(2) {
                .list_block_children_icon {
                    background: url(static/dialogue/agent_tree_icon2_22.png) no-repeat center center;
                }
            }

            &:nth-child(3) {
                .list_block_children_icon {
                    background: url(static/dialogue/agent_tree_icon2_33.png) no-repeat center center;
                }
            }
        }
    }
}

.default_ask_block {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .default_ask_list {
        width: 49%;
        border-radius: 4px;
        background: #f4f9fe;
        padding: 10px;
        margin-bottom: 10px;

        h4 {
            color: #1d2939;
            font-size: 14px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;

            span {
                flex: 1;
            }

            img {
                margin-right: 5px;
            }

            a {
                color: #1570ef;
                font-size: 12px;
                font-weight: normal;
                cursor: pointer;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        p {
            color: #475467;
            font-size: 14px;
        }
    }
}

// ---------------------------------不同问答组件界面公共样式------------------------------------------
.task_thinking_over {
    h2 {
        color: #667085;
        font-size: 14px;
        height: 30px;
        align-items: center;
        display: inline-flex;
        padding: 2px 12px;
        gap: 6px;
        border-radius: 4px;
        background: #f2f4f7;
        font-weight: normal;
        cursor: pointer;

        img.zhanshouicon {
            transition: 0.3s;
        }
    }

    .task_thinking_content {
        padding: 10px 0px;

        p {
            line-height: 24px;
            font-size: 12px;
            color: #999;
        }
    }
}

.task_thinking_result {
    height: 36px;
    align-items: center;
    display: flex;
    padding: 2px 12px;
    gap: 6px;
    border-radius: 4px;
    background: #f2f4f7;
    font-weight: normal;
    margin: 6px 0px;

    span {
        color: #475467;
        font-size: 14px;
        flex: 1;
    }
}

.task_thinking_btn {
    padding: 16px 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 24px;
    width: 100%;

    a {
        display: flex;
        height: 32px;
        padding: 0px 16px;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        font-size: 14px;
        transition: 0.3s;
        cursor: pointer;

        &:hover {
            opacity: 0.9;
            transition: 0.3s;
        }
    }

    .btn0 {
        border: 1px solid #eaecf0;
        background: #f2f4f7;
        color: #475467;
    }

    .btn1 {
        border: 1px solid #1570ef;
        background: #eff8ff;
        color: #1570ef;
    }

    .btn2 {
        border: 1px solid #1570ef;
        background: #1570ef;
        color: #fff;
    }
}
.task-container {
    padding: 0 16px;
}
.t2_info_box {
    margin-left: 10px;
    .t2_info_list {
        display: table;
        width: 100%;
        table-layout: fixed;
    }

    .t2_info_list > div {
        display: table-cell;
        vertical-align: top;
        padding-bottom: 10px;
    }

    .t2_info_list .t2_info_title {
        width: 20px;
        border-left: 2px #65adf9 dashed;
    }

    .t2_info_title h2 {
        margin: 0px;
        margin-left: -8px;
        height: 16px;
    }

    .t2_info_title h2 span {
        width: 16px;
        height: 16px;
        display: flex;
        border-radius: 50%;
        background-color: #1570ef;
        align-items: center;
        justify-content: center;
    }

    .t2_info_list:last-child > div {
        padding-bottom: 0;
    }

    .t2_info_con_box h2 {
        color: #1570ef;
        font-size: 16px;
        height: 30px;
        align-items: center;
        display: inline-flex;
        padding: 2px 12px;
        gap: 6px;
        border-radius: 4px;
        background: #eff8ff;
        font-weight: bold;
        cursor: pointer;

        img.zhanshouicon {
            transition: 0.3s;
        }
    }

    .t2_info_content {
        padding: 12px 0px;
    }

    .t2_info_list_over {
        .t2_info_title {
            h2 {
                span {
                    background-color: #ef6820;
                }
            }
        }

        .t2_info_con_box {
            h2 {
                background-color: #fef6ee;
                color: #ef6820;

                > img {
                    // display: none;
                }
            }
        }
    }
}

.togglticon {
    img.zhanshouicon {
        transition: 0.3s;
        transform: rotate(180deg);
    }
}

// 查看表单样式
.my_form_default_bj {
    padding: 10px;
    border-radius: 6px;
}

.dl_table_style {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    tr {
        td {
            height: 40px;
            line-height: 20px;
            empty-cells: show;
            word-break: break-all;
            padding: 4px 0px;
            color: #1d2939;
            font-size: 14px;

            &.td_width0 {
                width: 100px;
            }

            &.td_width {
                width: 110px;
            }

            &.td_width-2 {
                width: 201px;
            }

            &.td_width-3 {
                width: 304px;
            }

            &.common_label_title_r {
                text-align: right;
            }
            &.r-value-weight {
                font-weight: 500;
            }

            .red {
                color: #d92d20;
                font-size: 14px;
                margin: 0px 2px 0px 0px;
            }

            label {
                font-size: 14px;
                color: #475467;
            }
        }
    }
}

.td_block_box {
    display: flex;
    align-items: center;

    .td_block {
        display: inline-flex;
        height: 32px;
        padding: 0px 12px;
        align-items: center;
        border-radius: 6px;
        background: #eff8ff;
        color: #1570ef;
        font-size: 14px;

        &.td_color {
            color: #1d2939;
        }

        &.td_wc_color {
            color: #16b364;
            background-color: #edfcf2;
        }
        &.td_fail_color {
            color: #d00d23;
            background-color: #edfcf2;
        }
    }

    > img {
        margin: 0px 5px;
    }
}

.td_list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    height: 32px;
    padding: 0px 10px;

    span {
        font-size: 14px;
        color: #1570ef;
    }

    p {
        a {
            font-size: 14px;
            padding-left: 10px;
            cursor: pointer;

            &:hover {
                text-decoration: underline;
            }

            &.pz {
                color: #16b364;
            }

            &.yl {
                color: #1570ef;
            }

            &.sc {
                color: #ef6820;
            }
        }
    }
}

.tip_info {
    color: #98a2b3;
    font-size: 14px;
    padding: 10px 0px;
    border-top: 1px #eee solid;
    margin-top: 10px;
}

.progress_box {
    .el-progress-bar__outer {
        height: 20px !important;

        .el-progress-bar__inner {
            background: linear-gradient(270deg, #53b1fd 0%, #1570ef 100%);
        }
    }

    .progress_info {
        color: #666;
        font-size: 14px;
        padding: 10px 0px;

        a {
            color: #1570ef;
            cursor: pointer;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

// 弹窗
.task_dialog {
    .task_dialog_title {
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px #eee solid;
        margin-bottom: 25px;

        span {
            color: #1d2939;
            font-size: 16px;
            font-weight: bold;
        }

        p {
            display: flex;
            align-items: center;

            img {
                margin: 0px 0px 0px 12px;
                cursor: pointer;
            }

            i {
                font-size: 16px;
                color: #d6d6d6;
                margin-left: 12px;
                position: relative;
                top: -2px;
            }
        }
    }

    .task_dialog_content {
    }
}

// 编辑表单样式
.my_form_edit {
    .el-col {
        margin-bottom: 0px;
    }

    .el-form-item__label {
        color: #1d2939;
        font-size: 14px;
        align-items: center;
        line-height: 16px;
        text-align: right;
    }

    .el-select .el-input.is-focus .el-input__wrapper {
        box-shadow: none !important;
        border: 1px solid hsl(215, 100%, 70%);
    }

    .el-input__wrapper {
        height: 32px;
        padding: 0px 10px;
        border-radius: 6px;
        border: 1px solid #e9f2fd;
        background: #f4f9fe;
        box-shadow: none !important;

        &.is-focus {
            border: 1px solid hsl(215, 100%, 70%);
        }
    }

    .el-textarea__inner {
        padding: 0px 10px;
        border-radius: 6px;
        border: 1px solid #e9f2fd;
        background: #f4f9fe;
        box-shadow: none !important;
        color: #1d2939 !important;

        &.is-focus {
            border: 1px solid hsl(215, 100%, 70%);
        }
    }

    .el-form-item {
        margin-bottom: 18px;
    }

    .el-radio__label {
        color: #1d2939 !important;
        font-size: 14px;
    }

    .el-input__inner {
        color: #1d2939 !important;
    }

    .ts_input {
        .el-input__wrapper {
            border: 1px solid #fff;
            background: #fff;
            border-radius: 6px;
        }
    }

    .ts_select {
        .el-input__wrapper {
            border: 1px solid #fff;
            background: #fff;
            border-radius: 6px;

            input {
                color: #1570ef;
            }
        }
    }
}

.my_form_edit_label {
    .el-form-item__label {
        justify-content: flex-start;
        padding-right: 0px;
    }
}

.task_dialog_block_title {
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 0px;
    padding: 0 20px;
    span {
        color: #000;
        font-size: 14px;
    }

    a {
        color: #1570ef;
        font-size: 14px;
        cursor: pointer;
        border-radius: 4px;
        padding: 0px 10px;
        background: #f4f9fe;
    }
}

.task_dialog_block_con {
    padding: 10px;
    border-radius: 6px;
    background: #f4f9fe;
    margin-bottom: 20px;
}

.task_biao {
    font-size: 12px;
    color: #666;
    padding-top: 10px;

    b {
        color: #000;
    }
}

.errorinfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 18px;
    color: #000;
    font-weight: normal;
    margin-bottom: 10px;

    > span {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #1d2939;
        font-size: 14px;

        > img {
            margin-right: 5px;
        }
    }

    > a {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #267ef0;
        font-size: 14px;
        cursor: pointer;

        > img {
            margin-right: 5px;
        }
    }
}

.task_dialog_bigtitle {
    color: #1d2939;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
}

.sysinfocon {
    padding: 10px;
    border-radius: 6px;
    background: #f4f9fe;
    font-size: 14px;

    h4 {
        color: #1d2939;
        font-weight: normal;
        line-height: 27px;
        margin-bottom: 8px;
    }

    p {
        line-height: 24px;
        color: #1d2939;
        padding-left: 16px;
        margin-bottom: 10px;
    }

    span {
        color: #16b364;
        padding: 0px 5px;
    }

    small {
        color: #ef6820;
        font-style: normal;
        font-size: 14px;
        padding: 0px 5px;
    }

    b {
        color: #d92d20;
        font-weight: normal;
        padding: 0px 5px;
    }
}

.task_btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 16px 0px;

    a {
        display: flex;
        height: 28px;
        padding: 0px 16px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: 14px;
        background: linear-gradient(90deg, #00c8ff 0%, #1570ef 100%);
        font-size: 14px;
        color: #fff;
    }
}

.download_file {
    display: flex;
    padding: 8px 10px;
    // gap: 20px;
    margin: 5px 0px;
    border-radius: 6px;
    border: 1px solid #e9f2fd;
    overflow: auto;
    margin-bottom: 5px;
    justify-content: space-between;
    flex-wrap: wrap;
    min-height: 50px;

    .download_file_list {
        // flex: 1;
        width: 49%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        height: 32px;
        padding: 0px 20px 0px 10px;
        border-radius: 4px;
        background: #f4f9fe;
        margin: 3px 0px;

        span {
            color: #1d2939;
            font-size: 14px;
            flex: 1;
            padding: 0px 10px;
        }

        small {
            font-size: 12px;
            color: #98a2b3;
        }

        i {
            position: absolute;
            top: -8px;
            right: -8px;
            cursor: pointer;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fff;
        }
    }
}

.my_empty {
    padding: 20px 0px;

    .el-empty__image {
        img {
            opacity: 0.3;
            animation: linear 2s round infinite both;
        }

        @keyframes round {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    }

    .el-empty__description {
        margin-top: 5px;

        p {
            font-size: 14px;
        }
    }
}

.detailpzbtn {
    padding: 10px 0px;
    display: flex;

    a {
        display: flex;
        height: 28px;
        padding: 0px 12px;
        justify-content: center;
        align-items: center;
        gap: 2px;
        border-radius: 6px;
        border: 1px solid #e9f2fd;
        background: #f4f9fe;
        color: #1570ef;
        font-size: 14px;
        cursor: pointer;
        margin-right: 10px;
    }
}

.selectpzbtn {
    padding: 10px 0px;
    display: flex;

    a {
        display: flex;
        height: 28px;
        padding: 0px 12px;
        justify-content: center;
        align-items: center;
        gap: 2px;
        border-radius: 6px;
        border: 1px solid #e9f2fd;
        background: #f4f9fe;
        color: #344054;
        font-size: 14px;
        cursor: pointer;
        margin-right: 10px;
    }
}

.commonword {
    font-size: 14px;
    color: #1d2939;
    padding: 10px 0px;
    display: flex;
    align-items: center;
}

.commonbtn {
    display: flex;
    height: 28px;
    padding: 0px 16px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 6px;
    border: 1px #2e90fa solid;
    background: #fff;
    color: #1570ef;
    font-size: 14px;
    cursor: pointer;
    transition: 0.3s;

    &:hover {
        background: #2e90fa;
        color: #fff;
        transition: 0.3s;
    }
}

.commonbtn2 {
    display: flex;
    height: 28px;
    padding: 0px 8px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    border-radius: 6px;
    border: 1px #b9dbff solid;
    background: #fff;
    color: #1570ef;
    font-size: 14px;
    cursor: pointer;
    transition: 0.3s;
    margin-left: 5px;

    &:hover {
        background: #2e90fa;
        border: 1px #2e90fa solid;
        color: #fff;
        transition: 0.3s;
    }
}
.divider-line {
    width: 2px;
    height: 16px;
    background-color: #dcdfe6;
    margin: 0 10px;
}

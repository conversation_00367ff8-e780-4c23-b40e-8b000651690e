// rem 相关的混合宏和函数

// px 转 rem 函数
@function px2rem($px, $base: 16) {
  @return ($px / $base) * 1rem;
}

// rem 转 px 函数
@function rem2px($rem, $base: 16) {
  @return $rem * $base * 1px;
}

// 字体大小 rem 转换
@mixin font-size($px) {
  font-size: px2rem($px);
}

// 行高 rem 转换
@mixin line-height($px) {
  line-height: px2rem($px);
}

// 宽高 rem 转换
@mixin size($width, $height: $width) {
  width: px2rem($width);
  height: px2rem($height);
}

// 宽度 rem 转换
@mixin width($px) {
  width: px2rem($px);
}

// 高度 rem 转换
@mixin height($px) {
  height: px2rem($px);
}

// 边距 rem 转换
@mixin margin($top, $right: $top, $bottom: $top, $left: $right) {
  margin: px2rem($top) px2rem($right) px2rem($bottom) px2rem($left);
}

// 内边距 rem 转换
@mixin padding($top, $right: $top, $bottom: $top, $left: $right) {
  padding: px2rem($top) px2rem($right) px2rem($bottom) px2rem($left);
}

// 单边距 rem 转换
@mixin margin-top($px) {
  margin-top: px2rem($px);
}

@mixin margin-right($px) {
  margin-right: px2rem($px);
}

@mixin margin-bottom($px) {
  margin-bottom: px2rem($px);
}

@mixin margin-left($px) {
  margin-left: px2rem($px);
}

@mixin padding-top($px) {
  padding-top: px2rem($px);
}

@mixin padding-right($px) {
  padding-right: px2rem($px);
}

@mixin padding-bottom($px) {
  padding-bottom: px2rem($px);
}

@mixin padding-left($px) {
  padding-left: px2rem($px);
}

// 边框 rem 转换
@mixin border($width, $style: solid, $color: #ccc) {
  border: px2rem($width) $style $color;
}

@mixin border-radius($px) {
  border-radius: px2rem($px);
}

// 定位 rem 转换
@mixin position($position, $top: null, $right: null, $bottom: null, $left: null) {
  position: $position;
  @if $top != null {
    top: px2rem($top);
  }
  @if $right != null {
    right: px2rem($right);
  }
  @if $bottom != null {
    bottom: px2rem($bottom);
  }
  @if $left != null {
    left: px2rem($left);
  }
}

// 响应式断点
$breakpoints: (
  'xs': 480px,
  'sm': 768px,
  'md': 1024px,
  'lg': 1200px,
  'xl': 1440px,
  'xxl': 1920px,
  'xxxl': 2560px,
) !default;

// 响应式媒体查询混合宏
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

// 最大宽度响应式
@mixin respond-to-max($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}.";
  }
}

// 范围响应式
@mixin respond-between($min-breakpoint, $max-breakpoint) {
  @if map-has-key($breakpoints, $min-breakpoint) and map-has-key($breakpoints, $max-breakpoint) {
    @media (min-width: map-get($breakpoints, $min-breakpoint)) and (max-width: map-get($breakpoints, $max-breakpoint) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$min-breakpoint} or #{$max-breakpoint}.";
  }
}

// 文本省略号
@mixin text-ellipsis($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

// 居中对齐
@mixin center($width: null, $height: null) {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  @if $width != null {
    width: px2rem($width);
  }
  @if $height != null {
    height: px2rem($height);
  }
}

// Flex 居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 阴影 rem 转换
@mixin box-shadow($x, $y, $blur: 0, $spread: 0, $color: rgba(0, 0, 0, 0.1)) {
  box-shadow: px2rem($x) px2rem($y) px2rem($blur) px2rem($spread) $color;
}

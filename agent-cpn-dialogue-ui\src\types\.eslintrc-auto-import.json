{"globals": {"DialogueService": true, "EffectScope": true, "TestData": true, "addSystemCom": true, "addUserQuestion": true, "bgdsTest": true, "bgdwsest": true, "cjTestData": true, "computed": true, "createApp": true, "createLogger": true, "createNamespacedHelpers": true, "createStore": true, "customRef": true, "defineAsyncComponent": true, "defineComponent": true, "effectScope": true, "eventBus": true, "getCurrentInstance": true, "getCurrentScope": true, "h": true, "inject": true, "isProxy": true, "isReactive": true, "isReadonly": true, "isRef": true, "mapActions": true, "mapGetters": true, "mapMutations": true, "mapState": true, "markRaw": true, "mbwsTest": true, "nextTick": true, "onActivated": true, "onBeforeMount": true, "onBeforeRouteLeave": true, "onBeforeRouteUpdate": true, "onBeforeUnmount": true, "onBeforeUpdate": true, "onDeactivated": true, "onErrorCaptured": true, "onMounted": true, "onRenderTracked": true, "onRenderTriggered": true, "onScopeDispose": true, "onServerPrefetch": true, "onUnmounted": true, "onUpdated": true, "provide": true, "reactive": true, "readonly": true, "ref": true, "resolveComponent": true, "resolveDirective": true, "shallowReactive": true, "shallowReadonly": true, "shallowRef": true, "toRaw": true, "toRef": true, "toRefs": true, "triggerRef": true, "unref": true, "useAttrs": true, "useCssModule": true, "useCssVars": true, "useDialogInput": true, "useDialogueHandler": true, "useDialogueHistory": true, "useLink": true, "useRoute": true, "useRouter": true, "useSlots": true, "useStore": true, "useSystemResponse": true, "watch": true, "watchEffect": true, "watchPostEffect": true, "watchSyncEffect": true, "zbcxTest": true}}
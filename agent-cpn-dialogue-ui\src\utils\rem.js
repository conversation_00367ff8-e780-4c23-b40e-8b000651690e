// rem 自适应设置
class RemAdapter {
  constructor(options = {}) {
    this.designWidth = options.designWidth || 1920; // 设计稿宽度
    this.baseSize = options.baseSize || 16; // 基准字体大小
    this.minSize = options.minSize || 12; // 最小字体大小
    this.maxSize = options.maxSize || 20; // 最大字体大小
    this.minWidth = options.minWidth || 1200; // 最小适配宽度
    this.maxWidth = options.maxWidth || 2560; // 最大适配宽度
    
    this.init();
  }

  init() {
    this.setRem();
    window.addEventListener('resize', this.debounce(this.setRem.bind(this), 100));
    window.addEventListener('orientationchange', this.debounce(this.setRem.bind(this), 100));
    
    // 监听 DOM 加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', this.setRem.bind(this));
    }
  }

  setRem() {
    const clientWidth = document.documentElement.clientWidth || document.body.clientWidth;
    
    // 限制适配范围
    const width = Math.max(this.minWidth, Math.min(clientWidth, this.maxWidth));
    
    // 计算字体大小
    let fontSize = (width / this.designWidth) * this.baseSize;
    
    // 限制字体大小范围
    fontSize = Math.max(this.minSize, Math.min(fontSize, this.maxSize));
    
    // 设置根元素字体大小
    document.documentElement.style.fontSize = fontSize + 'px';
    
    // 设置 CSS 变量供其他地方使用
    document.documentElement.style.setProperty('--rem-base', fontSize + 'px');
    document.documentElement.style.setProperty('--screen-width', width + 'px');
    document.documentElement.style.setProperty('--design-width', this.designWidth + 'px');
    document.documentElement.style.setProperty('--scale-ratio', fontSize / this.baseSize);
    
    // 触发自定义事件，通知其他组件 rem 基准已更新
    window.dispatchEvent(new CustomEvent('remUpdated', {
      detail: {
        fontSize,
        width,
        ratio: fontSize / this.baseSize
      }
    }));
  }

  // 防抖函数
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // 手动更新
  update() {
    this.setRem();
  }

  // 获取当前 rem 基准值
  getRemBase() {
    return parseFloat(document.documentElement.style.fontSize) || this.baseSize;
  }

  // 获取当前缩放比例
  getScaleRatio() {
    return this.getRemBase() / this.baseSize;
  }

  // px 转 rem
  px2rem(px) {
    return px / this.getRemBase();
  }

  // rem 转 px
  rem2px(rem) {
    return rem * this.getRemBase();
  }

  // 销毁实例
  destroy() {
    window.removeEventListener('resize', this.debounce(this.setRem.bind(this), 100));
    window.removeEventListener('orientationchange', this.debounce(this.setRem.bind(this), 100));
  }
}

// 创建实例并导出
const remAdapter = new RemAdapter({
  designWidth: 1920, // 你的设计稿宽度，可以根据实际情况调整
  baseSize: 16,      // 基准字体大小
  minSize: 12,       // 最小字体大小
  maxSize: 20,       // 最大字体大小
  minWidth: 1200,    // 最小适配宽度
  maxWidth: 2560,    // 最大适配宽度
});

// 导出实例和类
export default remAdapter;
export { RemAdapter };

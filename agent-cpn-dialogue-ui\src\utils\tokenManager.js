import { getToken, setToken, removeToken } from '@/utils/auth';
import { getStore, setStore } from '@/utils/store';
import dayjs from 'dayjs';

/**
 * @description Token管理器
 */
export class TokenManager {
    /**
     * @description 检查token是否有效
     */
    static isTokenValid() {
        const token = getToken();
        return !!token;
    }

    /**
     * @description 检查token是否即将过期
     */
    static isTokenExpiringSoon() {
        const tokenData = getStore({ name: 'token' }) || {};
        if (!tokenData || !tokenData.datetime) {
            return false;
        }

        const lastTime = dayjs(tokenData.datetime);
        const now = dayjs();
        const diff = now.diff(lastTime, 'seconds');

        // 根据实际token有效期调整
        const TOKEN_EXPIRE_SECONDS = 60 * 60 * 2; // 2小时
        const REFRESH_THRESHOLD = 60 * 10; // 剩10分钟

        return diff >= TOKEN_EXPIRE_SECONDS - REFRESH_THRESHOLD;
    }

    /**
     * @description 更新token存储时间
     */
    static updateTokenTime(token) {
        if (token) {
            setStore({
                name: 'token',
                content: {
                    token: token,
                    datetime: dayjs().format(),
                },
            });
        }
    }

    /**
     * @description 清除token相关数据
     */
    static clearTokenData() {
        removeToken();
        // 清除store中的token数据
        setStore({ name: 'token', content: null });
    }

    /**
     * @description 获取token过期时间（秒）
     */
    static getTokenExpireTime() {
        const tokenData = getStore({ name: 'token' }) || {};
        if (!tokenData || !tokenData.datetime) {
            return 0;
        }

        const lastTime = dayjs(tokenData.datetime);
        const now = dayjs();
        const diff = now.diff(lastTime, 'seconds');

        const TOKEN_EXPIRE_SECONDS = 60 * 60 * 2; // 2小时
        return Math.max(0, TOKEN_EXPIRE_SECONDS - diff);
    }
}

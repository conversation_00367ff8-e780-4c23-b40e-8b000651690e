// import { Client } from '@stomp/stompjs';
// import SockJS from 'sockjs-client/dist/sockjs.min.js'
//
// let stompClient = null;
//
// export const connectWebSocket = (sessionId, onMessageReceived) => {
//     const socket = new SockJS('http://10.232.241.153:2888/api/data-component-dialogue/websocket');
//     stompClient = new Client({
//         webSocketFactory: () => socket,
//         reconnectDelay: 5000,
//         heartbeatIncoming: 4000,
//         heartbeatOutgoing: 4000,
//     });
//
//     stompClient.onConnect = () => {
//         console.log('Connected to WebSocket');
//         stompClient.subscribe(`/topic/queueName`, (message) => {
//             console.log('Received: ' + JSON.parse(message.body).content); // 处理接收到的消息
//         });
//     };
//
//     stompClient.activate();
// };
//
// export const disconnectWebSocket = () => {
//     console.log('Disconnecting WebSocket');
//     if (stompClient) {
//         stompClient.deactivate();
//     }
// };

// src/utils/websocket.js
import SockJS from 'sockjs-client/dist/sockjs.min.js';
import Stomp from 'stompjs';

// WebSocket客户端
let stompClient = null;
// 连接状态
let isConnected = false;
// 自动重连选项
let reconnectOptions = {
    enabled: false,
    maxAttempts: 5,
    delay: 3000,
};

/**
 * 连接WebSocket服务器
 * @param {string} url - WebSocket服务器地址
 * @param {Function} callback - 消息回调函数
 * @param {Object} options - 连接选项
 */
export function connect(url,messageContent, callback, options = {}) {
    const socket = new SockJS(url);
    stompClient = Stomp.over(socket);

    // 禁用STOMP日志
    stompClient.debug = null;

    // 设置重连选项
    if (options.reconnect) {
        reconnectOptions = {
            enabled: true,
            maxAttempts: options.maxAttempts || 5,
            delay: options.reconnectDelay || 3000,
        };
    }

    // 连接事件处理
    stompClient.connect(
        {},
        frame => {
            console.log('WebSocket连接成功');
            isConnected = true;

            // 订阅消息
            // stompClient.subscribe('/topic/messages', message => {
            //     callback(message.body);
            // });

            let userId = messageContent.userId;
            let dialogueId = messageContent.dialogueId;
            // 订阅消息
            stompClient.subscribe('/user/'+userId+'/'+dialogueId+'/queue/messages', message => {
                callback(message.body);
            });

            // 如果有连接成功回调，执行
            if (options.onConnect) {
                options.onConnect(frame);
            }
        },
        error => {
            console.error('WebSocket连接错误:', error);
            isConnected = false;

            // 如果有错误回调，执行
            if (options.onError) {
                options.onError(error);
            }

            // 自动重连逻辑可以添加在这里
        }
    );
}

/**
 * 断开WebSocket连接
 */
export function disconnect() {
    if (stompClient) {
        stompClient.disconnect(() => {
            console.log('WebSocket已断开连接');
            isConnected = false;
        });
    }
}

/**
 * 发送消息到WebSocket服务器
 * @param {string} message - 要发送的消息
 * @param {string} destination - 目标地址，默认为'/app/send'
 * @returns {boolean} 是否发送成功
 */
export function sendMessage(message, destination = '/app/send') {
    if (!stompClient || !isConnected) {
        console.error('WebSocket未连接，无法发送消息');
        return false;
    }

    try {
        stompClient.send(destination, {}, message);
        return true;
    } catch (error) {
        console.error('发送消息失败:', error);
        return false;
    }
}

/**
 * 获取当前连接状态
 * @returns {boolean} 是否已连接
 */
export function isWebSocketConnected() {
    return isConnected;
}

/**
 * 发送心跳消息
 * @returns {boolean} 是否发送成功
 */
export function sendHeartbeat() {
    return sendMessage(JSON.stringify({ type: 'heartbeat', timestamp: Date.now() }));
}

<template>
    <basic-container>
        <div>{{ replyContent }}</div>
        <div>收到消息: {{ receivedMsg }}</div>
        <el-button @click="getFlowOutput">GET流式输出</el-button>
        <el-button @click="postFlowOutput">POST流式输出</el-button>
    </basic-container>
</template>

<script>
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { getToken } from '@/utils/auth';
import { connect, disconnect } from '@/utils/websocket';

export default {
    name: 'wel',
    data() {
        return {
            replyContent: '',
            blockingResult: '',
            streamOutput: '',
            user: 'user123', // 替换为实际用户标识
            inputMsg: '',
            receivedMsg: '',
        };
    },
    created() {
        // this.data();
    },
    mounted() {},
    beforeUnmount() {
        disconnect();
    },
    methods: {
        getFlowOutput(params) {
            const eventSource = new EventSource('/api/data-component-dialogue/noauth/dialogue/data');
            let flagFlow = 1;
            // 监听消息事件
            eventSource.onmessage = event => {
                const json = event.data;
                this.replyContent += json;
            };
        },
        postFlowOutput() {
            connect('http://10.232.102.49:2888/api/data-component-dialogue/ws', msg => {
                if (msg.includes('工具执行完毕')) {
                    console.log('工具执行完毕,关闭连接...');
                    disconnect();
                } else {
                    this.receivedMsg = msg;
                }
            });
            //流式输出
            let flagFlow = 1;
            let data = {
                isDeepThinking: '1',
                msg: '帮我将A数据库的a1表全量采集到B数据库的b1表',
            };
            fetchEventSource('/api/data-component-dialogue/noauth/dialogue/reply', {
                method: 'post',
                headers: {
                    'Ark-Auth': 'bearer ' + getToken(),
                    Accept: 'text/event-stream;charset=UTF-8',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
                openWhenHidden: true,
                withCredentials: true,
                onmessage: event => {
                    // 处理接收到的消息
                    const json = event.data;
                    console.log(json);
                    this.replyContent += json;
                },
                onclose: () => {
                    console.log('Connection closed');
                },
                onerror: err => {
                    console.error('SSE 错误:', err); // 关闭连接
                },
            });
        },

        startBlockingTask() {
            this.$stompClient.send('/app/blocking-task', {}, '请求数据');
        },
        startStreamTask() {
            this.$stompClient.send('/app/stream-task', {}, '开始流处理');
        },
    },
};
</script>

<style></style>

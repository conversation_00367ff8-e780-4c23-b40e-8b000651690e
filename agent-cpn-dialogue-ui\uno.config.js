import { defineConfig, transformerVariantGroup } from 'unocss';
import presetUno from '@unocss/preset-uno';
import presetAttributify from '@unocss/preset-attributify';
import presetIcons from '@unocss/preset-icons';

export default defineConfig({
    // UnoCSS 选项
    presets: [
        // 使用内置预设
        presetUno(),
        // presetMini(),
        presetAttributify(),
        presetIcons(),
    ],
    transformers: [transformerVariantGroup()],
    shortcuts: {
        'flex-between': 'flex items-center justify-between',
        'flex-around': 'flex items-center justify-around',
        'flex-col-center': 'flex flex-col items-center justify-center',
        btn: 'px-4 py-1 rounded inline-block bg-teal-600 text-white cursor-pointer hover:bg-teal-700 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50',
        'icon-btn':
            'text-[0.9em] inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-teal-600 !outline-none',
        'text-ellipsis': 'truncate overflow-hidden whitespace-nowrap',
        card: 'bg-white rounded-lg shadow-md p-4',
        'hover-card': 'hover:shadow-lg transition-shadow duration-300',
        input: 'border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-teal-500',
        'btn-primary': 'bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors',
        'btn-secondary': 'bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 transition-colors',
        'btn-danger': 'bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors',
        'm-0-auto': 'm-0 ma', // margin: 0 auto
        'wh-full': 'w-full h-full', // width: 100%, height: 100%
        'flex-center': 'flex justify-center items-center', // flex布局居中
        'flex-x-center': 'flex justify-center', // flex布局：主轴居中
        'flex-y-center': 'flex items-center', // flex布局：交叉轴居中
        'text-overflow': 'overflow-hidden whitespace-nowrap text-ellipsis', // 文本溢出显示省略号
        'text-break': 'whitespace-normal break-all break-words', // 文本溢出换行

        // rem 相关工具类
        'text-rem-12': 'text-[0.75rem]',
        'text-rem-14': 'text-[0.875rem]',
        'text-rem-16': 'text-[1rem]',
        'text-rem-18': 'text-[1.125rem]',
        'text-rem-20': 'text-[1.25rem]',
        'text-rem-24': 'text-[1.5rem]',
        'text-rem-28': 'text-[1.75rem]',
        'text-rem-32': 'text-[2rem]',

        // 间距 rem 工具类
        'p-rem-4': 'p-[0.25rem]',
        'p-rem-8': 'p-[0.5rem]',
        'p-rem-12': 'p-[0.75rem]',
        'p-rem-16': 'p-[1rem]',
        'p-rem-20': 'p-[1.25rem]',
        'p-rem-24': 'p-[1.5rem]',
        'p-rem-32': 'p-[2rem]',

        'm-rem-4': 'm-[0.25rem]',
        'm-rem-8': 'm-[0.5rem]',
        'm-rem-12': 'm-[0.75rem]',
        'm-rem-16': 'm-[1rem]',
        'm-rem-20': 'm-[1.25rem]',
        'm-rem-24': 'm-[1.5rem]',
        'm-rem-32': 'm-[2rem]',

        // 宽高 rem 工具类
        'w-rem-full': 'w-[100%]',
        'h-rem-full': 'h-[100%]',
        'size-rem-16': 'w-[1rem] h-[1rem]',
        'size-rem-24': 'w-[1.5rem] h-[1.5rem]',
        'size-rem-32': 'w-[2rem] h-[2rem]',
    },
    rules: [
        [
            /^bd-(\d+)-([a-z]+)-(#[\da-fA-F]{3,6}|(?:rgba?|hsl)a?$.*?$)$/,
            ([_, width, style, color]) => ({
                'border-width': `${width}px`,
                'border-style': style,
                'border-color': color,
            }),
        ],
        [
            /^text-overflow-(\d+)$/,
            ([_, d]) =>
                d === '1'
                    ? { 'white-space': 'nowrap', overflow: 'hidden', 'text-overflow': 'ellipsis' }
                    : {
                          display: '-webkit-box',
                          '-webkit-line-clamp': d,
                          'line-clamp': d,
                          '-webkit-box-orient': 'vertical',
                          'box-orient': 'vertical',
                          overflow: 'hidden',
                          'text-overflow': 'ellipsis',
                      },
        ],
        // 动态 rem 规则
        [/^text-rem-(\d+)$/, ([, d]) => ({ 'font-size': `${d / 16}rem` })],
        [/^w-rem-(\d+)$/, ([, d]) => ({ width: `${d / 16}rem` })],
        [/^h-rem-(\d+)$/, ([, d]) => ({ height: `${d / 16}rem` })],
        [/^p-rem-(\d+)$/, ([, d]) => ({ padding: `${d / 16}rem` })],
        [/^px-rem-(\d+)$/, ([, d]) => ({ 'padding-left': `${d / 16}rem`, 'padding-right': `${d / 16}rem` })],
        [/^py-rem-(\d+)$/, ([, d]) => ({ 'padding-top': `${d / 16}rem`, 'padding-bottom': `${d / 16}rem` })],
        [/^pt-rem-(\d+)$/, ([, d]) => ({ 'padding-top': `${d / 16}rem` })],
        [/^pr-rem-(\d+)$/, ([, d]) => ({ 'padding-right': `${d / 16}rem` })],
        [/^pb-rem-(\d+)$/, ([, d]) => ({ 'padding-bottom': `${d / 16}rem` })],
        [/^pl-rem-(\d+)$/, ([, d]) => ({ 'padding-left': `${d / 16}rem` })],
        [/^m-rem-(\d+)$/, ([, d]) => ({ margin: `${d / 16}rem` })],
        [/^mx-rem-(\d+)$/, ([, d]) => ({ 'margin-left': `${d / 16}rem`, 'margin-right': `${d / 16}rem` })],
        [/^my-rem-(\d+)$/, ([, d]) => ({ 'margin-top': `${d / 16}rem`, 'margin-bottom': `${d / 16}rem` })],
        [/^mt-rem-(\d+)$/, ([, d]) => ({ 'margin-top': `${d / 16}rem` })],
        [/^mr-rem-(\d+)$/, ([, d]) => ({ 'margin-right': `${d / 16}rem` })],
        [/^mb-rem-(\d+)$/, ([, d]) => ({ 'margin-bottom': `${d / 16}rem` })],
        [/^ml-rem-(\d+)$/, ([, d]) => ({ 'margin-left': `${d / 16}rem` })],
        [/^gap-rem-(\d+)$/, ([, d]) => ({ gap: `${d / 16}rem` })],
        [/^rounded-rem-(\d+)$/, ([, d]) => ({ 'border-radius': `${d / 16}rem` })],
        [/^top-rem-(\d+)$/, ([, d]) => ({ top: `${d / 16}rem` })],
        [/^right-rem-(\d+)$/, ([, d]) => ({ right: `${d / 16}rem` })],
        [/^bottom-rem-(\d+)$/, ([, d]) => ({ bottom: `${d / 16}rem` })],
        [/^left-rem-(\d+)$/, ([, d]) => ({ left: `${d / 16}rem` })],
    ],
    theme: {
        colors: {
            // 可以在这里扩展颜色
            white: {
                50: '#f9fafb',
            },
            black: {
                50: '#000',
            },
        },
    },
});

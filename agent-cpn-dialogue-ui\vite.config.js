import { defineConfig, loadEnv } from 'vite';
import path from 'path';
import { fileURLToPath } from 'node:url';
import UnoCSS from 'unocss/vite';

import createVitePlugins from './vite/plugins/index.js';
import { AntDesignVueResolver, ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';

/**
 * 获取当前文件路径和目录（ESM 方式）
 */
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Vite 配置导出
 */
export default defineConfig(({ mode, command }) => {
    const env = loadEnv(mode, process.cwd());
    const { VITE_APP_BASE, VITE_APP_TARGET } = env;
    // 微服务下 网关的地址
    const target = VITE_APP_TARGET || 'http://10.232.206.24:30000';
    // const target = 'http://10.232.105.68:30000';
    // const target = 'http://10.233.206.22:30000';
    return {
        // base: VITE_APP_BASE,
        base: '/dialogue',
        server: {
            port: 2888,
            proxy: {
                // bx原来的系统管理请求没有做修改, 都指向系统模块
                // 部署的时候，需要在ng配置代理到data-component-system
                '^/api/blade-*': {
                    target: target,
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/api/, `/data-component-dialogue`),
                },
                // single模式下需要把所有data-component的服务指向同一个后台服务, cloud 模式下需要注释掉
                '^/api/data-component-util': {
                    target: target,
                    // 修改请求头中的 Host 字段
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/api\/data-component-[\da-zA-Z]*/, '/data-component-util'),
                    // 启用 WebSocket 代理支持需要使用 WebSocket 进行实时通信时必须设置
                    ws: true,
                },
                '^/api/data-component-catalog': {
                    target: target,
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/api\/data-component-[\da-zA-Z]*/, '/data-component-catalog'),
                    ws: true,
                },
                '^/api/data-component-service': {
                    target: target,
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/api\/data-component-[\da-zA-Z]*/, '/data-component-service'),
                    ws: true,
                },
                '^/api/data-component-*': {
                    target: target,
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/api\/data-component-[\da-zA-Z]*/, '/data-component-dialogue'),
                    ws: true,
                },
                // cloud 模式下,除了系统模块,其他都要把api替换为空字符串
                // 部署的时候，需要在ng配置
                '/api': {
                    target: target,
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/api/, ''),
                    ws: true,
                },
            },
        },
        resolve: {
            alias: {
                '~': path.resolve(__dirname, './'),
                '@': path.resolve(__dirname, './src'),
                '@dialogue': path.resolve(__dirname, './src/components/dialogue'),
                styles: path.resolve(__dirname, './src/styles'),
                utils: path.resolve(__dirname, './src/utils'),
            },
        },
        // 添加静态资源处理配置
        assetsInclude: ['**/*.docx', '**/*.xlsx', '**/*.doc', '**/*.xls'],
        css: {
            // PostCSS 配置
            postcss: './postcss.config.js',
            // 向每个 vue SCSS 文件中自动注入一些公共的 CSS 代码
            preprocessorOptions: {
                scss: {
                    additionalData: `@use "@/styles/dialogue/colors.scss" as *;`,
                },
            },
            // 禁用 Sass 弃用警告
            devSourcemap: true,
            sassOptions: {
                quietDeps: true,
                logger: {
                    warn: () => {},
                    debug: () => {},
                },
            },
        },
        plugins: [
            createVitePlugins(env, command === 'build'),
            UnoCSS(),
            AutoImport({
                dts: false,
                imports: [
                    'vue',
                    'vue-router',
                    'vuex',
                    // 添加eventBus的自动导入
                    {
                        '@/utils/bus.js': [['default', 'eventBus']],
                    },
                ],
                // 自动导入hooks目录下的所有函数
                dirs: ['src/components/dialogue/hooks'],
                resolvers: [],
                // 生成ESLint配置
                eslintrc: {
                    enabled: true,
                    filepath: 'src/types/.eslintrc-auto-import.json', // 生成的文件路径
                },
            }),
            Components({
                dts: false,
                resolvers: [AntDesignVueResolver(), ElementPlusResolver()],
                dirs: ['src/components/dialogue'],
                deep: true,
                directoryAsNamespace: true, // 子目录命名空间前缀
            }),
        ],
    };
});

import compression from 'vite-plugin-compression';

export default function createCompression(env) {
    const { VITE_BUILD_COMPRESS } = env;
    const plugin = [];
    if (VITE_BUILD_COMPRESS) {
        const compressList = VITE_BUILD_COMPRESS.split(',');
        if (compressList.includes('gzip')) {
            plugin.push(
                compression({
                    ext: '.gz',
                    threshold: 1024 * 50,
                    deleteOriginFile: false,
                    algorithm: 'gzip', 
                })
            );
        }
        if (compressList.includes('brotli')) {
            plugin.push(
                compression({
                    ext: '.br',
                    threshold: 1024 * 50,
                    deleteOriginFile: false,
                    algorithm: 'brotliCompress',
                })
            );
        }
    }
    return plugin;
}

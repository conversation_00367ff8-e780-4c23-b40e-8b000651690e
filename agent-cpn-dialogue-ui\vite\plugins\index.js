import vue from '@vitejs/plugin-vue';
// 编译阶段eslint
import viteEslint from 'vite-plugin-eslint';

import createCompression from './compression';
import createSetupMock from './setup-mock';

export default function createVitePlugins(viteEnv, isBuild = false) {
    const vitePlugins = [vue()];
    // 编译时ESLint
    // vitePlugins.push(
    //     viteEslint({
    //         failOnError: false,
    //         include: ['src/**/*.js', 'src/**/*.vue', 'src/**/*.ts', 'src/**/*.jsx', 'src/**/*.tsx'],
    //         cache: false,
    //     })
    // );
    vitePlugins.push(createSetupMock());
    isBuild && vitePlugins.push(...createCompression(viteEnv));
    return vitePlugins;
}
